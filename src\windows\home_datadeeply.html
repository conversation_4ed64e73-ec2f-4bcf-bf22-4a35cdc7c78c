<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Home - Data Deeply</title>

		<!-- Brython -->
		<script type="text/javascript" src="/lib/brython/brython.js"></script>
		<script type="text/javascript" src="/lib/brython/brython_stdlib.js"></script>

		<!-- Tabulator -->
		<script type="text/javascript" src="/lib/tabulator/tabulator_custom.js"></script>
		<link rel="stylesheet" href="/lib/tabulator/tabulator_bulma.min.css" />

		<!-- 'deeply' startup code for every page -->
		<script type="text/javascript" src="/common/init_this_page.js"></script>

		<!-- imports -->
		<script src="/common/tauri.py" type="text/python" id="tauri"></script>
		<script src="/common/file_sqlite.py" type="text/python" id="file_sqlite"></script>
		<script src="/common/table_utils.py" type="text/python" id="table_utils"></script>

		<script src="/windows/open_file.py" type="text/python" id="open_file"></script>
		<script src="/windows/shortcuts.py" type="text/python" id="shortcuts"></script>

		<!-- styles -->
		<link rel="stylesheet" href="/common/makedeeply.css" />
		<link rel="stylesheet" href="editors.css" />
	</head>
	<body id="home-page">
		<h1>Data Deeply</h1>
		<button class="open">Open Data File…</button>
		<button class="history">File History</button>
		<button class="edits if_paid_or_trial can_view_edits_table">Edits</button>
		<button class="help">Help</button>

		<script src="home.py" type="text/python"></script>
	</body>
</html>
