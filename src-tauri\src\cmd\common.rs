use crate::{global_config::AppConfig, utils};
use sqlx::{Pool, Row, Sqlite};
use tauri::{Manager, State};

#[tauri::command]
pub fn get_app_config(app_config: tauri::State<'_, AppConfig>) -> AppConfig {
	let config_ref = app_config.inner();
	config_ref.clone()
}

#[tauri::command]
pub fn get_product_name(app_handle: tauri::AppHandle) -> String {
	// Mixed Case with space, e.g. Data Deeply, Now Deeply
	app_handle.config().product_name.clone().unwrap()  // productName in tauri.conf.json
}

#[tauri::command]
pub fn get_product_id(app_handle: tauri::AppHandle) -> String {
	// lowercase no space, e.g. datadeeply, nowdeeply
	get_product_name(app_handle)
		.to_lowercase()
		.replace(" ", "")
}

#[tauri::command]
pub fn get_api_port(app_handle: tauri::AppHandle) -> i32 {
	// this value is hardcoded in the relevant /python_api/{product_id}.py file
	// that file isn't part of the app ==> can't access this port directly ==> must be hardcoded there
	// that file serves as documentation for the port number, though could still show in settings
	// ... maybe with error checking to ensure there's no port conflict!

	// (no installer yet ==> users should include that in their project)

	// future TBD: let user change the port in case there's a conflict
    let product_name = get_product_name(app_handle);

    match product_name.as_str() {
        "Now Deeply" => 44985,
        "Make Deeply" => 44986,
        "Data Deeply" => 44987,
        "Edit Deeply" => 44988,
        _ => 44984, // Default fallback
    }
}

/// Close webview window
/// Current global tauri api have bug which closes main window if we use window.getCurrent().close()
#[tauri::command]
pub fn close_window(app_handle: tauri::AppHandle, label: String) {
	if let Some(webview) = app_handle.get_webview_window(&label) {
		webview.close().unwrap();
	}
}

#[tauri::command]
pub fn is_mac_os() -> bool {
	cfg!(target_os = "macos")
}

#[tauri::command]
pub fn get_os() -> &'static str {
	// returns macos, windows, linux
	std::env::consts::OS
}

#[tauri::command]
pub fn get_home_dir() -> String {
	utils::home_dir().to_str().unwrap().into()
}

#[tauri::command]
pub async fn test(pool: State<'_, Pool<Sqlite>>) -> Result<(), String> {
	let mut conn = pool.acquire().await.unwrap();

	// Insert
	let query = sqlx::query(
		"INSERT INTO actions (action, details, count, date_saved) VALUES ('like', '1234', 1, '2024-05-03 12:00:00');",
	);
	query.execute(&mut *conn).await.unwrap();

	// Query
	let query = sqlx::query("SELECT * FROM actions");
	let rows = query.fetch_all(&mut *conn).await.unwrap();
	for row in rows {
		let value: Result<String, _> = row.try_get("action");
		println!("{:?}", value.unwrap());
	}
	Ok(())
}

/// Escape label name for tauri
/// https://github.com/tauri-apps/tauri/blob/e4292ce7be198f6265dde9b6ae54e73b7c41e7da/core/tauri-runtime/src/window.rs#L237-L241
pub fn escape_window_label(label: &str) -> String {
	label
		.chars()
		.filter(|&c| char::is_alphanumeric(c) || c == '-' || c == '/' || c == ':' || c == '_')
		.collect()
}
