use chrono::{DateTime, NaiveDateTime, ParseError, Utc};
use eyre::Result;
use rand::distributions::Alphanumeric;
use rand::Rng;
use serde_json::Value;
use std::env;
use std::path::PathBuf;
use std::sync::mpsc::{channel, Receiver, Sender};
use std::time::Duration;
use std::{sync::mpsc::TryRecvError, time::Instant};
use tauri::{Listener, Webview};

/// A macro which log result if it contains error
/// Usage: log_result!(some_call())
#[allow(unused)]
macro_rules! log_error {
    ($result:expr) => {
        match $result {
            Ok(_) => (),
            Err(e) => {
                println!("Error occurred: {:?}", e);
            }
        }
    };
}
#[allow(unused)]
pub(crate) use log_error;

pub fn parse_sqlite_date(date: &str) -> Result<DateTime<Utc>, ParseError> {
    // Attempt to parse the date string into a NaiveDateTime
    let naive_datetime = NaiveDateTime::parse_from_str(date, "%Y-%m-%d %H:%MZ")?;

    // Convert the NaiveDateTime to DateTime<Utc>
    let datetime_utc = DateTime::from_naive_utc_and_offset(naive_datetime, Utc);

    Ok(datetime_utc)
}

/// Eval command in webview and return the JS result by emitting event
/// Must emit event back in the script
pub async fn eval_with_event(
    webview: &Webview,
    event_name: &str,
    script: &str,
    timeout: Duration,
) -> Result<Value> {
    let (sender, receiver): (Sender<String>, Receiver<String>) = channel();
    let id = webview.listen(event_name, move |event| {
        let value = event.payload().to_string();
        let result = sender.send(value);
        if let Err(err) = result {
            log::error!("failed to get result from callback {:?}", err);
        }
    });
    let result = webview.eval(script);
    if let Err(err) = result {
        log::error!("failed to eval {:?}", err);
    }

    let mut final_value: Value = serde_json::Value::Object(Default::default());
    let start = Instant::now();
    while start.elapsed() < timeout {
        match receiver.try_recv() {
            Ok(json_value) => {
                // HTML received, break out of the loop
                let value: Result<Value, serde_json::Error> = serde_json::from_str(&json_value);
                if let Ok(value) = value {
                    final_value = value;
                }
                break;
            }
            Err(TryRecvError::Empty) => {
                // No HTML received yet, sleep for a bit before trying again
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
            Err(TryRecvError::Disconnected) => {
                // The sender has disconnected, handle the error
                log::error!("sender disconnected while waiting for eval result");
                break;
            }
        }
    }
    // here wait for payload to receive and then unlisten
    webview.unlisten(id);
    Ok(final_value)
}

pub fn home_dir() -> PathBuf {
    #[cfg(windows)]
    {
        env::var("USERPROFILE").unwrap_or_default().into()
    }

    #[cfg(unix)]
    {
        env::var("HOME").unwrap_or_default().into()
    }
}

pub fn random_string(length: usize) -> String {
    rand::thread_rng()
        .sample_iter(&Alphanumeric)
        .take(length)
        .map(char::from)
        .collect()
}
