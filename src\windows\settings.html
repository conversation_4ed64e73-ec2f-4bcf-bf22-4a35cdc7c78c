<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Settings</title>

		<link rel="stylesheet" href="/common/makedeeply.css" />
		<link rel="stylesheet" href="/windows/settings.css" />
	</head>

	<body id="settings-page" class="dialog">
		<div>
			<section class="customize">
				<h1>Customize</h1>
				<p>You may add your own <code>css</code> and/or <code>js</code> files.</p>

				<div class="stacked wide">
					<button id="open-folder">Open Settings Folder</button>
					<button id="reload-app" class="secondary">Reload App</button>
				</div>

				<div class="status success"></div>
			</section>
		</div>

		<script type="module" src="/windows/settings.js"></script>
	</body>
</html>
