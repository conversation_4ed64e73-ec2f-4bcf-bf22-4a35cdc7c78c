use lazy_static::lazy_static;
use std::collections::HashMap;

/************************** File extensions **************************/
// Rust: no leading dot (unlike Python)
pub const SQLITE_EXTENSIONS: &[&str] = &["sqlite", "db", "sqlite3"];

/************************** Paths **************************/
pub const LICENSE_PATH: &str = "./license.json";

/************************** Windows **************************/
pub const WINDOW_STATE_DENYLIST: &[&str] = &[];
pub const WINDOW_STATE_FILENAME: &str = "window-state.json";

/************************** Encryption **************************/
pub const TRIAL_DATE_ENC_KEY: &[u8; 16] = b"2sRz!tkxSA2G%Kaa";
pub const TRIAL_DATE_ENC_IV: &[u8; 16] = b"This is 16 bytes";

/************************** Windows size **************************/
// TBD: find this in code before 2025-07-07 and restore if useful
// pub const MIN_WINDOW_WIDTH: f64 = 250.0;
// pub const MIN_WINDOW_HEIGHT: f64 = 250.0;
lazy_static! {
    pub static ref WINDOW_SIZE_MAP: HashMap<&'static str, (f64, f64)> = {
        let items: &[(&'static str, (f64, f64))] = &[
            ("main", (1440.0, 990.0)),
            ("help", (770.0, 990.0)),
            ("buy", (500.0, 990.0)),
            ("settings", (440.0, 640.0)),
            ("trial", (280.0, 280.0)),
        ];
        items.iter().cloned().collect()
    };
    pub static ref TRIAL_INFO_ENCRYPTION: bool = {
        // compile time macros, the first is only in dev mode
        #[cfg(debug_assertions)]
        {
            std::env::var("TRIAL_INFO_ENCRYPTION").unwrap_or_default() != "0"
        }
        #[cfg(not(debug_assertions))]
        {
            true
        }
    };
}
