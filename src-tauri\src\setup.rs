use tauri::{async_runtime::spawn, Manager, State};

use crate::{
    database, http_api, trial, Args,
    edit1::{open_unsaved_autosave, AppState},  // Edit Deeply TBD: how manage?
    global_config::{AppConfig, Plan},
    window::{main_window, menu, numbering::NumberedWindows},
};
// FYI: the above is probably Rust standard format

pub fn setup(app: &tauri::App, _args: Args) -> Result<(), Box<dyn std::error::Error>> {
    // log webview version
    let webview_version = tauri::webview_version().unwrap();
    log::info!("Using webview version {webview_version}");

    let app_handle_clone = app.app_handle().to_owned();
	let port = crate::common::get_api_port(app_handle_clone.clone());

	log::debug!("about to spawn API on port {}", port);
	spawn(http_api::run(app_handle_clone.clone(), port));

    // add global state
    let app_config = AppConfig::try_create(app.app_handle())?;
    app.manage(app_config.clone());

    let numbered_windows = NumberedWindows::new();
    app.manage(tokio::sync::Mutex::new(numbered_windows));

	// documentation TBD: what is this for? (probably something in Edit Deeply?)
    app.manage(AppState::default());
    let handle = app.handle();
    let state: State<AppState> = handle.state();
    if let Some(w) = handle.get_webview_window("main") {
        *state.active_window.lock().unwrap() = Some(w);
    }
    // menubar
    let app_handle = app.app_handle();
    menu::create_menu(app_handle.to_owned(), menu::MenuType::FileData, true).unwrap();

    // connect database
    // allow receive path from arguments
    let app_handle = app.app_handle();
    spawn(database::add_pool_state(app_handle.clone()));

    // main window -- TBD: compare to Make Deeply code
    tokio::runtime::Runtime::new()?.block_on(main_window::create(app))?;

    log::debug!("plan: {:?}", app_config.plan);
    if app_config.plan == Plan::Trial {
        let app_handle = app.app_handle().to_owned();
        log::debug!("calling show popup");
        spawn(async move { trial::popup::show_popup(&app_handle).await });
    }

	// ensure the 2 subfolders exist (creating at first launch)
	let app_handle = app.handle();
	if let Ok(config_dir) = app_handle.path().app_config_dir() {
        let cache_folder = config_dir.join("cache").to_string_lossy().to_string();
        let backup_folder = config_dir.join("backup").to_string_lossy().to_string();

        // Use existing create_folder function
        tauri::async_runtime::spawn(async move {
            let cache_result = crate::cmd::file::create_folder(cache_folder).await;
            let backup_result = crate::cmd::file::create_folder(backup_folder).await;

            log::debug!("Cache folder created: {}", cache_result);
            log::debug!("Backup folder created: {}", backup_result);
        });
    }

    // re-open files as appropriate
	let app_handle = app.app_handle().to_owned();
	spawn(async move { open_unsaved_autosave(app_handle).await });
    Ok(())
}
