.titlebar {
	height: 32px;
	width: 100vw;
	background: #222;
	color: #ddd;
	user-select: none;
	display: flex;
	justify-content: space-between;

	top: 0;
	left: 0;
	right: 0;
}
.titlebar .titlebar-button {
	display: inline-flex;
	justify-content: center;
	align-items: center;
	width: 30px;
	height: 30px;
	cursor: pointer;
}

.titlebar .titlebar-button:hover {
	background: #444;
}
.titlebar .fa-xmark {
	color: #ff6060;
}
.titlebar .box {
	width: 33%;
	user-select: none;
}

.titlebar .right-box {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.titlebar .middle-box {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 8px;
}

.titlebar .middle-box #save-state {
	width: 10px;
	height: 10px;
	border-radius: 2px;
	background-color: #8f8;
}

.titlebar .left-box {
	padding-left: 4px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	font-weight: bold;
	font-size: 14px;
}

.titlebar .left-box .symbole {
	color: #f1a415;
	margin-right: 4px;
	transform: rotate(90deg);
}

.titlebar .left-box .t {
	color: #f1a415;
}

.titlebar .left-box .code {
	color: #0fd1f3;
}
