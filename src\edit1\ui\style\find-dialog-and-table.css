html,
body {
	width: 100vw;
	height: 100vh;
	overflow: hidden;
	font-family: 'Avenir', 'Lucida Grande', Lucida, Verdana, sans-serif;
	padding: 0;
	margin: 0;
}

body.dialog {
	padding-left: 0;
	padding-right: 0;
}

* {
	box-sizing: border-box;
}

#search.finder {
	width: 100%;
	height: 100%;
	background-color: var(--neutral);
	border-bottom: 1px solid var(--titlebar-bg);
	border-left: 1px solid var(--titlebar-bg);
	border-right: 1px solid var(--titlebar-bg);
}

#search > *:not(textarea) {
	user-select: none; /* Standard */
	-webkit-user-select: none; /* Safari */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* IE 10+ */
}

#search form {
	width: 100%;
	max-width: 100%;
	height: calc(100% - 50px);
	padding: 8px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	gap: 4px;
}

#search form .box {
	width: 100%;
	height: 50%;
	display: flex;
	gap: 6px;
}

#search form .box .input-box {
	width: calc(100% - 7rem);
	height: 100%;
	display: flex;
	flex-direction: column;
}


#search form .box .input-box textarea {
	background-color: var(--input-bg);
	border: 1px solid var(--input-border);
	height: 100%;
	resize: none;
	padding: 4px;
	color: var(--primary-text);
	font-family: monospace;
	border-radius: 4px;
}

#search button {
	width: 100%;
	height: 24px;
	background: var(--button_color) !important;
	color: var(--button_text_color) !important;
	border: none;
	font-size: var(--sm-text);
}

#search button:hover {
	background-color: var(--hover-bg);
}

#search button:disabled {
	background-color: var(--disabled-btn-bg);
	color: var(--disabled-btn);
	cursor: not-allowed;
}

#search .options-box {
	width: 100%;
	height: 50px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}



#search .options-box input[type='checkbox']:disabled ~ label {
	text-decoration: line-through;
	color: var(--disabled-btn);
}

#search .checkbox-row {
	display: flex;
	gap: 12px;
}

#search .checkbox-row .opt{
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 4px;
}

#search .options-box .box {
	display: flex;
	justify-content: start;
	align-items: center;
	width: 7rem;
	height: 100%;
	padding-right: 8px;
}

