"""
row_details.py - details window for csv/txt files (via our SQLite cache)

SEE ALSO: db/record_details.py for SQLite DB though the code is very different
"""

from browser import aio, document, window
import os

# local to load first
import page_runner
import tauri

# local imports
from file_sqlite import Database
import db_utils
import deeply_utils
import shortcuts  # runs the bind commands on import
import table_utils

# -----
PROPS = None
try:
	PROPS = dict(window['__PROPS__'])
except Exception as e:
	print('ERROR in row_details: could not get __PROPS__', e)

PATH = PROPS['path']
source_filename = PROPS['table_name']  # used for connection and in SQL
DB_CONNECTION_STRING = 'sqlite:' + os.path.join('cache', source_filename + '.sqlite')
PATH_RELATIVE = PROPS['path_relative']
EDITS_TABLE_NAME = 'edits'  # TBD: replace with call to EditsSQL wrapper
CREATING_NEW_ROW = PROPS.get('is_new_row', False)

column_names = None
row_index = None  # from PROPS if not CREATING_NEW_ROW
row_dict = None
row_count = None
new_row = {}

changed_table_data = {}
table_body = document.querySelector('#table-body')


def get_value(key):
	# globals not modified here: CREATING_NEW_ROW, row_dict
	if CREATING_NEW_ROW:
		return ''
	else:
		return row_dict.get(key)


def create_edit_field(key):
	filtered_name = table_utils.fieldname_filter(str(key))
	value = get_value(key)

	if filtered_name in table_utils.blob_fields:
		return '<span class="blob">(BLOB)</span>'

	elif filtered_name in table_utils.not_editable:
		# earlier code returned <input with readonly flag but that seems awkward
		return '<span class="read-only">%s</span>' % value

	# else: usual case, fall thru for normal fields

	length = len(str(value))

	# -----
	# textarea
	if ('\n' in value) or (length > 90) or (filtered_name in table_utils.textarea_editable):
		lines = value.count('\n') + 1
		if lines == 1 and length > 90:
			lines = int(length / 90) + 1
		rows = min(lines, 7)  # for now: up to 7 lines; user can increase
		return '<textarea name="%s" rows="%s" class="detail-textbox width100" spellcheck="false">%s</textarea>' % (
			key, rows, value
		)
	# else: plain input

	# -----
	# input
	extra_class = ''
	size_attr = ''

	if (length > 50) or (filtered_name in table_utils.wide_editable):
		extra_class = ' width100'  # leading space is important!
	elif length > 10:
		width_px = length * 8  # 8 px for non-bold chars
		size_attr = ' size="%s"' % width_px
	# else: default width for <input

	return '<input name="%s" value="%s" class="detail-textbox%s"%s spellcheck="false" />' % (
		key, value, extra_class, size_attr
	)


async def display_data():
	global new_row
	# globals not modified here: CREATING_NEW_ROW, column_names, row_dict, table_body

	# CROSS-REF: display_data() in row_details.py vs. render_row() in record_details.py

	deeply_utils.pp('row_details.py - display_data - row_dict', row_dict)

	if CREATING_NEW_ROW:
		keys = column_names
	else:
		# displaying an existing row
		keys = row_dict.keys()

		###debug only
		if sorted(keys) != sorted(column_names):
			deeply_utils.pp('row_details.py - why different?', sorted(keys), sorted(column_names))

	visible_keys = [key for key in keys if key not in table_utils.built_in_fields]

	# compute max width based on longest key
	max_len = max((len(k) for k in visible_keys), default=0)
	width_px = max_len * 10  # 10px for bold chars

	rows = []
	for key in visible_keys:
		if CREATING_NEW_ROW:
			new_row[key] = ''

		label = table_utils.create_label(key)
		cell = create_edit_field(key)
		rows.append(f'<tr> <td class="fieldname" style="width: {width_px}px">{label}</td> <td>{cell}</td> </tr>')

	table_body.innerHTML = '\n'.join(rows)  # update the document

	add_on_change_listeners()


def add_on_change_listeners():
	# FYI: record_details.py also creates fieldset ... though it adds the listener incrementally
	elements = table_body.querySelectorAll('input, textarea')
	for element in elements:
		element.addEventListener('change', value_changed)  # callback


def value_changed(event):
	# callback
	if CREATING_NEW_ROW:
		new_row[event.target.getAttribute('name')] = event.target.value
	else:
		changed_table_data[event.target.getAttribute('name')] = event.target.value

	edit_save_btn()


save_btn = document.querySelector('#save')
save_btn.addEventListener('click', lambda e: aio.run(save_to_edits()))


def edit_save_btn(row_added = False):
	global save_btn
	save_btn.classList.add('active')

	if CREATING_NEW_ROW:
		save_btn.classList.add('active')
	else:
		how_many_changes = len(changed_table_data.keys())
		if how_many_changes == 1:
			save_btn.innerHTML = f'Save 1 Change'
		elif how_many_changes > 1:
			save_btn.innerHTML = f'Save {how_many_changes} Changes'
		else:
			save_btn.classList.remove('active')
			save_btn.innerHTML = 'Save'


async def check_unsaved_changes(changes_dict, from_nav=False):
	# refactor TBD: details.py & row_details.py should call a common function -- but needs work
	global changed_table_data
	# print("row_details.py -- CREATING_NEW_ROW = PROPS.get('is_new_row', False)", CREATING_NEW_ROW)
	if not changes_dict:
		return True

	message = table_utils.make_changed_message(len(changes_dict.keys()), CREATING_NEW_ROW=CREATING_NEW_ROW)
	save_changes = await tauri.dialog.confirm(message, {'cancelLabel': 'Discard', 'okLabel': 'Save'})
	if save_changes:
		await save(from_nav)
		return True
	else:
		if not CREATING_NEW_ROW:
			changed_table_data = {}
			edit_save_btn()
		return False


async def navigate(event):
	global row_index, changed_table_data

	next_or_prev = event.target.id
	parentElem = None
	if not next_or_prev:
		parentElem = event.target.parentElement;
		next_or_prev = parentElem.id

	# print('row_details.py -- navigate: row_index, next_or_prev', row_index, next_or_prev)
	if next_or_prev == 'navbar-next' and (row_index < row_count):
		await check_unsaved_changes(changed_table_data, True)
		document['navbar-prev'].classList.remove('disable-nav');
		row_index += 1
		if row_index == row_count:
			if parentElem:
				parentElem.classList.add('disable-nav')
			else:
				event.target.classList.add('disable-nav');
		await select_and_display_data()

	elif next_or_prev == 'navbar-prev' and row_index != 1:
		await check_unsaved_changes(changed_table_data, True)
		document['navbar-next'].classList.remove('disable-nav');
		row_index -= 1
		if row_index == 1:
			if parentElem:
				parentElem.classList.add('disable-nav');
			else:
				event.target.classList.add('disable-nav');

		await select_and_display_data()


async def select_and_display_data():
	global changed_table_data, row_dict
	# globals not modified: row_index

	row_dict = await get_row(row_index)
	changed_table_data = {}
	await display_data()


async def on_window_close():
	if not await check_unsaved_changes(changed_table_data):
		return
	await table_utils.close_window()  # after emit file_tables_update


async def save(from_nav=False):
	await save_to_edits(from_nav)


async def save_to_edits(from_nav=False):
	# refactor TBD: should call same code as editing in place, e.g. using EditsSQLite
	# - see file_data.py cell_edited
	# - which then calls sync_utils.edits_db.insert_data(data)
	timestamp = deeply_utils.now_string()
	if not CREATING_NEW_ROW:
		# i.e. edited an existing row ==> write the change to EDITS table
		global changed_table_data

		app_db = await Database.load(DB_CONNECTION_STRING)

		the_id = f'{row_dict[table_utils.file_row]}'
		the_table = PATH_RELATIVE
		the_field = ''
		old_value = ''
		new_value = ''
		columns = 'the_id, the_table, the_field, old_value, new_value, timestamp'
		placeholders = '?, ?, ?, ?, ?, ?'
		for key in changed_table_data:
			the_field = key
			old_value = row_dict[key]
			new_value = changed_table_data[key]
			value_list = [the_id, the_table, the_field, old_value, new_value, timestamp]
			query = f'INSERT INTO {EDITS_TABLE_NAME} ({columns}) VALUES ({placeholders})'
			try:
				result = await app_db.execute(query, value_list)
				if result.get('rows_affected', 0) == 0:
					raise Exception('could not save "%s" with "%s"' % (query, value_list))

			except Exception as e:
				print('ERROR in save_to_edits:', e)

	await change_main_table_data(from_nav, timestamp)

	changed_table_data = {}
	edit_save_btn()


async def change_main_table_data(from_nav=False, timestamp=None):
	global row_dict, new_row, changed_table_data
	if CREATING_NEW_ROW:
		await add_or_edit_row(new_row, False)
	else:
		for key in changed_table_data:
			row_dict[key] = changed_table_data[key]

		await add_or_edit_row(row_dict, True, timestamp)

	args = dict()
	await tauri.invoke('update_table', args)  # TBD: replace with emit -- indirectly calls file_data.update_table_row({})

	if from_nav:
		changed_table_data = {}
		edit_save_btn()
	else:
		await table_utils.close_window()


async def get_row(primary_key:int) -> dict:
	# globals not modified: source_filename

	# refactor TBD: move sql to db_utils
	query = f'SELECT * FROM "{source_filename}" WHERE {table_utils.file_row} = ? LIMIT 1;'
	values = [primary_key]
	row = await db_utils.get_row(DB_CONNECTION_STRING, query, values)

	deeply_utils.pp('row_details.py - get_row for source_filename "%s"; row "%s"', (source_filename, row))

	await get_row_count()  # sets global

	return dict(row)


async def get_row_count():
	global row_count
	# globals not modified: source_filename

	if row_count is None:
		row_count = await db_utils.count_cached_rows(source_filename)
		deeply_utils.pp('get_row_count', row_count)
	# else: assume it hasn't changed; not sure that's a good assumption!


async def add_or_edit_row(data: dict, edit=False, timestamp=None):
	# refactor TBD: move all SQL to db_utils.py -- OR -- to DataSQLite in file_sqlite.py
	# refactor TBD: current implementation doesn't share code between edit & update ==> probably split into 2
	# ... I also don't like having a function with true/false for completely different output

	# globals not modified: DB_CONNECTION_STRING, source_filename

	db = await Database.load(DB_CONNECTION_STRING)  # cache DB

	i=0
	if edit:
		values = []
		query = f'UPDATE "{source_filename}" SET '
		for key in data:
			if key == table_utils.file_row:
				i+=1
				continue
			if i < (len(data)-1):
				query += f'[{key}] = ?,'
			else:
				# refactor TBD: move this out of the for loop
				query += f'[{key}] = ?, [{table_utils.mod_date}] = ?, [{table_utils.mod_note}] = ? WHERE {table_utils.file_row} = ?;'

			values.append(data[key])
			i+=1

		values.append(timestamp)
		values.append('edited')
		values.append(data[table_utils.file_row])

	else:
		timestamp = deeply_utils.now_string()
		values = []
		query = f'INSERT INTO "{source_filename}" ('
		for key in data:
			if key == table_utils.file_row:
				i+=1
				continue

			if i < (len(data)-1):
				query += f'[{key}],'
			else:
				# refactor TBD: move this out of the for loop
				query += f'[{key}], [{table_utils.mod_date}], [{table_utils.mod_note}]) VALUES ('
				values_list= ['?' for _ in range((len(data)+2))]
				query += ', '.join(values_list)
				query += ');'

			values.append(data[key])
			i+=1

		values.append(timestamp)
		values.append('added')

	_result = await db.execute(query, values)  # UPDATE or INSERT so res is just FYI until add error handling
	# TBD: see error handling in details.py save_row()
	# our Python wrapper returns a dict with rows_affected & last_insert_id
	# print('add_or_edit_row: _result', _result)


# -----
async def main():
	global row_dict, row_index, column_names
	# globals not modified here: CREATING_NEW_ROW, PROPS, row_index, source_filename

	deeply_utils.pp('row_details.py - main -- PROPS', PROPS)

	if CREATING_NEW_ROW:
		document.querySelector('#save').innerHTML = 'Save New Row'
	else:
		# displaying an existing row
		document.querySelector('#save').innerHTML = 'Save Changes'
		# document['key'].innerHTML = 'Label'

		if not await db_utils.exists_with_data(source_filename):
			window.alert('The cache is missing. Please re-load.')
			return

		row_index = int(PROPS['row_index'])  # the row's file_row cell ==> db primary key
		row_dict = await get_row(row_index)

		deeply_utils.pp('row_details.py - main - row_index, row_dict', row_index, row_dict)

		document.querySelector('#navbar-next').addEventListener('click', lambda e: (aio.run(navigate(e))))
		document.querySelector('#navbar-prev').addEventListener('click', lambda e: (aio.run(navigate(e))))

	column_names = await db_utils.get_cache_column_names(source_filename)

	await display_data()  # create a 'pivot' view; NOT with tabulator since that gets in the way here

	# leftover from tapx1 but not in other windows here so omit
	# document.querySelector('#header-help').addEventListener('click',  lambda _: aio.run(table_utils.open_help_window()))

	current_window = tauri.window.getCurrentWindow()
	current_window.listen('tauri://close-requested', lambda _: aio.run(on_window_close()))

page_runner.register_for_js()  # hardcoded for 'main'
