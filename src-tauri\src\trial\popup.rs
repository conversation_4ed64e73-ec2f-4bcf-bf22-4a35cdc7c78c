use crate::{
	cmd::window::{create_document_window, NewWindowOptions},
	global_config::AppConfig,
};
use tauri::{Manager, State};

pub async fn show_popup(app: &tauri::AppHandle) {
	// Finally, check trial and show popup

	let app_config: State<'_, AppConfig> = app.state();
	log::debug!("Plan: {:?}", app_config.plan);

	if let Some(info) = &app_config.trial_info {

		// in theory: only show the 'trial' popup if within the 31 day trial period
		// but in practice: I need a way to test the trial version
		// ==> ONLY check trial_still_active in mod.rs
		// but if code gets here during DEBUG via hardcoded TRIAL then days doesn't matter!
// 		let trial_active = info
// 			.time_since_activate()
// 			.map(|duration| duration.num_days() < 31)
// 			.unwrap_or(false);

		// ----------
// 		log::debug!("=========================================");
// 		log::debug!("Before trial_active calculation - info: {:?}", info);
// 		log::debug!("time_since_activate(): {:?}", info.time_since_activate());
// 		let duration_result = info.time_since_activate();
// 		match duration_result {
// 			Ok(duration) => log::debug!("days since activation: {}", duration.num_days()),
// 			Err(e) => log::debug!("days since activation: Error - {}", e),
// 		}
// 		log::debug!("result: {}", trial_active);
// 		log::debug!("=========================================");
		// ----------

		#[allow(unused_mut)]
		let mut should_show_popup = info
			.time_since_popup()
			.map(|opt_duration| opt_duration.map(|duration| duration.num_hours() > 24))
			.unwrap_or(Some(true))
			.unwrap_or(true);

		log::debug!("should_show_popup if > 24 hours {}", should_show_popup);

		// Allow controlling by ENV to test without waiting 24 hours
		#[cfg(debug_assertions)]
		{
			if let Ok(env_show_popup) = std::env::var("SHOW_TRIAL_POPUP") {
				should_show_popup = env_show_popup.to_lowercase() == "1";
				log::debug!("should_show_popup {} from env variable", should_show_popup);
			}
		}

		// if popup not showed in latest 24 hours (or if hardcoded to show for testing)
		if should_show_popup {
			let app_handle = app.app_handle();
			let _ = super::update_popup_date(app_handle);

			create_document_window(
				app.app_handle().clone(),
				"Trial".to_owned(),
				"trial".to_owned(),
				"/windows/trial.html".to_owned(),
				NewWindowOptions {
					allow_duplicate: Some(false),
					..Default::default()
				},
			)
			.await;
		}
	} else {
		log::error!("trial info is none");
	}
}
