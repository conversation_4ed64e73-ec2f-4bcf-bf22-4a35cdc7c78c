use crate::{
    cmd::window::{create_document_window, NewWindowOptions},
    global_config::AppConfig,
};
use tauri::{Manager, State};

pub async fn show_popup(app: &tauri::AppHandle) {
    // Finally, check trial and show popup

    let app_config: State<'_, AppConfig> = app.state();
    log::debug!("Plan: {:?}", app_config.plan);

    if let Some(info) = &app_config.trial_info {
        let trial_active = info
            .time_since_activate()
            .map(|duration| duration.num_days() < 31)
            .unwrap_or(false);

        #[allow(unused_mut)]
        let mut should_show_popup = info
            .time_since_popup()
            .map(|opt_duration| opt_duration.map(|duration| duration.num_hours() > 24))
            .unwrap_or(Some(true))
            .unwrap_or(true);
        log::debug!("should show popup {}", should_show_popup);
        // Allow controlling by ENV
        #[cfg(debug_assertions)]
        {
            if let Ok(env_show_popup) = std::env::var("SHOW_TRIAL_POPUP") {
                should_show_popup = env_show_popup.to_lowercase() == "1";
            }
        }

        // if popup not showed in latest 24 hours
        // and trial active
        // and no valid license
        // show popup
        log::debug!(
            "should popup: {} trial active: {}",
            should_show_popup,
            trial_active
        );
        if trial_active && should_show_popup {
            let app_handle = app.app_handle();
            let _ = super::update_popup_date(app_handle);
            create_document_window(
                app.app_handle().clone(),
                "Trial".to_owned(),
                "trial".to_owned(),
                "/windows/trial.html".to_owned(),
                NewWindowOptions {
                    allow_duplicate: Some(false),
                    ..Default::default()
                },
            )
            .await;
        }
    } else {
        log::error!("trial info is none");
    }
}
