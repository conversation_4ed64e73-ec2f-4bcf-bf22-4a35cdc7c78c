"""
db_utils.py - wrapper around cached get_friendly_fk_columns
"""

from browser import document, window
from typing import List
import os

# import first
import tauri

# local imports
import deeply_utils
import table_utils


# ---------------
class SimpleDB:
	# CONSIDER: create CacheDB that takes table_name instead of path
	# ... though SQLite code deals with multiple table_names and customer-supplied path

	def __init__(self, path):
		self.path = path
		# self.DEBUG = False


	async def get_rows(self, query, bind_values=[]):
		return await get_rows(self.path, query, bind_values)


	async def get_table_data(self, table_name):
		query = f"SELECT * from '{table_name}'"
		return await self.get_rows(query)


	async def get_table_info(self, table_name):
		query = f"PRAGMA table_info('{table_name}')"
		return await self.get_rows(query)


	async def get_fk_info(self, table_name):
		# this may be called for any table_name
		query = f"PRAGMA foreign_key_list('{table_name}')"
		return await self.get_rows(query)


	async def get_rows_where(self, table_name, key, value, operator='='):
		query = f'''
			SELECT *
			FROM {table_name}
			WHERE {key} {operator} ?
			'''

		return await get_rows(self.path, query, [value])
# ---------------


def make_partial_cache_path(source_filename):
	return os.path.join('cache', source_filename + '.sqlite')


async def exists_with_data(source_filename):
	# i.e. table_exists_with_data or have_valid_cache_db
	# for SQLite cache of file data (csv, txt, json, html)

	if not source_filename:
		print('ERROR: missing source_filename')
		return False

	return await table_utils.invoke_tauri('sqlite_exists_with_data', {
		'db': make_partial_cache_path(source_filename),
	})


async def count_cached_rows(source_filename):
	# for cache: source_filename suffices to determine path & table

	query = f'SELECT COUNT(*) AS count FROM "{source_filename}";'
	row = await get_row(make_partial_cache_path(source_filename), query)

	if row:
		return dict(row)['count']
	else:
		return 0


async def get_cache_column_names(source_filename):
	# for cache: source_filename suffices to determine path & table

	# query = f'PRAGMA table_info("{source_filename}")'
	query = f"PRAGMA table_info('{source_filename}')"
	rows = await get_rows(make_partial_cache_path(source_filename), query)

	column_names = []
	for row in rows:
		column_names.append(dict(row)['name'])

	deeply_utils.pp('get_cache_column_names', column_names)

	return column_names


async def get_rows(db_path, query, bind_values=[]):
	# query & bind_values: variables must be specified as a list in the correct order
	# FUTURE to CONSIDER: change the callers and this code and Rust to use a param dict instead bind_values list
	if not db_path:
		print('ERROR: missing db_path')
		return []

	if not query:
		print('ERROR: missing query')
		return []

	return await table_utils.invoke_tauri('sqlite_select', {
		'db': db_path,
		'query': query,
		'values': bind_values,
	})


async def get_row(db_path, query, bind_values=[]):
	deeply_utils.pp('db_utils.py - get_row', db_path, query, bind_values)
	rows = await get_rows(db_path, query, bind_values)
	if rows:
		return rows[0]
	else:
		return []


# FYI: functools cache does NOT work with async
async def get_friendly_fk_columns(simple_db, table) -> List[str] | None:
	"""
	Return foreign field name of foreign key based on heuristics
	"""
	foreign_key_info = await simple_db.get_fk_info(table)
	foreign_key_columns = [i['from'] for i in foreign_key_info]

	columns = await simple_db.get_table_info(table)  # column_dicts

	non_special_cols = []

	for column in columns:
		name = column['name']
		if name not in foreign_key_columns and column['pk'] == 0:
			non_special_cols.append(name)

	if not non_special_cols:
		return None # No friendly columns
	if len(non_special_cols) == 1:
		return [non_special_cols[0]] # Only single possible frinedly column

	# looks for common patterns (ad hoc / hardcoded; there are likely many others that would be useful)
	patterns: list[list[str]] = [
		['FirstName', 'LastName', 'CompanyName'],
		['ContactName', 'CompanyName'],
		['CompanyName'],
		['FirstName', 'LastName'],
		['Name'],
		['Label']
	]
	friendly = []
	non_special_cols_lowercase = [i.lower() for i in non_special_cols]
	for pattern_items in patterns:
		# Match with snake_case / TitleCase or with space between.
		pattern_lower = [item.lower().replace('_', '').replace(' ', '') for item in pattern_items]
		if all(
			# each item in pattern must be in non special columns. or they singular representation
			any(i in non_special_cols_lowercase for i in [item.lower(), deeply_utils.remove_plural_suffix(item.lower())])
			for item in pattern_lower
		):
			for item in pattern_items:
				# Append the original column name
				original_column = next((k for k in non_special_cols if k.lower().replace('_', '').replace(' ', '') == item.lower().replace('_', '').replace(' ', '')), None)
				if original_column:
					friendly.append(original_column)
			# break on first matched pattern_items
			break

	return friendly
