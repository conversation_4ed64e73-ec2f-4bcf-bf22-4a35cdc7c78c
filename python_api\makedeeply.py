import requests

class MakeDeeply:

	# no installer yet; just include in your project

	API_PORT = 44986  # hardcoded for each product
	API_HOST = '127.0.0.1'
	MAIN_WINDOW_LABEL = 'main'

	"""
	API for MakeDeeply desktop app
	"""
	def __init__(self, host = API_HOST, port = API_PORT) -> None:
		self.base_url = f'http://{host}:{port}'


	def post(self, command, key, value, extras_dict={}):
		"""
		wrapper with error handling
		"""
		param_dict = {key: value}
		if extras_dict:
			param_dict.update(extras_dict)

		try:
			resp = requests.post(f'{self.base_url}/{command}', json=param_dict)
		except Exception as e:
			print()
			print('ERROR for', command, key, value)
			print(e)
			print()
			return {}

		resp.raise_for_status()
		return resp.json()


	def create_window(self, url: str):
		"""
		create new window with the given URL
		returns label (id) for window
		"""
		result_dict = self.post('create_window', "url", url)
		return result_dict.get('label', '')


	def get_outer_html(self, label: str):
		"""
		get outer HTML for a given window label
		"""
		result_dict = self.post('get_outer_html', 'label', label)
		return result_dict.get('html', '')


	def wait_for_document(self, label: str):
		"""
		wait for document to be ready
		"""
		return self.post('wait_for_document', 'label', label)


	def set_visible(self, label: str, visible: bool):
		"""
		set window visibillity
		"""
		return self.post('set_visible', 'label', label, {"visible": visible})


	def is_focused(self, label: str):
		"""
		get window focused
		returns boolean
		"""
		result_dict = self.post('is_focused', 'label', label)
		return result_dict.get('is_focused', '')


	def set_focus(self, label: str, focus: bool):
		"""
		set window visibillity
		"""
		return self.post('set_focus', 'label', label, {"focus": focus})


	def close_window(self, label: str):
		"""
		close window
		"""
		return self.post('close_window', 'label', label)


	def get_location(self, label: str):
		"""
		get document.location for a given window label
		"""
		result_dict = self.post('get_location', 'label', label)
		return result_dict.get('location', '')
