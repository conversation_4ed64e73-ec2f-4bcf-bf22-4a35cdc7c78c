use std::collections::HashMap;

use regex::Regex;
use serde_json::json;
use tauri::{ <PERSON><PERSON><PERSON>,Listener, Manager, WebviewWindow};

use crate::{edit1::{get_label, get_window_title_by_path, AppState}, window::menu};

// Remove the import of the local menu module since we're using the main menu system now


// #[cfg(target_os = "windows")]
// use os::windows;

#[tauri::command]
pub fn set_active_window(window: tauri::WebviewWindow, state: tauri::State<AppState>) {
    let window_label = window.label();

    let _ = window.emit("app:focus-change", json!({"label":window_label}));
    *state.active_window.lock().unwrap() = Some(window);
}

fn get_next_untitled_name(windows: &HashMap<String, WebviewWindow>) -> String {
    let base_name = "untitled";
    let pattern = format!(r"^{}(\(\d{{2}}\))?$", regex::escape(base_name));
    let regex = Regex::new(&pattern).unwrap();

    let mut max_number = 0;
    let mut has_base = false;

    // Check existing names and find the highest number
    for name in windows.keys() {
        let window = windows.get(name);
        if let Some(w) = window {
            let title = w.title().unwrap();
            if title == base_name {
                has_base = true;
                continue;
            }

            if let Some(caps) = regex.captures(&title) {
                if let Some(number_part) = caps.get(1) {
                    let number_str = &number_part.as_str()[1..number_part.as_str().len() - 1];
                    if let Ok(num) = number_str.parse::<u32>() {
                        if num > max_number {
                            max_number = num;
                        }
                    }
                }
            }
        }
    }

    // Determine the next available number
    let next_number = if has_base {
        // If base exists, start from 02
        max_number.max(1) + 1
    } else {
        // If base doesn't exist, use it
        return base_name.to_string();
    };

    // Format with leading zero for numbers < 10
    format!("{}({:02})", base_name, next_number)
}


pub async fn create_new_editor(
	handle: tauri::AppHandle,
	file_path: Option<String>,
) -> WebviewWindow {
	let window_id = format!("editor-{}", get_label());
	build_editor_window(handle, window_id, file_path).await
}

pub async fn create_new_editor_with_id(
	handle: tauri::AppHandle,
	window_id: String,
	file_path: Option<String>,
) -> WebviewWindow {
	build_editor_window(handle, window_id, file_path).await
}

async fn build_editor_window(
	handle: tauri::AppHandle,
	window_id: String,
	file_path: Option<String>,
) -> WebviewWindow {
	let menu = menu::create_menu(handle.clone(), menu::MenuType::TextEditor, false).unwrap();

	let label = if let Some(path) = file_path {
		get_window_title_by_path(path)
	} else {
		get_next_untitled_name(&handle.webview_windows())
	};

	let window = tauri::WebviewWindowBuilder::new(
		&handle,
		&window_id,
		tauri::WebviewUrl::App("src/edit1/index.html".into()),
	)
	.title(label)
	.inner_size(1000.0, 650.0)
	.menu(menu)
	.disable_drag_drop_handler()
	.build()
	.unwrap_or_else(|e| {
		panic!("Failed to build window `{}`: {}", window_id, e);
	});

	let state:tauri::State<AppState> = handle.state();
	*state.active_window.lock().unwrap() = Some(window.clone());

	let window_c = window.clone();
	let window_c1 = window.clone();
	let app_handle_c = handle.clone();
	let app_handle_c1 = handle.clone();

	window.listen("tauri://window-created", move |event| {
		menu::update_titles(&app_handle_c);
		window_c.unlisten(event.id());
	});
	window.listen("tauri://destroyed", move |event| {
		menu::update_titles(&app_handle_c1);
		window_c1.unlisten(event.id());
	});

	window
}



#[tauri::command]
pub async fn run_new_editor(handle: tauri::AppHandle) {
// await instead of spawn so `_win` isn't dropped immediately
let _win = create_new_editor(handle, None).await;
// store `_win` somewhere if you need to reference it later
}
