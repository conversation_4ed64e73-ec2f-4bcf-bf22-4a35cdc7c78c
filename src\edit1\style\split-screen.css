:root {
	--split-resizer-h:12px
}

#container .split-screen-container {
	width: 100%;
	flex-grow: 1;
	display: flex;
	flex-direction: column;
	overflow-y: auto;

}
#container .split-screen-container #editor2 {
	width: 100%;
	height: calc(100% - 16px);
	flex-grow: 1;
}
#container .split-screen-container #editor2 .cm-editor {
	height: 100%;
	flex-grow: 1;
}

#container .split-screen-container .splite-resizer {
	width: 100%;
	height: var(--split-resizer-h);
	background: var(--resizer-bg);
	border-top:1px solid var(--resizer-border-clr);
	border-bottom:1px solid var(--resizer-border-clr);
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: ns-resize;
}

#container .split-screen-container .splite-resizer .editor-resizer {
	width: 6px;
	height: 6px;
	/* background: #ddd; */
	border: 2px solid var(--resizer-circle-clr);
	border-radius: 100%;
}

#split-doc {
	background-color: transparent;
	border: none;
	width: 16px;
	height: 16px;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0;
	cursor: pointer;
	border-radius: 2px;
}

#split-doc.split {
	filter: invert(2);
	background-color: hsl(0, 0%, 100%);
}

#split-doc svg {
	width: 14px;
	height: 14px;
}

.split-dragger-box {
	position: absolute;
	right: 0;
	z-index: 999;
}

.split-dragger-box > .split-dragger {
  position: absolute;
  right: 1px;
  width: 14px;
  height: 7px;
  background-color: var(--resizer-thumb-bg);
  cursor: row-resize;
  z-index: 10;
}
