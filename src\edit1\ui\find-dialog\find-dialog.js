import { emit, listen } from '@tauri-apps/api/event';
import { getCurrentWebviewWindow, WebviewWindow } from '@tauri-apps/api/webviewWindow';
import {
	caseEl,
	entireWordEl,
	findIn,
	formEl,
	grepEl,
	nextBtn,
	previousBtn,
	replaceAllBtn,
	replaceBtn,
	replaceFindBtn,
	replaceIn,
	selTextOnlyEl,
	tabulateBtn,
	wrapAroundEl,
} from './js/elements';
import { FinderStore } from './js/storage';
import { invoke } from '/common/tauri.js';
const appWindow = getCurrentWebviewWindow();

//listen to theme change
listen('theme:change', (e) => {
	if (e.payload.dark) document.body.classList.add('dark-theme');
	else document.body.classList.remove('dark-theme');
});

//form data
let data = {
	find: '',
	replace: '',
	case_sensitive: false,
	entire_word: false,
	grep: true,
	showMatches: true,
	selected_only: false,
	wrapAround: false,
};

window.addEventListener('DOMContentLoaded', async () => {
	await emit('finder:created', {});
});

(async () => {
	let d = await FinderStore.getFinderData();
	if (d != null) {
		data = d;
		findIn.value = data.find;
		replaceIn.value = data.replace;
		caseEl.checked = data.case_sensitive;
		entireWordEl.checked = data.entire_word;
		grepEl.checked = data.grep;
		selTextOnlyEl.checked = data.selected_only;
		wrapAroundEl.checked = data.wrapAround;
	}
})();

let parentWind;
let typingTimer;
const TYPING_INTERVAL = 1000;

appWindow.onCloseRequested(async () => {
	if (parentWind) {
		await parentWind.emit('finder:closing');
	}
	await appWindow.close();
});

// Changing the parent Window to the focused one.
// await listen(TauriEvent.WINDOW_FOCUS, async (e) => {
// 	console.log("Focus Changed",e);
// 	if (e.windowLabel != 'search' && parentWind && parentWind.label != e.windwoLabel && !e.windwoLabel.includes('tabulated')) {
// 		console.log('Foxus =>', e);
// 		await setParentWindow(e.windowLabel);
// 		await parentWind.emit('finder:get-window-data');
// 	} else if (e.windowLabel == appWindow.label) formEl.dispatchEvent(new Event('input')); //parentWind.emit('finder:open-panel');
// });

// await listen('app:last-focused', async (e) => {
// 	console.log('Foxus =>', e.payload,parentWind?.label);
// 	if (e.windowLabel != 'search' && parentWind && parentWind.label != e.payload.label && !e.payload.label.includes('tabulated')) {
// 		console.log("new =>",e.payload.label);
// 		setParentWindow(e.windowLabel);
// 		await parentWind.emit('finder:get-window-data');
// 	} else if (e.windowLabel == appWindow.label) formEl.dispatchEvent(new Event('input')); //parentWind.emit('finder:open-panel');
// });

await listen("finder:change-parent", async (e) => {
	console.log("Focus Changed",e.payload.label);
	// if (e.windowLabel != 'search' && parentWind && parentWind.label != e.windwoLabel && !e.windwoLabel.includes('tabulated')) {
	// 	console.log('Foxus =>', e);
		await setParentWindow(e.payload.label);
		await parentWind.emit('finder:get-window-data');
	// } else if (e.windowLabel == appWindow.label) formEl.dispatchEvent(new Event('input')); //parentWind.emit('finder:open-panel');
});

async function setParentWindow(label) {
	parentWind = await WebviewWindow.getByLabel(label);
	console.log('Label', label,parentWind);
	// formEl.dispatchEvent(new Event('input'));
}

//open the finder
await listen('finder:on-open', async (d) => {
	await setParentWindow(d.payload);
	console.log('On open', data, d);
	findIn.focus();
	findIn.select();
	await parentWind.emit('finder:get-window-data');
	await parentWind.emit('finder:get-data');

	await parentWind.emit('finder:find', data);
});

listen('finder:set-data', (e) => {
	let q = e.payload.query;
});

await parentWind?.listen('finder:set-window-data', async (e) => {
	console.log('set-window-data', e);
	let { path, appTitle } = e.payload;
	// document.getElementById('titlebar-title').innerText = `Find/Replace in ${file} ${folder == 'unsaved' ? '' : ` of ${folder}`}`;
	const title = path ? await invoke('get_window_title_by_path', { path }) : appTitle;
	// await appWindow.setTitle(`Find/Replace in ${file} ${folder == 'unsaved' ? '' : ` of ${folder}`}`);
	console.log('path =>', path, 'title=>', title);
	await appWindow.setTitle(title);
});

// get the Form data when they change
formEl.addEventListener('input', async function (e) {
	console.log('heard formEl i.e. finder-form');
	e.preventDefault();
	//get Data from the form
	var formData = new FormData(this);
	let obj = Object.fromEntries(formData);
	data.find = obj.find.trim();
	data.replace = obj.replace.trim();
	data.selected_only = selTextOnlyEl.checked;
	disableBtnsIfTextSel();
	data.case_sensitive = caseEl.checked;
	data.entire_word = entireWordEl.checked;
	data.grep = grepEl.checked;
	data.wrapAround = wrapAroundEl.checked;
	//emit the the data to window.
	parentWind.emit('finder:find', data);
	triggerMatches();
	await FinderStore.storeFinderData(data);
});

function disableBtnsIfTextSel() {
	if (data.selected_only) {
		nextBtn.disabled = true;
		previousBtn.disabled = true;
		replaceBtn.disabled = true;
		replaceFindBtn.disabled = true;
		wrapAroundEl.disabled = true;
	} else {
		nextBtn.disabled = false;
		previousBtn.disabled = false;
		replaceBtn.disabled = false;
		replaceFindBtn.disabled = false;
		wrapAroundEl.disabled = false;
	}
}

//get the find value when it change
findIn.addEventListener('input', (e) => {
	data.find = e.target.value.trim();
	parentWind.emit('finder:find', data);
	findIn.dispatchEvent(new Event('keyup'));
});

//next,previous and first logic
nextBtn.addEventListener('click', () => {
	parentWind.emit('finder:next');
});

previousBtn.addEventListener('click', () => {
	parentWind.emit('finder:previous');
});

// firstBtn.addEventListener("click", () => {
//     parentWind.emit("finder:first");
// });

//get replace value when it change
replaceIn.addEventListener('input', (e) => {
	data.replace = e.target.value.trim();
	parentWind.emit('finder:replace-value', data.replace);
});

//replace,replace all and replace&find logic
replaceBtn.addEventListener('click', () => {
	parentWind.emit('finder:replace');
	// formEl.dispatchEvent(new Event("input"));
});

replaceAllBtn.addEventListener('click', () => {
	parentWind.emit('finder:replace-all');
});

replaceFindBtn.addEventListener('click', async () => {
	await parentWind.emit('finder:replace');
	await parentWind.emit('finder:next');
});

tabulateBtn.addEventListener('click', async () => {
	await parentWind.emit('tabulater:tabulate', data);
});

// matches found logic
function triggerMatches() {
	clearTimeout(typingTimer);
	typingTimer = setTimeout(() => {
		parentWind.emit('finder:count-matches');
	}, TYPING_INTERVAL);
}

findIn.addEventListener('keyup', () => {
	triggerMatches();
});

findIn.addEventListener('keydown', function () {
	clearTimeout(typingTimer);
});

// keyboard shortcuts

// in document (whether the dialog is open OR NOT):
// - cmd-e enter selected text into FIND
// - cmd-shift-E enter selected text into REPLACE
// - cmd-f opens the dialog
//
// in document or dialog:
// - cmd-g go to the next match
// - cmd-shift-G go to the previous match
// - cmd-= replace
// - cmd-shift-= replace AND go to the previous match
// - cmd-option-= replace AND go to the next match
//
// in dialog:
// - TAB navigates between FIND and REPLACE, selecting the entire contents
// - RETURN selects the entire contents

// in document
listen('finder:ctrl-e', async (d) => {
	await setParentWindow(d.windowLabel);
	findIn.value = data.find = d.payload;
	parentWind.emit('finder:find', data);
});

listen('finder:ctrl-shift-e', async (d) => {
	await setParentWindow(d.windowLabel);
	replaceIn.value = data.replace = d.payload;
	parentWind.emit('finder:find', data);
});

// in dialog
document.addEventListener('keydown', (e) => {
	switch (e.key) {
		case 'Tab':
			e.preventDefault();
			if (document.activeElement === replaceIn) {
				findIn.focus();
				findIn.select();
			} else {
				replaceIn.focus();
				replaceIn.select();
			}
			break;
		case 'Enter':
			if (e.location === 0) {
				e.preventDefault();
				e.target.select();
			}
			break;
	}
});

let sequence = [];

// in document or dialog
const otherShortcuts = [
	// Windows & Linux
	// go to next or previous
	{ keys: ['Control', 'g'], action: () => nextBtn.dispatchEvent(new Event('click')) },
	{
		keys: ['Control', 'shift', 'g'],
		action: () => {
			previousBtn.dispatchEvent(new Event('click'));
		},
	},

	// replace and optionally go to next or previous
	{ keys: ['Control', '='], action: () => replaceBtn.dispatchEvent(new Event('click')) },

	// ctrl-alt-= appears at itself (NOT consistent with alt-=)
	{
		keys: ['Control', 'alt', '='],
		action: () => {
			replaceBtn.dispatchEvent(new Event('click'));
			nextBtn.dispatchEvent(new Event('click'));
		},
	},

	// ctrl-shift-= appears as + (consistent with shift=-)
	{
		keys: ['Control', 'shift', '+'],
		action: () => {
			replaceBtn.dispatchEvent(new Event('click'));
			previousBtn.dispatchEvent(new Event('click'));
		},
	},
];

const macShortcuts = [
	// 'meta' is js term for the 'command' key
	// go to next or previous
	{ keys: ['Meta', 'g'], action: () => nextBtn.dispatchEvent(new Event('click')) },
	{
		keys: ['Meta', 'shift', 'g'],
		action: () => {
			previousBtn.dispatchEvent(new Event('click'));
		},
	},

	// replace and optionally go to next or previous
	{ keys: ['Meta', '='], action: () => replaceBtn.dispatchEvent(new Event('click')) },

	// cmd-alt-= appears as ≠ (consistent with alt-=)
	{
		keys: ['Meta', 'alt', '≠'],
		action: () => {
			replaceBtn.dispatchEvent(new Event('click'));
			nextBtn.dispatchEvent(new Event('click'));
		},
	},

	// cmd-shift-= appears as itself (NOT consistent with shift-=)
	{
		keys: ['Meta', 'shift', '='],
		action: () => {
			replaceBtn.dispatchEvent(new Event('click'));
			previousBtn.dispatchEvent(new Event('click'));
		},
	},
];

document.addEventListener('keydown', function (event) {
	if (!data.selected_only) {
		const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
		let shortcuts = isMac ? macShortcuts : otherShortcuts;
		if ((isMac && event.metaKey) || (!isMac && event.ctrlKey)) {
			sequence.push(event.key);
			if (sequence.length > 3) {
				sequence.shift();
			}
			console.log(sequence);
			for (let i = 0; i < shortcuts.length; i++) {
				const shortcut = shortcuts[i];
				if (sequence.join(',').toLowerCase() === shortcut.keys.join(',').toLowerCase()) {
					// console.log('Shortcut activated:', shortcut.keys.join('+'), '\n');
					shortcut.action();
					break;
				}
			}
		}
	}
});

document.addEventListener('keyup', function (event) {
	sequence = sequence.filter((key) => key !== event.key);
	// console.log(`keyup ${event.key} >>> `,sequence);
});

appWindow.emit('finder:loaded');
