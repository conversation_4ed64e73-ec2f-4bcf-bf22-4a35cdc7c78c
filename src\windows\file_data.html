<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<title>File Data</title>

		<!-- Brython -->
		<script type="text/javascript" src="/lib/brython/brython.js"></script>
		<script type="text/javascript" src="/lib/brython/brython_stdlib.js"></script>

		<!-- Tabulator -->
		<script type="text/javascript" src="/lib/tabulator/tabulator_custom.js"></script>
		<link rel="stylesheet" href="/lib/tabulator/tabulator_bulma.min.css" />

		<!--Extend Tabulator Module-->
		<script type="module" src="/windows/file.js" defer></script>

		<!-- 'deeply' startup code for every page -->
		<script type="text/javascript" src="/common/init_this_page.js"></script>
		<script src="/windows/page_runner.py" type="text/python" id="page_runner"></script>

		<!-- imports -->
		<script src="/common/tauri.py" type="text/python" id="tauri"></script>

		<script src="/common/app_data.py" type="text/python" id="app_data"></script>
		<script src="/common/db_utils.py" type="text/python" id="db_utils"></script>
		<script src="/common/deeply_utils.py" type="text/python" id="deeply_utils"></script>
		<script src="/common/file_sqlite.py" type="text/python" id="file_sqlite"></script>
		<script src="/common/table_utils.py" type="text/python" id="table_utils"></script>
		<script src="/windows/disable_window.py" type="text/python" id="disable_window"></script>
		<script src="/windows/sync_utils.py" type="text/python" id="sync_utils"></script>

		<script src="/windows/open_file.py" type="text/python" id="open_file"></script>

		<script src="file.py" type="text/python" id="file"></script>

		<script type="module" src="/lib/passive-events/index.js"></script>

		<!-- styles -->
		<link rel="stylesheet" href="/common/makedeeply.css" />
		<link rel="stylesheet" href="/windows/editors.css" />
		<link rel="stylesheet" href="/windows/file_data.css" />
	</head>
	<body id="file-data-page" class="data-page">
		<div id="not-data-note" hidden></div>
		<textarea id="not-data-area" hidden></textarea>

		<div id="header-btn-container">
			<div>
				<button id="edit-search">Search</button>
				<button id="edit-sort">Sort</button>
				<button id="edit-layout">Layout</button>
			</div>
			<div>
				<button class="if_paid_or_trial" id="insert">New</button>
			</div>
			<div>
				<button id="export-btn" disabled>Export</button>
				<button class="if_paid_or_trial can_import_files" id="import-btn">Import</button>
			</div>
			<div id="delete-btn-container">
				<button id="delete-btn">Delete</button>
			</div>
			<div id="edit-rows">
				<button class="if_paid_or_trial can_select_multiple_rows" id="hide-btn" disabled>Hide</button>
				<div class="if_paid_or_trial can_select_multiple_rows">
					<input type="checkbox" id="show-hidden-checkbox" />
					<label for="show-hidden-checkbox" class="show-hidden-label" id="show-hidden-label">Show hidden</label>
				</div>
				<button class="if_paid_or_trial can_select_multiple_rows" id="select-all-btn">Select All</button>
				<button class="if_paid_or_trial can_select_multiple_rows" id="select-none-btn">Select None</button>
			</div>
			<div>
				<button class="if_paid_or_trial" id="edit-column">Define</button>
			</div>
		</div>

		<div id="header-info-row">
			<span id="shown-records"></span>
		</div>

		<div id="edits-preview-container" hidden>
			<div id="edits-preview-text"></div>
			<div id="edits-preview-btns">
				<button class="btn" id="resume"></button>
				<button class="btn" id="start-over"></button>
			</div>
			<div id="edits-preview-table">

			</div>
		</div>

		<div id="main-container">
			<div id="table"></div>
			<textarea id="error-text-area"></textarea>
		</div>

		<script src="process_files.py" type="text/python"></script>
		<script src="file_data.py" type="text/python"></script>

		<div class="spinner-container">
			<div class="spinner"></div>
		</div>
	</body>
</html>
