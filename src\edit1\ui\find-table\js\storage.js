import { load } from '@tauri-apps/plugin-store';

const store = await load('.search.dat');

export async function getSearchState() {
	if (await store.has('data')) {
		const state = await store.get('data');
		console.log('SEARCH STATE', state);
		return state;
	}
	return null;
}

export async function setSearchState(state) {
	console.log(state);
	await store.set('data', state);
	await store.save();
}

export default {
	store,
	getSearchState,
	setSearchState,
};
