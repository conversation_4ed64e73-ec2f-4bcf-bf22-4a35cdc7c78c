"""
process_files.py - read csv/txt etc. files
"""

from browser import window, document
import csv
import io
import json

try:
	path = window['__PROPS__']['path']
	file_type = window['__PROPS__']['ext']  # from Rust: no leading dot (unlike Python)
	name = window['__PROPS__']['name']
except Exception as e:
	print('ERROR in process_files: could not get __PROPS__', e)

valid_file_types = ['csv', 'txt', 'tsv', 'json', 'jsonl', 'html', 'htm']

# -----
# globals

header = []

async def filter_lines(content):
	# strip whitespace, allowing None
	# skip comment lines; FUTURE: should be a param
	lines = content.splitlines()
	return [line for line in lines if line and line.strip() and not line.strip().startswith('#')]


async def process_row(row, is_import=False):
	# treat csv, txt & html the same

	global header

	# filter leading/trailing whitespace; convert None to empty string
	items = [item.strip() if item else '' for item in row]

	if not header:
		# only runs once but is inside the loop since that's how reader works
		if is_import:
			# create synthetic / internal field names with parens: (field1), (field2), (field3), etc.
			header = [f'(field{i+1})' for i in range(len(items))]
		else:
			header = items

	if is_import and len(items) > len(header):
		# handle rare case of more fields on a particular line
		for i in range(len(header), len(items)):
			header.append(f'(field{i+1})')

	# create a dictionary mapping real or synthetic field names to values
	row_dict = {}
	for i in range(len(header)):
		if i < len(items):
			row_dict[header[i]] = items[i]
		else:
			row_dict[header[i]] = ''  # fill missing items with empty string

	return row_dict


async def process_txt(content, delimiter, is_import=False):
	# is_import False: when opening a file, assume first row is column titles
	# is_import True: when importing, generate field1..n so that caller can handle with or without column titles

	content = '\n'.join(await filter_lines(content))

	f = io.StringIO(content, newline='')
	reader = csv.reader(f, delimiter=delimiter)

	row_dicts = []

	for row in reader:
		row_dicts.append(await process_row(row, is_import))

	if is_import:
		return row_dicts
	else:
		return row_dicts[1:]


def custom_decoder(value):
	# for JSON: treat null as empty string, including in nested data

	if value is None:
		return ''
	elif isinstance(value, str):
		return value.strip()
	elif isinstance(value, dict):
		return {key: custom_decoder(val) for key, val in value.items()}
	elif isinstance(value, list):
		return [custom_decoder(item) for item in value]
	else:
		# no change
		return value


async def process_json(content):
	# is_import param not relevant since JSON doesn't allow field-less data
	return json.loads(content, object_hook=custom_decoder)


async def process_html(content, is_import=False):
	# is_import False: when opening a file, assume first row is column titles
	# is_import True: when importing, generate field1..n so that caller can handle with or without column titles

	# this code intentionally ignores any distinction between thead/tbody & th/td

	parser = window.DOMParser.new()
	doc = parser.parseFromString(content, 'text/html')

	table = doc.querySelector('table')
	if not table:
		return []

	html_rows = table.querySelectorAll('tr')
	if not html_rows:
		return []

	row_dicts = []

	for html_row in html_rows:
		row = [cell.innerHTML for cell in html_row.querySelectorAll('th, td')]
		row_dicts.append(await process_row(row, is_import))

	if is_import:
		return row_dicts
	else:
		return row_dicts[1:]

# -----
async def get_content(is_import=False):
	# is_import False: when opening a file, assume first row is column titles
	# is_import True: when importing, generate field1..n so that caller can handle with or without column titles

	if file_type not in valid_file_types:
		# should be impossible but check just in case
		# print('file_type %s not in %s' % (file_type, ', '.join(valid_file_types)))
		return {}  # TBD: this should open as plain text in a TEXTAREA ... and soon in CodeMirror

	content = await window.__TAURI__.core.invoke('read_text_file', { 'path': path })

	if file_type in ('json', 'jsonl'):
		# TBD: refactor the json / jsonl code to avoid calling loads then dumps then load again with custom_decoder in process_files
		if file_type == 'jsonl':
			# convert into json then call standard parser
			# print('jsonl length', len(content))
			# print('jsonl lines', content.count('\n'))
			try:
				content = '[' + content.strip().replace('\n', ',') + ']'
			except Exception as e:
				print('Error parsing JSONL:', e)

		try:
			# Brian TBD: explain
			content = json.loads(content)
			content = json.dumps(content, indent='\t')
		except Exception as e:
			print('Error parsing JSON:', e)

	try:
		if file_type == 'csv':
			return await process_txt(content, ',', is_import)

		elif file_type in ('txt', 'tsv'):
			return await process_txt(content, '\t', is_import)

		elif file_type in ('html', 'htm'):
			return await process_html(content, is_import)

		elif file_type in ('json', 'jsonl'):
			return await process_json(content)  # does NOT support is_import

		else:
			# print('impossible file_type', file_type)
			return {}

	except Exception as e:
		# CONSIDER moving this to a function and handling json above
		print('Not simple row or col DATA:', e)
		document['header-btn-container'].style.display='none';

		not_data_area = document.querySelector('#not-data-area')
		not_data_area.innerHTML = content
		not_data_area.removeAttribute('hidden')

		not_data_note = document.querySelector('#not-data-note')
		not_data_note.innerHTML = 'Did not find row/column data so showing plain text.'
		not_data_note.removeAttribute('hidden')

# refactor TBD: is this useful after I added not_data_note?
# ... the textarea itself appears below the main table
# ... and why should it be a text area?
# def show_error_content(content):
# 	text_area = document['error-text-area']
# 	text_area.style.display = 'block'
# 	text_area.value = content
#
# 	text_area.style.height = 'auto'
#
# 	max_height = window.innerHeight - 70  # subtract for header
#
# 	content_height = text_area.scrollHeight
# 	new_height = min(content_height, max_height)
#
# 	text_area.style.height = f'{new_height}px'
#
# 	# Add scrollbar if content exceeds max height
# 	if content_height > max_height:
# 		text_area.style.overflowY = 'auto'
# 	else:
# 		text_area.style.overflowY = 'hidden'
#
# 	try:
# 		document['layout-editor-content'].style.display = 'none'
# 	except:
# 		# Element doesn't exist
# 		pass

# aio.run(get_content())

# =========================================================================
# filter code is still in file.js since extendModule yields a Brython error
# (see console log after opening a search dialog)

# reported 2024-08-19: https://github.com/brython-dev/brython/issues/2486
# =========================================================================

# def not_like_filter(search_value, cell_value, params):
# 	result = not search_value in cell_value
# 	return result

# def not_regex_filter(search_value, cell_value, params):
# 	result = re.search(search_value,cell_value)
# 	if result:
# 		return False
# 	else:
# 		return True

# def omit_keywords_filter(search_value, cell_value, params):
# 	keywords = str(search_value).split(' ')
# 	result = True
# 	for i in range(len(keywords)):
# 		if(str(search_value).lower() in str(cell_value).lower()):
# 			result = False
# 			break
# 	return result

# window.Tabulator.extendModule('filter', 'filters', {
# 	'not_like': not_like_filter,
# 	'not_regex': not_regex_filter,
# 	'omit_keywords': omit_keywords_filter,
# });
