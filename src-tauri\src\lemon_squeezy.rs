use anyhow::{bail, Result};
use reqwest::header;
use reqwest::{self, Client};
use uuid::Uuid;

/// Lemon Squeezy API base URL
const BASE_URL: &str = "https://api.lemonsqueezy.com";

pub struct LemonSqueezy {
    client: Client,
}

impl LemonSqueezy {
    /// Create instance with default headers
    pub fn try_create() -> Result<Self> {
        let mut headers = header::HeaderMap::new();
        headers.insert(
            "Accept",
            header::HeaderValue::from_static("application/json"),
        );
        headers.insert(
            "Content-Type",
            header::HeaderValue::from_static("application/x-www-form-urlencoded"),
        );
        let client = Client::builder().default_headers(headers).build()?;
        Ok(LemonSqueezy { client })
    }

    /// Activate new license_key
    pub async fn activate(&self, license_key: &str) -> Result<String> {
        let instance_name = Uuid::new_v4().to_string();
        let body = serde_json::json!({
            "license_key": license_key,
            "instance_name": instance_name
        });
        let resp = self
            .client
            .post(format!("{}{}", BASE_URL, "/v1/licenses/activate"))
            .json(&body)
            .send()
            .await?;
        let resp = resp.error_for_status()?;
        let resp: serde_json::Value = resp.json().await?;
        let instance = &resp["instance"];
        let instance_id = instance["id"].as_str();
        if let Some(instance_id) = instance_id {
            return Ok(instance_id.to_string());
        }
        bail!("instance ID not returned in activate!")
    }

    /// Validate license key
    pub async fn validate(&mut self, license_key: &str, instance_id: &str) -> Result<()> {
        let body = serde_json::json!({
            "license_key": license_key,
            "instance_id": instance_id
        });
        let resp = self
            .client
            .post(format!("{}{}", BASE_URL, "/v1/licenses/validate"))
            .json(&body)
            .send()
            .await?;
        let resp = resp.error_for_status()?;
        let resp: serde_json::Value = resp.json().await?;
        log::trace!("validate result from lemon squeezy: {:?}", resp);
        let valid = &resp["valid"].as_bool().unwrap_or(false);
        let error = &resp["error"].as_str().unwrap_or_default();
        if *valid {
            return Ok(());
        }
        bail!("Invalid license key! error: {}", error)
    }

    /// Deactivate license key in Lemon Squeezy
    pub async fn deactivate(&mut self, license_key: &str, instance_id: &str) -> Result<()> {
        let body = serde_json::json!({
            "license_key": license_key,
            "instance_id": instance_id
        });
        let resp = self
            .client
            .post(format!("{}{}", BASE_URL, "/v1/licenses/deactivate"))
            .json(&body)
            .send()
            .await?;
        let resp = resp.error_for_status()?;
        let resp: serde_json::Value = resp.json().await?;
        let deactivated = &resp["deactivated"].as_bool().unwrap_or(false);
        let error = &resp["error"].as_str().unwrap_or_default();
        if *deactivated {
            return Ok(());
        }
        bail!("can't deactivate! error: {}", error)
    }
}
