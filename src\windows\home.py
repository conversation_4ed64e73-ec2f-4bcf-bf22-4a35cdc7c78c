"""
home.py - open & handle the main window
"""

from browser import aio, document, window

# local to load first
import page_runner
import tauri

# local imports
import deeply_utils
import file_sqlite
import open_file
import shortcuts  # runs the bind commands on import
import table_utils

app_config = None  # must be loaded async


async def on_close():
	# <PERSON><PERSON> quits the app when the last window is closed, which is wrong on Mac
	# temporary workaround: prevent this main window from being closed!

	# platform TBD: only do this on macOS -- but should first refactor that code
	window.alert('To quit the app: use the Quit menu item. To minimize this window, click that control or select Minimize from the Window menu.')


async def open_history_if_any():
	history = await tauri.invoke('get_history')  # performance TBD: a faster call that checks if table exists and (count > 0)

	if not history:
		return

	await table_utils.create_known_window('history')

# -----

async def main():
	global app_config
	app_config = await window.get_app_config()
	open_file.app_config = app_config
	table_utils.app_config = app_config

	deeply_utils.pp('home.py - main')

	await table_utils.add_flags_to_body()

	if app_config.is_paid_or_trial:
		await open_history_if_any()
	else:
		# no easy way to clean on quit ==> clean on launch
		# (I was going to do both just in case, but this suffices even though not quite as restricted)
		await file_sqlite.clear_data(app_config)

	# bind various buttons using different code
	# refactor TBD: unique ==> should be ID not class
	open_file.bind_this_button('.open')  # attach listener to Open button via '.open' CSS class

	for which in ['edits', 'help', 'history']:
		button = document.querySelector(f'.{which}')  # FUTURE: 'actions',
		if button:
			# aio.run seems to be required; not sure why
			button.bind('click', lambda _, name=which: aio.run(table_utils.create_known_window(name)))
		else:
			# should never happen
			print('ERROR: missing "%s" button' % button)

	if app_config['product_name'] == 'Edit Deeply':

		button = document.querySelector('.new-text-file')
		if button:
			button.bind('click', lambda _: tauri.invoke('run_new_editor'))
		else:
			# should never happen
			print('ERROR: missing "%s" button' % button)

		button = document.querySelector('.open-text-file')
		if button:
			button.bind('click', lambda _: tauri.invoke('open_files'))
		else:
			# should never happen
			print('ERROR: missing "%s" button' % button)

		deeply_utils.pp('home.py - bound buttons for Edit Deeply')

	current_window = tauri.window.getCurrentWindow()
	current_window.listen('tauri://close-requested', lambda *_args: aio.run(on_close()))

	deeply_utils.pp('DONE with home.py')

page_runner.register_for_js()  # hardcoded for 'main'
