"""
save_utils.py - called from Export & File > Save

(even though it's a utils file, it's embedded in the web page so has access to the DOM)
"""

from browser import document, window
import csv
import io
import javascript
import json
import os
import tauri

# local imports
import deeply_utils
import table_utils

html_template = '''
<!DOCTYPE html>
<html lang="en">
	<head>
		<title>{title}</title>
		<meta name="generator" content="{product_name}">
	</head>
	<body id="export-dialog">
		{table}
	</body>
</html>
'''.lstrip()  # leave trailing newline

table_template = '''
<table>
	<thead>
		{header_row}
	</thead>
	<tbody>
		{body_rows}
	</tbody>
</table>
'''.strip()


async def save_deleted_columns(current_path, data, removed_col_names):
	# as backup before deleting a column: save a copy of column data to disk with file_row as primary key
	keep = []

	suffix = ' (columns deleted %s)' % deeply_utils.now_string(as_filestamp=True)

	app_data_dir = await tauri.get_app_data_dir()
	out_folder = os.path.join(app_data_dir, 'backup')
	out_path = deeply_utils.add_suffix(os.path.basename(current_path), suffix, folder=out_folder)

	primary_key = table_utils.file_row

	header = [primary_key] + removed_col_names

	keep = [header]

	for row in data:
		row_dict = dict(row)  # original is a js object
		keep_row = []

		for col_name in header:
			keep_row.append('%s' % row_dict.get(primary_key, ''))  # '%s' to convert to string in case that's important

		keep.append(keep_row)

	str_value = rows_to_string(keep)

	res = await tauri.invoke('save_file', {'file_path': out_path, 'data': str_value})  # TBD: file_path vs filePath
	if res:
		print('column backed up to csv file path: ', out_path)
	else:
		print('error saving column to csv file')


async def save_file(filepath, rows, file_type, field_names, header_row_if_different=[]):
	# column_definitions are in order: list of dict with original ['name'] and friendly ['label']
	csv_tsv_txt = ['csv', 'tsv', 'txt']
	html_htm = ['html', 'htm']

	keep = []  # list of string or dict

	if header_row_if_different:
		header = header_row_if_different
	else:
		header = field_names

	for data in rows:
		data_dict = dict(data)  # convert from JavaScript object
		keep_row = []  # for all except JSON
		json_dict = dict()

		# gather the data in order
		for field_name in field_names:
			value = '%s' % data_dict.get(field_name, '')  # use %s to convert to string in case useful
			if value is javascript.NULL:
				value = ''

			if file_type == 'json':
				json_dict[field_name] = value
			else:
				keep_row.append(value)

		# keep the row
		if file_type == 'json':
			keep.append(json_dict)
		else:
			keep.append(keep_row)

	line_ending = await get_line_ending()  # Scott TBD: fix today 7/6/25

	# assemble the data
	if file_type in csv_tsv_txt:
		if file_type == 'csv':
			delimiter = ','
		else:
			delimiter = '\t'

		final_data = await rows_to_string([header] + keep, delimiter, line_ending)

	elif file_type in html_htm:
		# FYI there's an interesting risk here:
		# - open an html file: we extract data from the first table
		# - File > Save: we will ONLY write the data we extracted, ovewriting everything else
		# ==> CONSIDER graying-out File > Save for HTML
		# (but meanwhile we keep a backup copy of the original on save ==> should be safe)

		with_page_template = document.querySelector('#with-page-template')
		if with_page_template:
			# i.e. called by export dialog ==> see if this value is checked
			with_page_template = with_page_template.checked
		else:
			# default: wrap in an html page -- though not sure this is a better default!
			with_page_template = True

		# pass filepath to generate a TITLE
		final_data = await data_to_html(header, keep, filepath, with_page_template)

	elif file_type == 'json':
		# TBD: support jsonl
		pp = document.querySelector('#pretty-printed')
		if pp:
			pp = pp.checked
		else:
			pp = True

		if pp:
			indent = 4
		else:
			indent = None

		final_data = json.dumps(keep, indent=indent).replace('\n', line_ending)

	else:
		window.alert('ERROR: impossible file_type', file_type)
		return

	# print('final_data', final_data)
	status = await save_file_rust(filepath, final_data)
	if not status:
		window.alert('ERROR: could not save data')


async def cache_to_backup(filename, edits_db):
	# move cached .sqlite to backups before caller generates new one without earlier changes
	# filename is of the original csv/txt etc. file which is also the core name of the SQLite cache

	app_data_dir = await tauri.get_app_data_dir()

	current_path = os.path.join(app_data_dir, 'cache', f'{filename}.sqlite')  # current_path to get timestamp; new_path after suffix

	# include full timestamp in the suffix (multiple edits per day not too common, but long filenames are fine)
	if edits_db:
		# from old code though in theory the cache file's mod date should suffice always
		timestamp = await edits_db.get_last_edited()
	else:
		timestamp = await tauri.invoke('get_file_mod_date', {'path': current_path})

	suffix = ' (edited %s)' % timestamp.replace(':', ';')  # colon to semi-colon to avoid macOS issue
	new_path = os.path.join(app_data_dir, 'backup', f'{filename}{suffix}.sqlite')

	# the following would work in Python but NOT Brython ==> call Rust
	# os.rename(current_path, new_path)

	deeply_utils.pp('cache_to_backup', current_path, new_path)
	status = await tauri.invoke('rename_file', {
		'oldPath': current_path,
		'newPath': new_path,
	})
	return status


async def original_to_backup(current_path):
	# called from File > Save, this puts a backup of the original in our 'backup' folder

	# include full timestamp in the suffix; a hack way to (mostly) ensure that same filename from different folders gets different name
	timestamp = await tauri.invoke('get_file_mod_date', {'path': current_path})

	suffix = ' (as of %s)' % timestamp.replace(':', ';')  # colon to semi-colon to avoid macOS issue

	app_data_dir = await tauri.get_app_data_dir()  # e.g. full path to com.makedeeply.datadeeply
	out_folder = os.path.join(app_data_dir, 'backup')
	new_path = deeply_utils.add_suffix(os.path.basename(current_path), suffix, folder=out_folder)

	# the following would work in Python but NOT Brython ==> call Rust
	# os.rename(current_path, new_path)

	deeply_utils.pp('original_to_backup:', current_path, new_path)
	status = await tauri.invoke('rename_file', {
		'old_path': current_path,  # TBD: oldPath vs old_path
		'new_path': new_path,
	})
	return status


async def overwrite_file(tauri_message={}, get_cached_data_CALLBACK=None, lbl=None):
	# handle File > Save -- but make a 'hidden' backup to prevent accidental data loss
	# Scott TBD July 2025: fix sync_utils. below
	global last_ignored_changes_date
	if lbl:
		label = lbl
	else:
		label = dict(dict(dict(tauri_message)).get('payload')).get('label')

	if label != current_window.label:
		# print('not target window')
		return

	backed_up = await original_to_backup(FILE_PATH)  # move original to backup folder before writing new

	if not backed_up:
		window.alert('Error making a copy of the file before saving. Try export.')
		return

	header_data = await get_save_header_data(sync_utils.header_data)

	if get_cached_data_CALLBACK:
		await get_cached_data_CALLBACK(False)

	saved = await save_file(FILE_PATH, sync_utils.table_data, header_data, file_type)

	new_last_ignored_date = deeply_utils.now_string()

	if not sync_utils.actions_db:
		await get_actions_db()

	if last_ignored_changes_date:
		# TBD: should probably store every save not just the most recent
		await sync_utils.actions_db.update_action(['date_saved'], [new_last_ignored_date], {'details': FILE_PATH})
	else:
		await sync_utils.actions_db.add_action('ignored edits', FILE_PATH, new_last_ignored_date, 1)

	last_ignored_changes_date = new_last_ignored_date


#......................html........................................................
async def data_to_html(header, rows, filepath, with_page_template=False):
	line_ending = await get_line_ending()

	header_row = await generate_table_header(header)
	body_rows = await generate_table_body(rows)

	table_html = table_template.format(header_row=header_row, body_rows=body_rows)

	if with_page_template:
		table_html = table_html.replace('\n', f'\n\t\t')  # indent to nicely fit inside the body
		page_title = table_utils.create_label(deeply_utils.drop_extension(filepath))  # friendly version

		html = html_template.format(
			table=table_html,
			title=page_title,
			product_name=table_utils.product_name,  # for generator
			)
	else:
		html = table_html

	if line_ending != '\n':
		html = html.replace('\n', line_ending)

	return html


async def generate_table_header(header):
	th_list = []

	for item in header:
		th_list.append(f'<th>{item}</th>')

	return '<tr>' + ''.join(th_list) + '</tr>'


async def generate_table_body(rows):
	line_ending = await get_line_ending()
	html_rows = []

	for row in rows:
		td_list = []

		for item in row:
			td_list.append(f'<td>{item}</td>')

		keep.append('<tr>' + ''.join(td_list) + '</tr>')

	return f'{line_ending}\t\t'.join(keep)


#.................................end.....................................................
#.................................csv...............................................
async def rows_to_string(rows, delimiter=',', line_ending='\n'):
	# use csv.writer to handle quoting (csv and tsv/txt)
	# but Brython doesn't write to disk ==> assemble a large string for Rust

	# performance TBD: skip this and send row data directly to Rust

	# use a file-like object for csv.writer

	with io.StringIO() as output:
		writer = csv.writer(
			output,
			delimiter=delimiter,
			quoting=csv.QUOTE_MINIMAL,
			lineterminator=line_ending
		)
		writer.writerows(rows)
		return output.getvalue()

	# the 'with' will automatically close the output


#................................end..........................................................


#.....................save file.......................
async def save_file_rust(path, data):
	return await window.__TAURI__.core.invoke('save_file', { 'filePath': path, 'data': data });


#............................end.........................
#.........................get save header data...........
async def get_save_header_data(main_table_header_data):
	export_header_data = []
	for row in main_table_header_data:
		if dict(row)['name'] not in table_utils.built_in_fields:
			export_header_data.append(row)

	return export_header_data


#...........................end.......................................................


#..........................line-ending..............................................


async def get_line_ending():
	# may be called for Export (which has crlf option) or Save (which should use platform default)

	# Scott TBD: fix today 7/6/25 -- ideally only call once

	table_crlf = document.getElementById('crlf')

	line_ending = '\n'  # default: macos & linux

	if table_crlf:
		# came from export dialog
		if table_crlf.checked:
			line_ending = '\r\n'

	else:
		# came from File > Save
		os_name = await window.__TAURI__.core.invoke('get_os')  # returns macos, windows, linux
		if os_name == 'windows':
			line_ending = '\r\n'

	return line_ending
