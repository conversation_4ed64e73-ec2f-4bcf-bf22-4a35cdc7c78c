<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<link rel="icon" type="image/svg+xml" href="/vite.svg" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Vite App</title>
		<link rel="stylesheet" href="./plugins/fontawesome/css/fontawesome.min.css" />
		<link rel="stylesheet" href="./plugins/fontawesome/css/solid.css" />
		<link rel="stylesheet" href="style.css" />
	</head>
	<body id="menubar-body">
		<div class="window-header">
			<div class="l-box">
				<button class="line-wrap-btn active" type="button">
					<img src="/assets/wrap-off.svg" >
				</button>
			</div>
			<div class="r-box">
				<!-- <button type="button" id="split-doc">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 24 24"
						width="24"
						height="24"
						fill="none"
						stroke="#000"
						stroke-width="2"
						stroke-linecap="round"
						stroke-linejoin="round">
						<rect x="2" y="2" width="20" height="20" rx="2" />
						<line x1="2" y1="12" x2="22" y2="12" />
						<circle cx="8" cy="12" r="1" fill="#000000" />
						<circle cx="12" cy="12" r="1" fill="#000000" />
						<circle cx="16" cy="12" r="1" fill="#000000" />
					</svg>
				</button> -->
				<!-- <button type="button" id="toggle-theme"><i class="fa-solid fa-moon"></i></button> -->
			</div>
			<div class="split-dragger-box">
				<div class="split-dragger" id="split-thumb"></div>
			</div>
		</div>
		<div id="container">
			<div id="editor"></div>
		</div>
		<script type="module" src="./main.js" ></script>
	</body>
</html>
