import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';
import { filesListEl, foldersListEl, newDirsBtn, newFilesBtn, selFilesCheck, selFoldersCheck } from '../elements.js';
import { desktopDir } from '@tauri-apps/api/path';
import filesFoldersStorage, { getFileNameFromPath } from './files-folders-storage.js';
import elt from 'crelt';
import { type } from '@tauri-apps/plugin-os';
import { checkSelectedFilesStatus, getReviewData } from '../gitignore.js';
import { updateGroupsStatus } from '../../find-table.js';

let ignoredPaths = [];

export async function updateFilesFolderStatus() {
	ignoredPaths = await checkSelectedFilesStatus();
	await handlefilesList(false);
	await handleDirList(false);
	await updateGroupsStatus(ignoredPaths);
}

document.addEventListener('DOMContentLoaded', async () => {
	const paths = (await filesFoldersStorage.getFilesPaths()) ?? [];
	await updateFilesFolderStatus();
	//console.log(paths, selFilesCheck);
	selFilesCheck.checked = paths.length > 0;
});

async function openFiles() {
	const selected = await open({
		multiple: true,
	});

	return selected;
}

async function openDirs() {
	const selected = await open({
		directory: true,
		multiple: true,
	});

	return selected;
}

async function removeFilePath(e) {
	e.stopPropagation();
	const path = e.target.parentElement.getAttribute('title');
	await filesFoldersStorage.deletefilePath(path);
	await updateFilesFolderStatus();
}

export async function handlefilesList(update = true) {
	const paths = await filesFoldersStorage.getFilesPaths();
	filesListEl.innerHTML = '';
	if (paths && paths.length > 0) {
		paths.forEach(async (path) => {
			const isIgnored = ignoredPaths.includes(path);
			const p = await invoke('get_file_folder', { path });
			const item = elt('div', { class: 'list-item', title: path }, [
				elt('i', { class: 'fa-sharp fa-regular fa-xmark', onclick: removeFilePath }),
				elt('div', { class: isIgnored ? 'ignored' : '' }, [`${getFileNameFromPath(path)} - ${p}`]),
			]);
			filesListEl.appendChild(item);
		});
		if (update) selFilesCheck.checked = true;
	} else {
		selFilesCheck.checked = false;
	}
}

async function handleFiles(filesPath) {
	await filesFoldersStorage.saveFilesPath(filesPath);
	await updateFilesFolderStatus();
}

newFilesBtn.addEventListener('click', async () => {
	const filesPath = await openFiles();

	if (Array.isArray(filesPath)) {
		handleFiles(filesPath);
	} else if (filesPath !== null) {
		handleFiles([filesPath]);
	}
});

async function removeDirPath(e) {
	e.stopPropagation();
	const path = e.target.parentElement.getAttribute('title');
	await filesFoldersStorage.deleteDirPath(path);
	await updateFilesFolderStatus();
}

export async function handleDirList(update = true) {
	const slash = (await type()) == 'Windows_NT' ? '\\' : '/';
	const paths = await filesFoldersStorage.getDirsPaths();
	foldersListEl.innerHTML = '';
	if (paths && paths.length > 0) {
		paths.forEach(async (path) => {
			const p = await invoke('get_file_folder', { path });
			const isIgnored = ignoredPaths.includes(path);
			const item = elt('div', { class: 'list-item', title: path }, [
				elt('i', { class: 'fa-sharp fa-regular fa-xmark', onclick: removeDirPath }),
				elt('div', { class: isIgnored ? 'ignored' : '' }, [`${slash}${getFileNameFromPath(path)}${slash} - ${p}`]),
			]);
			foldersListEl.appendChild(item);
		});
		if (update) selFoldersCheck.checked = true;
	} else {
		selFoldersCheck.checked = false;
	}
}

async function handleDirs(dirsPath) {
	await filesFoldersStorage.saveDirsPath(dirsPath);
	await updateFilesFolderStatus();
}

newDirsBtn.addEventListener('click', async () => {
	const dirPaths = await openDirs();

	if (Array.isArray(dirPaths)) {
		handleDirs(dirPaths);
	} else if (dirPaths !== null) {
		handleDirs([dirPaths]);
	}
});
