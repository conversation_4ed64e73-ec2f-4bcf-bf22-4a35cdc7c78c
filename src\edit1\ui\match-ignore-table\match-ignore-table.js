import {
	Tabulator,
	SortModule,
	FormatModule,
	EditModule,
	FilterModule,
	ResizeColumnsModule,
	ResizeTableModule,
	InteractionModule,
	GroupRowsModule,
} from 'tabulator-tables';

import { emit, listen } from '@tauri-apps/api/event';
import { getCurrentWebviewWindow, WebviewWindow } from '@tauri-apps/api/webviewWindow';
import { Window } from '@tauri-apps/api/window';
const appWindow = getCurrentWebviewWindow();

function getStringBeforeMatchIgnore(inputString) {
	const matchIndex = inputString.indexOf('matchignore');
	if (matchIndex === -1) {
		// If "matchignore" is not found, return the original string
		return inputString;
	}
	return inputString.slice(0, matchIndex);
}

let data = [
	{
		search: false,
		flags: [],
		matched: '-',
		ignored: '-',
		extension: '',
		name: '',
		parent_folder: '',
		path: '',
	},
];

window.addEventListener('DOMContentLoaded', async () => {
	const parentLabel = getStringBeforeMatchIgnore(appWindow.label);
	await new Window(parentLabel).emit('find-table:get-review-data');
});

new Window(getStringBeforeMatchIgnore(appWindow.label)).listen('review:set-data', async (e) => {
	try {
		console.log('E', e);
		data = e.payload.data;
		console.log('DATA', data);
		await table.setData(data);
	} catch (error) {
		console.error(error);
	}
});

function extensionFormatter(cell, params) {
	const value = cell.getValue();
	if (value) return value;
	else return '/';
}

function searchFormatter(cell, params) {
	const value = cell.getValue();
	if (value) return 'yes';
	else return '-';
}

function flagsFormatter(cell, params) {
	const value = cell.getValue();
	return value.join(', ').toLowerCase();
}

function ignoreRowFormatter(row) {
	if (row.getData().search == '-') {
		row.getElement().style.color = '#ee4040';
	}
}

Tabulator.registerModule([
	SortModule,
	FormatModule,
	FilterModule,
	EditModule,
	ResizeColumnsModule,
	ResizeTableModule,
	InteractionModule,
	GroupRowsModule,
]);

var table = new Tabulator('#table', {
	autoColumns: false,
	autoResize: true,
	layoutColumnsOnNewData: true,
	layout: 'fitData',
	reactiveData: true,
	rowFormatter: ignoreRowFormatter,
	initialSort: [
		{ column: 'search', dir: 'desc' },
		{ column: 'parent_folder', dir: 'asc' },
		{ column: 'name', dir: 'asc' },
	],
	columns: [
		{
			title: 'Search',
			field: 'search',
			formatter: 'html',
			headerFilter: 'input',
			editorParams: {
				elementAttributes: {
					spellcheck: false,
					autocapitalize: false,
					autocorrect: false,
				},
			},
			headerSortTristate: true,
			width: 90,
		},
		{
			title: 'Flags',
			field: 'flags',
			formatter: flagsFormatter,
			headerFilter: 'input',
			editorParams: {
				elementAttributes: {
					spellcheck: false,
				},
			},
			headerSortTristate: true,
			width: 200,
		},
		{
			title: 'Extension',
			field: 'extension',
			formatter: extensionFormatter,
			headerFilter: 'input',
			editorParams: {
				elementAttributes: {
					spellcheck: false,
				},
			},
			headerSortTristate: true,
			width: 120,
		},
		{
			title: 'Name',
			field: 'name',
			formatter: 'html',
			headerFilter: 'input',
			editorParams: {
				elementAttributes: {
					spellcheck: false,
				},
			},
			headerSortTristate: true,
			width: 300,
		},
		{
			title: 'Folder',
			field: 'parent_folder',
			formatter: 'html',
			headerFilter: 'input',
			editorParams: {
				elementAttributes: {
					spellcheck: false,
				},
			},
			headerSortTristate: true,
			widthGrow: 600,
		},
	],
});
