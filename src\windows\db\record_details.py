"""
record_details.py - details window for SQLite DB records

SEE ALSO: /window/row_details.py for csv/txt files though the code is very different
	e.g. this has ForeignKey info
"""

from browser import aio, console, document, window
from functools import lru_cache
from typing import List
import copy
import javascript
import numbers

# local to load first
import page_runner
import tauri

# local imports
from file_sqlite import Database
import db_utils
import deeply_utils
import shortcuts  # runs the bind commands on import
import table_utils


PROPS = None
try:
	PROPS = dict(window['__PROPS__'])
	PATH = PROPS['path']
	table_name = PROPS['table']  # TBD: change name in Rust
	PRIMARY_KEY = PROPS['primary_key']
	CREATING_NEW_ROW = PROPS.get('is_new_row', False)
	ROW_ID = PROPS.get('row_id')
except Exception as e:
	print('ERROR in record_details.py; could not get PROPS', e)
	pass

ROW: dict = None
PREV_ROW: dict = None
TABLE_INFO = None
PRIMARY_KEY: str = None
FK_INFO = None
DB = None

# refactor TBD: if this is useful then call now_string or similar shared function
# e.g. this date code looks wrong!
# DEFAULT_VALUES = {
# 	'INTEGER': 0,
# 	'TEXT': '',
# 	'BLOB': window.Uint8Array.new(0),
# 	'REAL': 0.0,
# 	'NUMERIC': 0,
# 	'DATE': '',
# 	'JSON': '',
# 	'boolean': 1,
# 	'date': datetime.now().replace(tzinfo=timezone.utc).strftime('%Y-%m-%d %H:%M:%S %p'),
# 	'TIMESTAMP': datetime.now().replace(tzinfo=timezone.utc).strftime('%Y-%m-%d %H:%M:%S %p'),
# }

DEFAULT_TYPES = {
	'INTEGER': int,
	'TEXT': str,
	'REAL': float,
	'NUMERIC': int,
	'DATE': str,
	'JSON': str,
	'boolean': bool,
	'date': str,
	'TIMESTAMP': str,
}

# -----
async def get_adjacent_id(is_direction_forward: bool):
	global PRIMARY_KEY, table_name, ROW_ID, DB

	fn = 'LEAD' if is_direction_forward else 'LAG'
	query = f'SELECT * FROM (SELECT *, {fn}({PRIMARY_KEY}) OVER (ORDER BY {PRIMARY_KEY}) AS adjacent_id FROM "{table_name}") WHERE {PRIMARY_KEY} = {ROW_ID}'
	resp = await DB.select(query, [])
	row = dict(resp[0])
	adjacent_id = row['adjacent_id']
	if adjacent_id:
		return adjacent_id
	# get the next / prev circular (last / first)
	query = f'SELECT {PRIMARY_KEY} FROM {table_name} ORDER BY {PRIMARY_KEY} LIMIT 1'
	if not is_direction_forward:
		query = query.replace(f'ORDER BY {PRIMARY_KEY}', f'ORDER BY {PRIMARY_KEY} DESC')
	resp = await DB.select(query, [])
	row = dict(resp[0])
	return row[PRIMARY_KEY]


async def get_adjacent_row(is_forward: bool):
	global ROW_ID, PRIMARY_KEY, ROW, PREV_ROW

	if not await check_unsaved_changes():
		return
	ROW_ID = await get_adjacent_id(is_forward)
	ROW = await load_row()
	PREV_ROW = copy.copy(ROW)
	await render_row()
	save_btn = document.querySelector('#save')
	save_btn.innerText = 'Save'
	save_btn.classList.remove('active')


async def get_fk_rows_info(db, name) -> dict:
	# TBD: document this vs lesser code in db_data.py
	query = f"PRAGMA foreign_key_list('{name}')"
	rows = await db.select(query, [])
	data = {}
	for row in rows:
		from_field = row['from']
		data[from_field] = dict(row)
	return data


async def load_row():
	global PRIMARY_KEY, table_name, ROW_ID, DB
	rows = await DB.select(f"SELECT * from '{table_name}' where {PRIMARY_KEY} = '{ROW_ID}'", [])
	return dict(rows[0])


def get_count_diff():
	global ROW, PREV_ROW
	count = 0
	for i in range(len(ROW)):
		original_row = dict(PREV_ROW[i])
		current_row = dict(ROW[i])

		if current_row.get('_meta_src_value') != original_row.get('_meta_src_value'):
			count += 1
		elif str(original_row['Value']) != str(current_row['Value']):
			count += 1
	return count


def get_changes_count():
	global ROW, PREV_ROW
	count = 0
	for key in ROW.keys():
		if str(ROW[key]) != str(PREV_ROW[key]):
			count += 1
	return count


def on_field_change(name: str, new_value: str):
	# print('on field change', name, new_value)
	global ROW, PREV_ROW

	# Update current ROW
	ROW[name] = new_value

	change_count = get_changes_count()

	# Update save button
	save_btn = document.querySelector('#save')
	if change_count > 0 and not CREATING_NEW_ROW:
		save_btn.innerText = f'Save ' + deeply_utils.plural_phrase(change_count, 'changes')
		save_btn.classList.add('active')
	elif change_count > 0 and CREATING_NEW_ROW:
		save_btn.classList.add('active')
	else:
		save_btn.innerText = 'Save'
		save_btn.classList.remove('active')


async def create_edit_field(key, value):
	# refactor TBD: try to use same code as in row_details.py
	# print('\n\n create_edit_field key =', key, '\n\n')
	textarea = document.createElement('textarea')
	textarea.setAttribute('spellcheck', 'false')
	input_el = document.createElement('input')
	input_el.setAttribute('spellcheck', 'false')

	if value is javascript.NULL:
		value = ''

	if str(key).lower() in table_utils.textarea_editable:
		textarea.setAttribute('class', 'width100')
		textarea.setAttribute('name', key)
		textarea.value = value
		return textarea

	elif str(key).lower() in table_utils.not_editable:
		input_el.setAttribute('name', key)
		input_el.value = value
		input_el.setAttribute('readonly', 'true')
		return input_el


	elif str(key).lower() in table_utils.wide_editable:
		input_el.setAttribute('name', key)
		input_el.setAttribute('class', 'width100')
		input_el.value = value
		return input_el

	elif str(key).lower() in table_utils.blob_fields:
		span = document.createElement('span')
		span.setAttribute('class', 'blob')
		span.innerHTML = '(BLOB)'
		return span

	else:
		input_el.setAttribute('name', key)
		input_el.value = value
		return input_el


@lru_cache
async def get_fk_rows(table: str) -> List[dict]:
	return await DB.select(f'SELECT * FROM {table}', [])


async def render_row():
	# refactor TBD: move the ForeignKey special case to a separate function that this calls
	# ... then call create_edit_field() from row_details.py to handle other fields
	# ... well, move that to a shared detail_utils.py or some such
	global ROW, PRIMARY_KEY

	# CROSS-REF: display_data() in row_details.py vs. render_row() in record_details.py

	table = document.querySelector('#table')
	table.innerHTML = ''  # Clear the existing content

	# Headers
	header = document.createElement('tr')
	for h in ['Name', 'Value']:
		th = document.createElement('th')
		th.textContent = h
		header.appendChild(th)
	table.appendChild(header)

	# compute max width based on longest key
	max_len = max((len(k) for k in ROW.keys()), default=0)
	width_px = max_len * 10  # 10px for bold chars

	for key, value in ROW.items():
		# print('key => ', key)
		tr = document.createElement('tr')

		name_td = document.createElement('td')
		name_td.textContent = key
		name_td.classList.add('fieldname')
		name_td.style.width = f'{width_px}px'

		value_td = document.createElement('td')
		fk_rows = None
		fk_info = FK_INFO.get(key)

		if TABLE_INFO[key]['type'] == 'BLOB':
			value_td.innerText = '(BLOB)'
			value_td.classList.add('blob')
		elif fk_info:
			fk_rows = await DB.select(f"SELECT * FROM {fk_info['table']}", [])
			friendly_columns = await db_utils.get_friendly_fk_columns(DB, fk_info['table'])
			# print('friendly', friendly_columns)
			friendly_column = None
			if friendly_columns:
				friendly_column = friendly_columns[0]

			# may let users customize in the future
			max_menu_items = 60
			max_radios = 7

			# DOM element: input
			if len(fk_rows) in [0, 1] or len(fk_rows) > max_menu_items:
				# refactor TBD: move these out of the deeply-nested context (but may need testing)
				def on_change(event):
					on_field_change(event.target.name, event.target.value)

				def on_focus(event, value):
					changed_value = ROW.get(event.target.name)
					if changed_value:
						event.target.value = changed_value
					else:
						event.target.value = value

				def on_blur(event, fk_info, fk_rows, friendly_columns, value_input):
					fk_row = next((i for i in fk_rows if str(i[fk_info['to']]) == event.target.value), None)
					if fk_row:
						value_input.value = ' '.join(fk_row[i] for i in friendly_columns)
				# ^^^^^ refactor TBD: move the above

				value_input = document.createElement('input')
				value_input.setAttribute('spellcheck', 'false')

				fk_row = next((i for i in fk_rows if i[fk_info.get('to', 0)] == value), None)
				if fk_row:
					value_input.value = ' '.join(fk_row[i] for i in friendly_columns)
				elif value is javascript.NULL:
					value_input.value = ''  # TBD: this did NOT fix JS null, e.g. in Northwind DB suppliers FAX
				else:
					value_input.value = value

				value_input.setAttribute('name', key)
				value_input.addEventListener('change', lambda e: on_change(e))
				value_input.addEventListener('focus', lambda e, value=value: on_focus(e, value))
				# print(f'define on blur for {key} with {fk_info}')
				value_input.addEventListener('blur', lambda e, fk_info=fk_info, fk_rows=fk_rows, friendly_columns=friendly_columns, value_input=value_input: on_blur(e, fk_info, fk_rows, friendly_columns, value_input))
				value_td.appendChild(value_input)

			# DOM element: radio buttons
			elif len(fk_rows) <= max_radios:
				def on_change(event):
					on_field_change(event.target.name, event.target.value)

				fieldset = document.createElement('fieldset')
				fieldset.addEventListener('change', on_change)
				fieldset.classList.add('generated')  # add input/label padding with CSS

				if len(fk_rows) == 2:
					# put binary choice on same line
					same_line = True
					container = fieldset
				else:
					# >2 radio buttons are easier with each on its own line
					same_line = False

				for row in fk_rows:
					if not same_line:
						# wrap each pair
						container = document.createElement('div')
						container.classList.add('labelled-pairs')

					fieldname = fk_info['to']

					label = document.createElement('label')
					label.innerText = ' '.join(row[i] for i in friendly_columns)
					label.setAttribute('for', row[fieldname])  # label for each radio
					label.classList.add('suffix-label')  # needs padding before

					input = document.createElement('input')
					input.setAttribute('type', 'radio')
					input.setAttribute('id', row[fieldname])  # id for each radio
					input.setAttribute('name', fk_info['from'])
					input.value = row[fieldname]
					input.checked = row[fieldname] == value

					container.appendChild(input)  # radio first
					container.appendChild(label)  # label next

					if not same_line:
						fieldset.appendChild(container)

				value_td.appendChild(fieldset)

			# DOM element: select dropdown
			else:
				def on_change(event):
					on_field_change(event.target.name, event.target.value)

				fieldname = fk_info['to']

				label = document.createElement('label')
				label.setAttribute('for', fieldname)  # single label for entire drop-down

				select = document.createElement('select')
				select.value = value
				select.setAttribute('name', key)
				select.setAttribute('id', key)  # single id for entire drop-down
				select.addEventListener('change', on_change)

				for row in fk_rows:
					# print('fk row => ', row, fieldname)
					option = document.createElement('option')
					option.value = row[fieldname]
					option.innerText = ' '.join(row[i] for i in friendly_columns)
					option.selected = row[fieldname] == value
					select.appendChild(option)

				value_td.appendChild(label)
				value_td.appendChild(select)

		else:
			value_input = await create_edit_field(key, value)
			def on_change(event):
				on_field_change(event.target.name, event.target.value)

			value_input.addEventListener('change', on_change)
			value_td.appendChild(value_input)

		tr.appendChild(name_td)
		tr.appendChild(value_td)
		table.appendChild(tr)


async def save_row() -> bool:
	"""
	Update rows in db when clicking save (from close dialog / save button)
	"""
	# refactor TBD: move all sql into new wrapper in file_sqlite.py
	# e.g. user will be able to edit in table view OR detail view

	global CREATING_NEW_ROW, ROW_ID, ROWS, DB, PREV_ROW, ROW, TABLE_INFO, table_name, PRIMARY_KEY

	data = {}
	for key, value in ROW.items():

		if PREV_ROW[key] == value and not CREATING_NEW_ROW:
			continue
		if PRIMARY_KEY == key and CREATING_NEW_ROW and value == '':
			continue
		field_type = dict(TABLE_INFO.get(key, {})).get('type')
		if field_type == 'NVARCHAR(120)' or field_type == 'VARCHAR':
			field_type = 'TEXT'
		# print('save_row: field_type', field_type)
		if field_type:
			if DEFAULT_TYPES[field_type] in [int, float] and value is not '' and not isinstance(int(value), numbers.Number):
				# print('save_row: value', value)
				await tauri.dialog.message(
					f'Invalid type on {key}. should be numeric.',
					{'title': 'Save Error', 'type': 'error'}
				)
				return False
			if value is not '':
				data[key] = DEFAULT_TYPES[field_type](value)
			else:
				data[key] = None

		else:
			data[key] = value

	if CREATING_NEW_ROW:
		column_names = ', '.join(data.keys())
		placeholders = ', '.join(['?'] * len(column_names))
		query = f'INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})'
		parameters = list(data.values())
	else:
		set_clause = ', '.join([f'{key} = ?' for key in data.keys()])
		query = f'UPDATE {table_name} SET {set_clause} WHERE {PRIMARY_KEY} = ?'
		parameters = list(data.values())
		parameters.append(ROW_ID)

	try:
		# print('query', DB.path, query, parameters)
		# print('save_row: parameters', parameters)
		result = await DB.execute(query, parameters)
		if result.get('rows_affected', 0) == 0:
			raise Exception('ERROR in save_row')  # trigger the code below
		# TBD: call shared code to record this edit
	except Exception as e:
		console.error('e => ', e)
		await tauri.dialog.message(
			'Failed to update record in database. Maybe invalid foreign key?',
			{'title': 'Save Error', 'type': 'error'}
		)
		return False

	await tauri.event.emit(f'table_{table_name}_update')
	return True


async def on_save_rows_click():
	global ROW
	success = await save_row()
	if success:
		current_window = tauri.window.getCurrentWindow()
		current_window.destroy()
	else:
		print('ERROR in on_save_rows_click; save_row apparently failed')


async def check_unsaved_changes():
	# refactor TBD: details.py & row_details.py should call a common function -- but needs work
	global CREATING_NEW_ROW

	changes_count = get_changes_count()
	if changes_count == 0:
		return True

	message = table_utils.make_changed_message(changes_count, CREATING_NEW_ROW=CREATING_NEW_ROW)
	save_changes = await tauri.dialog.confirm(message, {'cancelLabel': 'Discard', 'okLabel': 'Save'})
	if save_changes:
		success = await save_row()
		if not success:
			return False
	return True


async def get_table_info():
	info = await DB.select(f"PRAGMA table_info('{table_name}')", [])
	info_dict = {}
	for i in info:
		info_dict[i['name']] = i
	return info_dict


async def main():
	global app_config
	app_config = await window.get_app_config()
	table_utils.app_config = app_config  # must be async so cannot be set on import

	# refactor TBD: don't use CAPS for variables that will change e.g. ROW & many/most others
	global ROW, TABULATOR, TABLE_INFO, DB, FK_INFO, PREV_ROW, PRIMARY_KEY

	# documentation TBD: which windows need this and why?
	await table_utils.set_title()
	tauri.event.listen(f'title_update', lambda _: aio.run(table_utils.set_title()))

	db = await Database.load(f'sqlite:{PATH}')  # sometimes our SQLite DB; sometimes the customer's
	DB = db  # refactor TBD: get rid of this redundancy!
	TABLE_INFO = await get_table_info()
	FK_INFO = await get_fk_rows_info(db, table_name)

	PRIMARY_KEY = [i for i in TABLE_INFO.values() if i['pk'] == 1][0]['name']

	if CREATING_NEW_ROW:
		button_label = 'Save New Record'
		ROW = {name: '' for name in TABLE_INFO.keys()}
	else:
		button_label = 'Save Changes'  # TBD: does this accidentally change to 'Save' on prev/next?
		ROW = await load_row()

	document.querySelector('#save').innerHTML = button_label
	PREV_ROW = copy.copy(ROW)
	await render_row()

# 	help_btn = document.querySelector('#header-help')
	next_btn = document.querySelector('#navbar-next')
	prev_btn = document.querySelector('#navbar-prev')
	save_btn = document.querySelector('#save')

# 	help_btn.bind('click', lambda _: aio.run(table_utils.open_help_window()))
	next_btn.bind('click', lambda _: aio.run(get_adjacent_row(True)))
	prev_btn.bind('click', lambda _: aio.run(get_adjacent_row(False)))
	save_btn.bind('click', lambda _: aio.run(on_save_rows_click()))

	current_window = tauri.window.getCurrentWindow()
	current_window.listen('tauri://close-requested', lambda _: aio.run(on_window_close()))


async def on_window_close():
	if not await check_unsaved_changes():
		return
	await table_utils.close_window()  # after emit file_tables_update

page_runner.register_for_js()  # hardcoded for 'main'
