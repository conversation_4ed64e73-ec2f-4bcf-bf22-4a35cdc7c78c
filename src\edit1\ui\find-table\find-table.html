<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Matches (Find All)</title>
		<link href="../../../../node_modules/tabulator-tables/dist/css/tabulator_bulma.min.css" rel="stylesheet" />
		<link rel="stylesheet" href="../../plugins/fontawesome/css/fontawesome.min.css" />
		<link rel="stylesheet" href="../../plugins/fontawesome/css/solid.css" />

		<link rel="stylesheet" href="../../style/makedeeply.css" />
		<link rel="stylesheet" href="../../style/theme.css" />
		<!-- <link rel="stylesheet" href="./style/find-replace-expand.css" /> -->
		<link rel="stylesheet" href="./style/target-opts-expand.css" />
		<link rel="stylesheet" href="./style/filters-opts-expand.css" />
		<link rel="stylesheet" href="/edit1/ui/style/find-dialog-and-table.css" />
		<link rel="stylesheet" href="find-table.css" />
	</head>
	<body id="find-table" class="dialog">
		<div id="search" class="search-tab">
			<form id="finder-form">
				<div class="input-container">
					<div class="box find-box">
						<div class="input-box">
							<label for="find-value" id="find-value-label" tabindex="-1">Find</label>
							<textarea name="find" id="find-value" spellcheck="false"></textarea>
						</div>
					</div>
					<div class="box replace-box">
						<div class="input-box">
							<label for="replace-value" tabindex="-1">Replace</label>
							<textarea name="replace" id="replace-value" spellcheck="false"></textarea>
						</div>
					</div>
				</div>
			</form>

			<div class="options-box">
				<div class="opts">
					<div class="matching-opts checkbox-row">
						<label class="group-label">Matching:</label>
						<div class="opt">
							<input type="checkbox" name="case_sensitive" id="case-sensitive" tabindex="-1" />
							<label for="case-sensitive">Case sensitive</label>
						</div>
						<div class="opt">
							<input tabindex="-1" type="checkbox" name="entire_word" id="entire-word" />
							<label for="entire-word">Entire word</label>
						</div>
						<div class="opt">
							<input tabindex="-1" type="checkbox" name="grep" id="grep" checked />
							<label for="grep">Regex</label>
						</div>
					</div>
				</div>
				<div class="btns-box">
					<button type="button" id="tabulate-btn" tabindex="-1">Tabulate</button>
				</div>
			</div>
			<div id="resizer">
				<img src="../../../assets/drag-up-down.svg" alt="finder resizer" width="20" height="20" />
			</div>
			<div class="opts2">
				<div class="bottom-opts" id="scroll-to">
					<!-- Target Section -->
					<input type="checkbox" name="target-opts-expand" id="target-opts-expand" style="display: none" />
					<div class="target-opts checkbox-row">
						<div class="name">
							<label for="target-opts-expand" class="section-state">
								<div class="right">
									<i class="fa-solid fa-chevron-right"></i>
								</div>
								<div class="down">
									<i class="fa-solid fa-chevron-down"></i>
								</div>
							</label>
							<label>Target:</label>
						</div>
						<div class="row-2 checkbox-row">
							<div class="opt">
								<input type="checkbox" name="target" id="open-docs" tabindex="-1" />
								<label for="open-docs">Open documents</label>
							</div>
							<div class="opt with-list">
								<div class="check">
									<input tabindex="-1" type="checkbox" name="target" id="selected-files" checked />
									<label for="selected-files">Selected files</label>
									<button id="new-files">
										<i class="fa-solid fa-plus"></i>
									</button>
								</div>
								<div class="list" id="files-list"></div>
							</div>
							<div class="opt with-list">
								<div class="check">
									<input tabindex="-1" type="checkbox" name="target" id="selected-folders" />
									<label for="selected-folders">Selected folders</label>
									<button id="new-dirs">
										<i class="fa-solid fa-plus"></i>
									</button>
								</div>
								<div class="list" id="folders-list"></div>
							</div>
						</div>
						<div class="target-btns">
							<button id="history-btn">History</button>
						</div>
					</div>
					<span class="divider"></span>
					<!-- Filters Section -->
					<input type="checkbox" name="filters-opts-expand" id="filters-opts-expand" style="display: none" />
					<div class="filters-opts checkbox-row">
						<div class="name">
							<label for="filters-opts-expand" class="section-state">
								<div class="right">
									<i class="fa-solid fa-chevron-right"></i>
								</div>
								<div class="down">
									<i class="fa-solid fa-chevron-down"></i>
								</div>
							</label>
							<label>Filters:</label>
						</div>
						<div class="filters">
							<div class="inputs-area">
								<div class="input-box">
									<label for="gitMatchArea">Match</label>
									<textarea name="gitmatch-area" id="gitMatchArea" rows="5" spellcheck="false"></textarea>
								</div>
								<div class="input-box">
									<label for="gitIgnoreArea">Ignore</label>
									<textarea name="gitignore-area" id="gitIgnoreArea" rows="5" spellcheck="false"></textarea>
								</div>
							</div>
							<div class="checkbox-area">
								<div class="checkbox">
									<input type="checkbox" name="use-gitignore" id="useGitIgnoreCheck" checked />
									<label for="useGitIgnoreCheck">use .gitignore</label>
								</div>
								<div class="checkbox">
									<input type="checkbox" name="omit-invisible-files" id="omitInvisibleFilesCheck" checked />
									<label for="omitInvisibleFilesCheck">omit invisible files</label>
								</div>
								<div class="checkbox">
									<input type="checkbox" name="use-gitignore" id="omitInvisibleFoldersCheck" checked />
									<label for="omitInvisibleFoldersCheck">omit invisible folders</label>
								</div>
							</div>
						</div>
						<div class="target-btns">
							<button id="review-btn">Review</button>
						</div>
					</div>
					<span class="divider"></span>
					<!-- Replace In section -->
					<div class="replace-in-box checkbox-row">
						<label>Replace in:</label>
						<div class="opt">
							<input type="checkbox" name="before" id="before-check" tabindex="-1" checked />
							<label for="before-check">Before (<span id="before-count">0</span>)</label>
						</div>
						<div class="opt">
							<input tabindex="-1" type="checkbox" name="selected" id="selected-check" checked />
							<label for="selected-check">Selected (<span id="selected-count">0</span>)</label>
						</div>
						<div class="opt">
							<input tabindex="-1" type="checkbox" name="after" id="after-check" checked />
							<label for="after-check">After (<span id="after-count">0</span>)</label>
						</div>
					</div>
					<div class="replace-set-box">
						<button type="button" id="replace-current-set" tabindex="-1">Replace Current Set</button>
					</div>
				</div>
			</div>
		</div>

		<div id="table"></div>
	</body>
	<script type="module" src="find-table.js" defer></script>
</html>
