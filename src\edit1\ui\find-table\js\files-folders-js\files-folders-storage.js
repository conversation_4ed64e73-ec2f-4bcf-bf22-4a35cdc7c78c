import TableStorage from '../storage';

async function saveFilesPath(paths) {
	if (paths.length > 0) {
		const hasFiles = await TableStorage.store.has('filesPaths');
		if (hasFiles) {
			const oldFiles = await TableStorage.store.get('filesPaths');
			const newPaths = paths.filter((path) => !oldFiles.includes(path));
			//console.log(newPaths);
			if (newPaths.length > 0) TableStorage.store.set('filesPaths', [...newPaths, ...oldFiles]);
		} else {
			TableStorage.store.set('filesPaths', paths);
		}
		TableStorage.store.save();
	}
}

async function saveDirsPath(paths) {
	if (paths.length > 0) {
		const hasDirs = await TableStorage.store.has('dirsPaths');
		if (hasDirs) {
			const oldDirs = await TableStorage.store.get('dirsPaths');
			const newPaths = paths.filter((path) => !oldDirs.includes(path));
			//console.log(newPaths);
			if (newPaths.length > 0) TableStorage.store.set('dirsPaths', [...newPaths, ...oldDirs]);
		} else {
			TableStorage.store.set('dirsPaths', paths);
		}
		TableStorage.store.save();
	}
}

function getFilesPaths() {
	return TableStorage.store.get('filesPaths');
}

function getDirsPaths() {
	return TableStorage.store.get('dirsPaths');
}

async function deletefilePath(path) {
	const paths = await getFilesPaths();
	let newPaths = paths.filter((p) => !p.includes(path));
	TableStorage.store.set('filesPaths', newPaths);
	TableStorage.store.save();
}

async function deleteDirPath(path) {
	const paths = await getDirsPaths();
	let newPaths = paths.filter((p) => !p.includes(path));
	TableStorage.store.set('dirsPaths', newPaths);
	TableStorage.store.save();
}

export function getFileNameFromPath(path) {
	const pathComponents = path.split('/');
	const file_name = pathComponents.pop();
	return file_name;
}

export default {
	saveFilesPath,
	getFilesPaths,
	deletefilePath,
	saveDirsPath,
	getDirsPaths,
	deleteDirPath,
};
