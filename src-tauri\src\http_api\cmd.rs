use crate::{
    cmd::window::{self, NewWindowOptions},
    utils::{eval_with_event, random_string},
};
use axum::{extract::State, Json};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::time::Duration;
use tauri::Manager;

// Handlers
#[derive(Debug, Deserialize)]
pub struct CreateWindowRequest {
    url: String,
}

#[derive(Debug, Serialize)]
pub struct CreateWindowResponse {
    label: String,
}

// TBD: document this (purpose & how it relates to the create_window that it calls)
pub async fn create_window(
    State(app_handle): State<tauri::AppHandle>,
    Json(payload): Json<CreateWindowRequest>,
) -> Result<Json<CreateWindowResponse>, ()> {
	let label = random_string(8);
	window::create_window(
		app_handle,
		format!("{} {}", label, payload.url),
		label.clone(),
		payload.url,
		NewWindowOptions {
			..Default::default()
		},
		false, // not a data window
		None,
		None
	)
	.await;
	let body = Json(CreateWindowResponse { label });
	Ok(body)
}

#[derive(Debug, Deserialize)]
pub struct GetOuterHTMLRequest {
    label: String,
}

#[derive(Debug, Serialize)]
pub struct GetOuterHTMLResponse {
    html: String,
}

pub async fn get_outer_html(
    State(app_handle): State<tauri::AppHandle>,
    Json(payload): Json<GetOuterHTMLRequest>,
) -> Result<Json<Value>, ()> {
    let webview = app_handle.get_webview(&payload.label);
    if let Some(webview) = webview {
        let result = eval_with_event(
            &webview,
            "OUTER_HTML",
            r#"
		(() => {
			const html = document.documentElement.outerHTML
			window.__TAURI__.event.emit("OUTER_HTML", {html})
		})()
		"#,
            Duration::from_secs(3),
        )
        .await;
        if let Ok(value) = result {
            let body = Json(value);
            return Ok(body);
        }
        if let Err(err) = result {
            log::error!("eval log::error! {:?}", err);
            // return axum error with text
        }
    } else {
        // return axum error
        log::error!("no such webview {:?}!", payload.label);
    }
    let body = Json(json!({}));
    Ok(body)
}

#[derive(Debug, Deserialize)]
pub struct WaitForDocumentRequest {
    label: String,
}
pub async fn wait_for_document(
    State(app_handle): State<tauri::AppHandle>,
    Json(payload): Json<WaitForDocumentRequest>,
) -> Result<Json<Value>, ()> {
    let webview = app_handle.get_webview(&payload.label);
    if let Some(webview) = webview {
        let result = eval_with_event(
            &webview,
            "READY_STATE",
            r#"
		(async () => {
			while (document.readyState === "loading") {
				console.log('pause');
				await new Promise(resolve => setTimeout(resolve, 1000));
			}
			window.__TAURI__.event.emit("READY_STATE", {status: "ready"})
		})()
		"#,
            Duration::from_secs(10),
        )
        .await;
        if let Ok(value) = result {
            let body = Json(value);
            return Ok(body);
        }
        if let Err(err) = result {
            log::error!("eval log::error! {:?}", err);
            // return axum error with text
        }
    } else {
        // return axum error
        log::error!("no such webview {:?}!", payload.label);
    }
    let body = Json(json!({}));
    Ok(body)
}

#[derive(Debug, Deserialize)]
pub struct SetVisibleRequest {
    label: String,
    visible: bool,
}
pub async fn set_visible(
    State(app_handle): State<tauri::AppHandle>,
    Json(payload): Json<SetVisibleRequest>,
) -> Result<Json<Value>, String> {
    let window = app_handle.get_window(&payload.label);
    if let Some(window) = window {
        if payload.visible {
            window.show().map_err(|e| e.to_string())?;
        } else {
            window.hide().map_err(|e| e.to_string())?;
        }
    } else {
        // return axum error
        log::error!("no such window {:?}!", payload.label);
        return Err("no such window".into());
    }
    let body = Json(json!({}));
    Ok(body)
}

#[derive(Debug, Deserialize)]
pub struct CloseWindowRequest {
    label: String,
}
pub async fn close_window(
    State(app_handle): State<tauri::AppHandle>,
    Json(payload): Json<CloseWindowRequest>,
) -> Result<Json<Value>, String> {
    let window = app_handle.get_window(&payload.label);
    if let Some(window) = window {
        window.close().map_err(|e| e.to_string())?;
    } else {
        // return axum error
        log::error!("no such window {:?}!", payload.label);
        return Err("no such window".into());
    }
    let body = Json(json!({}));
    Ok(body)
}

#[derive(Debug, Deserialize)]
pub struct SetFocusRequest {
    label: String,
    focus: bool,
}
pub async fn set_focus(
    State(app_handle): State<tauri::AppHandle>,
    Json(payload): Json<SetFocusRequest>,
) -> Result<Json<Value>, String> {
    let window = app_handle.get_window(&payload.label);
    if let Some(window) = window {
        if payload.focus {
            window.show().map_err(|e| e.to_string())?;
            window.set_focus().map_err(|e| e.to_string())?;
        } else {
            window.minimize().map_err(|e| e.to_string())?;
        }
    } else {
        // return axum error
        log::error!("no such window {:?}!", payload.label);
        return Err("no such window".into());
    }
    let body = Json(json!({}));
    Ok(body)
}

#[derive(Debug, Deserialize)]
pub struct GetLocationRequest {
    label: String,
}

pub async fn get_location(
    State(app_handle): State<tauri::AppHandle>,
    Json(payload): Json<GetLocationRequest>,
) -> Result<Json<Value>, ()> {
    let webview = app_handle.get_webview(&payload.label);
    if let Some(webview) = webview {
        let result = eval_with_event(
            &webview,
            "LOCATION",
            r#"
		(() => {
			const location = document.location
			window.__TAURI__.event.emit("LOCATION", {location: document.location})
		})()
		"#,
            Duration::from_secs(3),
        )
        .await;
        if let Ok(value) = result {
            let body = Json(value);
            return Ok(body);
        }
        if let Err(err) = result {
            log::error!("eval log::error! {:?}", err);
            // return axum error with text
        }
    } else {
        // return axum error
        log::error!("no such webview {:?}!", payload.label);
    }
    let body = Json(json!({}));
    Ok(body)
}

pub async fn is_focused(
    State(app_handle): State<tauri::AppHandle>,
    Json(payload): Json<Value>,
) -> Result<Json<Value>, String> {
    let label = payload["label"].as_str().unwrap_or_default();
    let window = app_handle.get_window(label);
    if let Some(window) = window {
        let is_focused = window.is_focused().map_err(|e| e.to_string())?;
        return Ok(Json(json!({"is_focused": is_focused})));
    } else {
        // return axum error
        log::error!("no such webview {:?}!", label);
    }
    let body = Json(json!({}));
    Ok(body)
}
