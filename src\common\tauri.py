"""
tauri.py - wraps window['__TAURI__']
"""

from browser import aio, window

tauri = window['__TAURI__']
dialog = tauri.dialog
event = tauri.event
invoke = tauri.core.invoke
webview = tauri.webview
window = tauri.window

convertFileSrc = tauri.core.convertFileSrc  # documentation TBD: summarize


''' (from <PERSON>; I didn't cross-check)

macOS:
- app_config_dir = ~/Library/Application Support/YourApp/
- app_data_dir = ~/Library/Application Support/YourApp/ -- i.e. SAME on Mac but not Windows
- app_cache_dir = ~/Library/Caches/YourApp/ -- CONSIDER using this
- app_log_dir = ~/Library/Logs/YourApp/

Windows:
- app_config_dir = %APPDATA%\YourApp\config\
- app_data_dir = %APPDATA%\YourApp\data\
- app_cache_dir = %LOCALAPPDATA%\YourApp\cache\
- app_log_dir = %LOCALAPPDATA%\YourApp\logs\

Linux:
- app_config_dir = ~/.config/YourApp/
- app_data_dir = ~/.local/share/YourApp/
- app_cache_dir = ~/.cache/YourApp/
- app_log_dir = ~/.local/share/YourApp/logs/
'''
