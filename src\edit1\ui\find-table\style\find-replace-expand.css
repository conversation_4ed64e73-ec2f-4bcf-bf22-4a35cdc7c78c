#find-replace-expand:checked ~ form > .section-state > .down {
	display: block;
}

#find-replace-expand:not(:checked) ~ form > .section-state > .down {
	display: none;
}

#find-replace-expand:checked ~ form > .section-state > .right {
	display: none;
}

#find-replace-expand:not(:checked) ~ form > .section-state > .right {
	display: block;
}

#find-replace-expand:not(:checked) ~ form#finder-form {
	display: flex;
	flex-direction: row;
	gap: 8px;
	width: 100%;
	height: fit-content;
}

#find-replace-expand:not(:checked) ~ form > .input-container {
	display: flex;
	flex-direction: row;
	width: 100%;
	height: fit-content;
	gap: 8px;
}

#find-replace-expand:not(:checked) ~ form > .input-container > .box > .input-box {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	width: 100%;
	gap: 4px;
}
#find-replace-expand:not(:checked) ~ form > .input-container > .box > .input-box > textarea {
	width: 100%;
	height: 28px;
}
