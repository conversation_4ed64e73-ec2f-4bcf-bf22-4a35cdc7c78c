/* makedeeply.css */

/* copy/paste makedeeply.css & responsive.css */
:root {
	--titlebar-height: 36px;
	--deeper_blue: hsl(228, 33%, 25%);
	--deep_blue: #182869;
	--mid_blue: #578bc1;
	--pale_blue: hsl(211, 54%, 90%);
	--paler_blue: #e9eef9;

	--titlebar_light_mode: #e0eeff;

	--deep_green: #172;
	--mid_green: #4b4;
	--pale_green: #bfe8b7;
	--paler_green: #eef9f1;

	--deep_red: #700;

	--dark_gray: #555;
	--mid_gray: #888;
	--pale_gray: #ddd;

	--deep_brown: hsl(33, 35%, 37%);
	--mid_brown: hsl(33, 40%, 47%);
	--pale_brown: hsl(33, 45%, 57%);

	--pale_tan: hsl(33, 55%, 77%);
	--paler_tan: hsl(33, 75%, 87%);

	--royal_purple: #7851a9;
	--light_royal_purple: #a580b7;
	--lighter_royal_purple: #b799c5;

	/* mostly derived: light mode */
	--button_color: var(--deep_blue);
	--button_text_color: white;
	--icon_color: var(--deep_blue);
	--post_hover: #f2fcf5;
	/* very light green */
	--text_color: var(--deep_blue);
	--textbox_blue: var(--titlebar_light_mode);
	--titlebar_blue: var(--titlebar_light_mode);

	--profile_note_color: green;
	--profile_mention_color: gray;
	--profile_button_border_color: var(--profile_note_color);
	--profile_button_background_color: white;

	--sans_family: Avenir, 'Lucida Grande', Lucida, Verdana, sans-serif;
	--serif_family: Palatino, Georgia, serif;
	--fixed_family: Courier, monospace;
}

@media (prefers-color-scheme: dark) {
	:root {
		/* mostly derived: dark mode */
		/* --button_color: var(--mid_blue);
		--button_text_color: var(--titlebar_blue);
		--icon_color: white;
		--post_hover: #001e00; */
		/* very dark green */
		/* --text_color: var(--titlebar_light_mode);
		--textbox_blue: var(--deep_blue);
		--titlebar_blue: var(--deep_blue);

		--profile_button_border_color: white;
		--profile_button_background_color: var(--profile_note_color); */
	}
}

* {
	font-family: var(--sans_family);
}

code,
pre {
	font-family: var(--fixed_family);
}

.bold {
	font-weight: bold;
}

.center {
	justify-content: center;
	align-items: center;
}

.error {
	color: red;
}

.indent-1 {
	margin-left: 1rem;
}

.indent-tab {
	margin-left: 3rem;
}

.serial,
.fixed-width-font {
	font-family: var(--fixed_family);
}

body.dialog {
	margin: 0;
	padding-left: 2rem;
	padding-right: 2rem;
}

@media only screen and (min-width: 1100px) {
	body.dialog {
		padding-left: 10%;
		padding-right: 10%;
	}
}

body.dialog input {
	accent-color: var(--deep_blue);
}

body.macOS .windows-only {
	display: none;
}

body.windowsOS .mac-only {
	display: none;
}

#help_page {
	margin-bottom: 2rem;
}

h1,
h2 {
	margin: 0;
	margin-block-start: 0;
	margin-block-end: 0;
	margin-top: 1rem;
	padding: 0;
}

h1 {
	color: var(--text_color);
	font-size: 18px;
}

h2 {
	color: var(--mid_blue);
	font-size: 16px;
}

hr {
	margin-top: 1rem;
}

ul,
ol,
li:first-of-type,
p:first-of-type {
	margin-block-start: 0.5rem;
	margin-top: 0;
	padding-top: 0;
}

li:not(:last-of-type) {
	margin-bottom: 0.5rem;
}

li:last-of-type,
p:last-of-type {
	margin-bottom: 0;
	margin-block-end: 0.25rem;
}

/* font-size */

#help_page,
.glyph,
.info-note,
.purchase_buttons button,
.settings_page button,
.tabulator,
td,
th,
button,
label,
input,
option,
select,
textarea {
	font-size: 14px;
	width: auto;
	height: auto;
}

/* workaround: set a border so the above font-size takes effect in Safari */
select {
	border: 1px solid var(--button_color);
}

.tabs button {
	font-size: 1rem;
}

.lemon-squeezy,
.small,
.purchase_buttons button,
button.clearBtn,
button.small {
	font-size: 13px;
	/* height: 1.6rem */
}

/* database.css: font-size: 12px for glyphs */

.lemon-squeezy {
	color: var(--dark_gray);
	flex-basis: 100%;
	font-style: italic;
}

#trial_dialog .lemon-squeezy {
	margin-top: 1em;
}

.dialog button,
.navbar button,
.purchase_buttons button,
.tabs button {
	background: var(--button_color);
	color: var(--button_text_color);
	border: none;
}

/* DB tables: Users, Posts, Actions, Edits */
.tabs button {
	width: 5rem;
}

.small_tabs button,
.tabs button {
	margin-right: 0.5rem;
}

button#save,
.glyphs_wrapper button,
.purchase_buttons button,
.small_tabs button {
	padding-left: 0.5rem;
	padding-right: 0.5rem;
}

/* must go AFTER purchase_buttons */
/* convert to free version */
/* open settings folder */
button.wide {
	width: 12rem;
}

button.clearBtn,
.dialog button,
.purchase_buttons button,
.glyphs_wrapper button,
.small_tabs button,
.tabs button,
.edit-btn {
	align-items: center;
	border-radius: 8px;
	cursor: pointer;
	display: inline-flex;
	justify-content: center;
	transition: transform 0.2s ease;
}

.dialog button,
.purchase_buttons button,
.glyphs_wrapper button,
.small_tabs button,
.tabs button,
.edit-btn {
	height: 2rem;
}

button.clearBtn {
	margin-right: 0.5rem;
	align-self: center;
}

.dialog button.secondary,
button.clearBtn,
button.secondary,
div.search-footer-container button,
div.sort-footer-container button,
button#save,
button#sort-set-save {
	background-color: var(--button_text_color);
	border: 1px solid var(--button_color);
	color: var(--button_color);
}
/* TBD: I commented out for button#save; do we need it elsewhere? */
/* 	align-items: baseline; */

.glyphs_wrapper button,
#edit-layout {
	color: var(--button_text_color);
}

.purchase_buttons button {
	height: 1.8rem;
	width: 7rem;
}

button.small {
	height: 24px;
	padding-top: 4px;
}

/* ----- */
/* :hover and our .active */

.help_container img:hover {
	background: var(--mid_blue);
}

.dialog button:hover,
.purchase_buttons button:hover,
.tabs button:hover,
button#save:hover,
button#sort-set-save:hover {
	background: var(--mid_blue);
	color: var(--button_color);
}

.dialog button.secondary:hover,
button.secondary:hover {
	background: var(--paler_blue);
	/* 	border: 2px solid var(--mid_blue); */
	/* 	color: var(--button_text_color); */
}

.purchase_buttons button.active,
.tabs button.active {
	background: var(--mid_blue);
}

button.secondary.small:hover {
	background: var(--paler_blue);
}

/* .glphyBtn in database.css as: #glyphs div.glyph button:hover { */

/* ----- */

input[type='url'],
input[type='text'],
input[type='password'] {
	border-radius: 8px;
	border: 1px solid var(--mid_blue);
	/* light & dark */
	padding: 0.25rem 0.5rem;
}

.stacked {
	margin-top: 0.25rem;
}

.stacked button,
.stacked input[type='url'],
.stacked input[type='text'],
.stacked input[type='password'] {
	display: block;
	margin-top: 0.75rem;
}

.labelled-pairs {
	margin-top: 0.5rem;
	margin-left: 1rem;
}

#help_page .purchase_buttons {
	align-items: center;
	display: flex;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin-top: 1rem;
}

#help_page .purchase_buttons .error {
	margin-left: 9rem;
}

/* SEE ALSO: tiny .notemaker button in a_notemaker_extension.css */

#exportButton {
	margin-top: 1rem;
}

/* SEE ALSO: #windows-window-controls .titlebar-button img */
#dialog_close_img {
	width: calc(var(--titlebar-height) / 3.5);
	height: calc(var(--titlebar-height) / 3.5);
	padding: 0.7rem;
}

#dialog_close_img:hover {
	background-color: red;
	color: white;
}

#search_dialog_label,
#layout-dialog-label,
#save-as-dialog-label {
	font-weight: bold;
	/* margin-top: 0.25rem; */
}

.tabulator-cell.tabulator-editable {
	border: 0.25px solid var(--pale_gray);
	background-color: var(--paler_blue);
}

.spacer_bar {
	width: 1px;
	height: 1.5rem;
	background-color: #aaa;
}

.spacer {
	width: 3rem;
}

.half-spacer {
	width: 1.5rem;
}

.navbar {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin: 0.5rem;
	gap: 1rem;
}

.tabs {
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
}

/* ----- */
/* mostly for bar.html; some for details.html */

.titlebar-button {
	/* color: var(--icon_color); */
	display: inline-flex;
	justify-content: center;
	align-items: center;
	width: var(--titlebar-height);
	height: var(--titlebar-height);
	transform: translate(4px, -2px);
	cursor: pointer;
}

/* ideally like titlebar-button but round corners get in the way */
.x_to_close {
	/* 	width: var(--titlebar-height); */
	cursor: pointer;
}

#windows-window-controls .titlebar-button {
	width: 46px;
}

.titlebar-button.makedeeplyicon img {
	width: calc(var(--titlebar-height) / 1.2);
	height: calc(var(--titlebar-height) / 1.2);
}

.titlebar-button img {
	width: calc(var(--titlebar-height) / 2);
	height: calc(var(--titlebar-height) / 2);
}

#windows-window-controls .titlebar-button img {
	width: calc(var(--titlebar-height) / 3.5);
	height: calc(var(--titlebar-height) / 3.5);
}

#titlebar-free:hover,
#titlebar-trial:hover,
.titlebar-button:hover {
	background: var(--mid_blue);
	/* light & dark */
}

.titlebar-button#titlebar-close:hover,
.dialogs .titlebar-button:hover {
	background: red;
	color: white;
}

/* ----- */
/* help page */

.if_free_or_trial,
.if_free,
.if_paid_or_trial,
.if_paid,
.if_trial {
	display: none;
}

body.FREE .if_free_or_trial,
body.FREE .if_free,
body.PAID .if_paid_or_trial,
body.PAID .if_paid,
body.TRIAL .if_free_or_trial,
body.TRIAL .if_paid_or_trial,
body.TRIAL .if_trial {
	display: block;
}

/* ----- */
/* settings page and maybe elsewhere */
.info-note {
	border-radius: 8px;
	box-shadow: var(--mid_gray) 0px 2px 8px 0px;
	color: var(--mid_gray);
	padding: 1rem;
	align-items: start;
	display: flex;
	margin-bottom: 2rem;
}

.info-icon {
	margin-right: 8px;
	width: 18px;
	height: auto;
}

.info-text {
	flex: 1;
}

/* TBD: change to have a class for disabled state, then make available to all buttons not just #save */
#save {
	color: var(--mid_gray) !important;
	border-color: var(--mid_gray) !important;

	width: auto;

	user-select: none;

	pointer-events: none;
	cursor: default;
}

#save.active {
	color: var(--deep_blue) !important;
	border-color: var(--deep_blue) !important;

	pointer-events: auto;
	cursor: pointer;
}
