use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON><PERSON>, Deserialize, <PERSON>lone)]
pub struct M {
    pub from: usize,
    pub to: usize,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Row {
    pub m: M,
    pub number: usize,
    pub match_from: usize,
    pub match_to: usize,
    pub content: String,
    pub found: Option<String>,
}

#[derive(Debug, <PERSON><PERSON><PERSON>, Deserialize, <PERSON>lone)]
pub struct Finder {
    pub find: String,
    pub replace: String,
    pub case_sensitive: bool,
    pub entire_word: bool,
    pub grep: bool,
    pub selected_only: bool,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Data {
    pub title: String,
    pub file_name: String,
    pub file_folder: String,
    pub parent_folder: String,
    pub window: String,
    pub opendocs: bool,
    pub finder: Finder,
    pub selection: M,
    pub rows: Vec<Row>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ReplaceInstance {
    pub line: usize,
    pub pos: Position,
    pub replace: String,
    pub find: String,
}

#[derive(Debug, <PERSON>ial<PERSON>, Deserialize, <PERSON><PERSON>)]
pub struct ReplaceOptions {
    pub regex: bool,
    pub entire_word: bool,
    pub case_sensitive: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Position {
    pub from: u64,
    pub to: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Filters {
    pub match_rules: Vec<String>,
    pub ignore_rules: Vec<String>,
    pub use_gitignore: bool,
    pub omit_invisible_files: bool,
    pub omit_invisible_folders: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MatchIgnoreData {
    pub search: String,
    pub matched: String,
    pub ignored: String,
    pub extension: Option<String>,
    pub name: String,
    pub parent_folder: String,
    pub path: String,
    pub flags: Vec<String>,
}
