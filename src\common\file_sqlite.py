"""
file_sqlite.py - wrapper classes for
	Database - general; used by the others
	ActionsSQLite
	EditsSQLite
	HistorySQLite
	DataSQLite - wraps the cache table for any open csv/txt/json/html

refactor TBD: one or more of:

- MAYBE get rid of classes and move procedural code to db_utils.py (which has new exists_with_data)

- MAYBE refactor into fewer classes; these are very similar, e.g.
	* self.table_name already provides lots of overlap, and the fact that a few functions operate on different tables shouldn't matter
	* delete_all_data() is same for actions, edits, history
	* Database.load is NOT called in init -- but I later discovered it sometimes creates an empty file, which can then fool other code into thinking a proper cache exists
		'load' might (?) be required for 'plugin:sql' but not clear that's the best way to wrap SQLite; e.g. it hides many errors so difficult to debug
"""

from browser import aio, document, window
from datetime import datetime, timedelta
import json
import os

# local utils
import deeply_utils
import table_utils
import tauri


async def clear_data(app_config):
	# called on load if FREE version

	actions_db = ActionsSQLite(app_config=app_config)
	edits_db = EditsSQLite(app_config=app_config)
	history_db = HistorySQLite(app_config=app_config)

	res = await actions_db.delete_all_data()
	# print('clean_up_tables -- actions_db.delete_all_data:', res)

	res = await edits_db.delete_all_data()
	# print('clean_up_tables -- edits_db.delete_all_data:', res)

	res = await history_db.delete_all_data()
	# print('clean_up_tables -- history_db.delete_all_data:', res)


# ---------------
class Database:
	def __init__(self, path):
		self.path = path
		self.DEBUG = False


	@staticmethod
	async def load(path, debug=False):
		# WARNING: this sometimes creates an empty file, which can then fool other code into thinking a proper cache exists

		# documentation TBD: why is this a static method?
		# ... and not clear how useful it is in general

		_path = await tauri.invoke('plugin:sql|load', {
			'db': path,  # actually a partial path: sqlite:cache/foo.sqlite
		})
		if _path:
			deeply_utils.pp('FYI file_sqlite.py - Database.load staticmethod returned "%s"' % _path)
		else:
			deeply_utils.pp('ERROR? in file_sqlite.py - Database.load')
		return Database(_path)


	@staticmethod
	def get(path):
		return Database(path)


	def print_these(self, which, query, bind_values):
		print('\n-' * 3, 'file_sqlite.py -- %s' % which)
		print('db (self.path)', type(self.path), self.path)
		print('query (showing up to 999 chars)', type(query), query[:999])
		print('bind_values (showing up to 9 items)', bind_values[:9])


	def pp(self, which, query, bind_values):
		if self.DEBUG:
			return self.print_these(which, query, bind_values)


	async def execute(self, query, bind_values=[]):
		self.pp('execute', query, bind_values)

		result = await table_utils.invoke_tauri('plugin:sql|execute', {
			'db': self.path,
			'query': query,
			'values': bind_values if bind_values else []
		})

		if result and (len(result) == 2):
			rows_affected, last_insert_id = result
		else:
			self.print_these('execute FAILED', query, bind_values)
			rows_affected, last_insert_id = 0, None

		return {
			'rows_affected': rows_affected,
			'last_insert_id': last_insert_id,
		}


	async def select(self, query, bind_values=[]):
		# self.DEBUG = True
		self.pp('-----> sqlite_select using table_utils.invoke_tauri', query, bind_values)  # also prints self.db

		try:
			# result = await tauri.invoke('plugin:sql|select', {
# 			result = await tauri.invoke('sqlite_select', {
			result = await table_utils.invoke_tauri('sqlite_select', {
				'db': self.path,
				'query': query,
				'values': bind_values if bind_values else [],
			})
			return result

		except Exception as e:
			print('ERROR in file_sqlite.py -- sqlite_select:', type(e), e)


	async def close(self, db=None):
		try:
			success = await tauri.invoke('plugin:sql|close', {
				'db': db
			})
			return success
		except Exception as e:
			print('ERROR in file_sqlite.py -- close', e)
			return False


# ---------------
class EditsSQLite:
	# wraps the 'edits' table

	def __init__(self, path_relative='', app_config=None):
		self.app_config = app_config
		self.path_relative = path_relative  # optional if the class is instantiated only for delete_all_data()
		self.db = ''
		self.table_name = 'edits'  # defining table_name allows several queries to be identical across similar code


	async def load_db(self):
		# SEE ABOVE: 'load' might (?) be required for 'plugin:sql' but not clear that's the best way to wrap SQLite
		# ... and if it makes sense to preserve, still seems better to call in __init__ BUT maybe an async issue?
		if not self.app_config.connection_string:  # obsolete?
			print('EditsSQLite: FIX')

		if not self.db:
			self.db = await Database.load(self.app_config.connection_string)


	async def get_last_edited(self, as_date=False):
		query = f'''
			SELECT timestamp
			FROM {self.table_name}
			WHERE the_table=?
			ORDER BY timestamp
			DESC LIMIT 1;
			'''
		row = await self.db.select(query, [self.path_relative])
		if not row:
			return None

		timestamp_string = dict(row[0])['timestamp']

		if as_date:
			dt_part, offset_part = timestamp_string.rsplit(" ", 1)

			utc_dt = datetime.strptime(dt_part, "%Y-%m-%d %H:%M:%S")
			local_time = utc_dt
			return local_time

		else:
			return timestamp_string


	async def check_edit_since_last_opened(self, last_opened):
		if not self.db:
			await self.load_db()

		query = f'''
			SELECT timestamp
			FROM {self.table_name}
			WHERE the_table=?
			ORDER BY timestamp
			DESC LIMIT 1;
			'''
		row = await self.db.select(query, [self.path_relative])
		if not row:
			return False

		timestamp_datetime = await self.get_last_edited(as_date=True)
		last_opened_datetime = deeply_utils.date_from_string(last_opened)
		# print('LAST OPENED:', last_opened_datetime, 'TIMESTAMP DATETIME:', timestamp_datetime)

		return last_opened_datetime < timestamp_datetime


	async def get_preview_data(self, last_ignored_changes_date=None):
		# for showing the user any changes made after the file was opened or after previous changes
		# (the 'preview' part of the name is something of a misnomer)
		if not self.db:
			await self.load_db()

		values = [self.path_relative]

		if last_ignored_changes_date:
			values.append(last_ignored_changes_date)
			query = f'SELECT * FROM {self.table_name} WHERE the_table = ? AND timestamp > ?;'
		else:
			query = f'SELECT * FROM {self.table_name} WHERE the_table = ?;'

		# print('get_preview_data query:', query)
		rows = await self.db.select(query, values)
		if rows:
			return rows
		else:
			return []


	async def insert_data(self, data):
		# refactor TBD: rename add_edit in parallel to add_action ... or maybe rename that one
		if not self.db:
			await self.load_db()

		query = f'''
			INSERT INTO {self.table_name} (the_id, the_field, old_value, new_value, timestamp, the_table)
			VALUES (?, ?, ?, ?, ?, ?);
			'''
		return await self.db.execute(query, data)


	async def delete_all_data(self):
		if not self.db:
			await self.load_db()

		query = f'DELETE FROM {self.table_name};'
		res = await self.db.execute(query)
		return res


# ---------------
class ActionsSQLite:
	# wraps the 'actions' table

	def __init__(self, app_config):
		self.app_config = app_config
		# refactor TBD: why does EditsSQLite need path_relative but this doesn't?
		# TBD: change DB fields to be closer to Edits, e.g. timestamp instead of date_saved
		self.db = None
		self.table_name = 'actions'  # defining table_name allows several queries to be identical across similar code


	async def load_db(self):
		# SEE ABOVE: 'load' might (?) be required for 'plugin:sql' but not clear that's the best way to wrap SQLite
		# ... and if it makes sense to preserve, still seems better to call in __init__
		if not self.app_config.connection_string:  # obsolete?
			print('\n?' * 6, 'ActionsSQLite: FIX')

		if not self.db:
			self.db = await Database.load(self.app_config.connection_string)


	async def add_action(self, action, details, date_saved, count):
		if not self.db:
			await self.load_db()

		query = f'''
			INSERT INTO "{self.table_name}" (action, details, date_saved, count)
			VALUES (?, ?, ?, ?);
			'''
		values = [action, details, date_saved, count]
		# print('add_action: query', query)
		res = await self.db.execute(query, values)
		return res


	async def get_action(self, field, value):
		if not self.db:
			await self.load_db()

		query = f'''
			SELECT *
			FROM {self.table_name}
			WHERE {field} = ?
			LIMIT 1;
			'''
		# print('get_action: query', query)
		row = await self.db.select(query, [value])
		if row:
			return row
		else:
			return None


	async def get_last_ignored_edits_date(self, details):
		if not self.db:
			await self.load_db()

		# TBD: should probably store every save not just the most recent ==> will need to sort
		query = f'''
			SELECT date_saved
			FROM {self.table_name}
			WHERE details = ?
			LIMIT 1;
			'''
		try:
			last_ignored = await self.db.select(query, [details])
			return dict(dict(last_ignored)['0'])['date_saved']
		except Exception as e:
			# print('Error getting last ignored edits date:', e)
			return None


	async def update_action(self, fields, values, action_id):
		# TBD: should probably store every save not just the most recent ==> sql INSERT not UPDATE
		if not self.db:
			await self.load_db()

		set_clause = ', '.join([f'"{field}" = ?' for field in fields])
		query = f'''
			UPDATE "{self.table_name}"
			SET {set_clause}
			WHERE "{list(action_id.keys())[0]}" = ?;
			'''
		# print('update_action: query', query)
		values.append(list(action_id.values())[0])  # append the action_id value to the end of the values list
		res = await self.db.execute(query, values)
		return res


	async def delete_all_data(self):
		if not self.db:
			await self.load_db()

		query = f'DELETE FROM {self.table_name};'
		return await self.db.execute(query)


# ---------------
class HistorySQLite:
	# wraps the 'history' table
	# SEE ALSO: get_history in file.rs -- called by history.py
	# ... though awkward to have code in 2 different places, probably not worth rewriting that Rust to Python

	def __init__(self, app_config):
		self.app_config = app_config
		self.db = None
		self.table_name = 'history'  # defining table_name allows several queries to be identical across similar code


	async def load_db(self):
		# SEE ABOVE: 'load' might (?) be required for 'plugin:sql' but not clear that's the best way to wrap SQLite
		# ... and if it makes sense to preserve, still seems better to call in __init__
		if not self.app_config.connection_string:  # obsolete?
			print('\n?' * 6, 'HistorySQLite: FIX')

		self.db = await Database.load(self.app_config.connection_string)


	async def delete_all_data(self):
		if not self.db:
			await self.load_db()

		query = f'DELETE FROM {self.table_name};'
		return await self.db.execute(query)


# ---------------
class DataSQLite:
	# wraps the cache table for any open csv/txt/json/html
	# SEE ALSO: db_utils.py with simpler code

	def __init__(self, source_filename, table_data=[], app_config=None):
		self.app_config = app_config
		self.app_data_dir = None
		self.db = None
		self.table_data = table_data
		# self.columns_with_type = dict()
		self.column_names = []
		self.columns_with_brackets = []

		# from the caller's perspective: pass the original source filename
		# internally: it's the table_name
		self.table_name = source_filename  # e.g. foo.csv, bar.txt

		# with parens to minimize the chance of overlapping with user fields
		# CONSIDER _deeply_ prefix or suffix instead (so manual queries don't have to be quoted)
		self.internal_with_type = [
			'"%s" TEXT' % table_utils.mod_date,
			'"%s" TEXT' % table_utils.mod_note,
			]

		# print('DataSQLite INIT table_data[0]', table_data[0])


	async def set_column_globals(self, row):
		self.column_names = []  # TBD: maybe this is ok or maybe the caller is re-using an instance it shouldn't or maybe it should check if already done
		self.columns_with_brackets = []

		for key, value in row.items():
			self.column_names.append(key)
			self.columns_with_brackets.append(f'[{key}]')

		# print('set_column_globals -- columns_with_brackets', self.columns_with_brackets)


	async def load_db(self):
		# SEE ABOVE: 'load' might (?) be required for 'plugin:sql' but not clear that's the best way to wrap SQLite
		# ... and if it makes sense to preserve, still seems better to call in __init__

		# 'plugin:sql' seems to require 'sqlite:' prefix as connection string -- and looks in app_config_path
		self.db = await Database.load('sqlite:' + os.path.join('cache', '%s.sqlite' % self.table_name))
		# print('Database Loaded:', self.db)


	async def create_cache_table(self):
		# create empty SQLite DB to be populated via a subsequent call to insert_data_as_batch()
		deeply_utils.pp('create_cache_table')
		if not self.table_data:
			# i.e. should only call this function if table_data was sent to this instance in the constructor (or otherwise)
			raise Exception('requires data!')

		sample_row = self.table_data[0]

		columns_with_type = []
		columns_with_type.append(f'{table_utils.file_row} INTEGER PRIMARY KEY AUTOINCREMENT')

		await self.set_column_globals(sample_row)

		for key, value in sample_row.items():
			columns_with_type.append(f'[{key}] TEXT')

		# these columns won't be used if not is_paid_or_trial but they should be there for upgrade
		columns_with_type.extend(self.internal_with_type)

		query = f'''
			DROP TABLE IF EXISTS "{self.table_name}";
			CREATE TABLE IF NOT EXISTS "{self.table_name}" ({', '.join(columns_with_type)});
			'''
		# print('create_cache_table: query', query)
		res = await self.db.execute(query)
		# print('Table Created:', res)


	async def build_batch_insert_query(self, rows):
		# builds a single INSERT statement with values that span multiple records
		# ==> SQLite handles any required quoting (embedded single or double quote depending on how we call it)

		# build "(?,?,…)" placeholder groups
		group = '(' + ','.join(['?'] * len(self.columns_with_brackets)) + ')'
		placeholder_groups = ','.join([group] * len(rows))

		columns_text = ', '.join(self.columns_with_brackets)

		query = f'''
			BEGIN TRANSACTION;
			INSERT INTO "{self.table_name}" ({columns_text})
			VALUES {placeholder_groups};
			COMMIT;
			'''

		# flatten values across all rows
		flat_values = []
		for row in rows:
			for column_name in self.column_names:
				flat_values.append(row[column_name])

		return query, flat_values


	async def insert_data_as_batch(self, data_to_insert=None):
		# performance TBD: this code is likely slow for a large set of data
		# ==> first load enough to display the table
		# then load additional batches in the background (with a warning if users searches/sorts before done)

		if not self.db:
			await self.load_db()
		# print('insert_data_as_batch: self.db', self.db)  # object so not very informative

		rows = data_to_insert or self.table_data  # i.e. if data_to_insert then use that, else table_data
		query, flat_values = await self.build_batch_insert_query(rows)
		# print('insert_data_as_batch: query', query)  # moved to execute() with debug param

		if True:###try:  # SSL TBD: enable after better understanding of error cases
			result = await self.db.execute(query, flat_values)

			if result.get('rows_affected', 0) > 0:
				# print('insert_data_as_batch: data added successfully:', result)
				return
			else:
				await self.insert_data_per_row(rows)  # fallback to insert data per row; this has its own try/except

# 		except Exception as e:
# 			print('ERROR INSERTING DATA IN A BATCH (will try insert_data_per_row):', e)
# 			await self.insert_data_per_row()  # fallback to insert data per row; this has it's own try/except


	async def insert_data_per_row(self, rows):
		# called by insert_data_as_batch as fallback on failure

		columns_text = ', '.join(self.columns_with_brackets)
		placeholders = ','.join(['?'] * len(self.columns_with_brackets))

		failed_count = 0

		try:
			await self.db.execute('BEGIN TRANSACTION;')

			for row in rows:
				query = f'''
					INSERT INTO "{self.table_name}" ({columns_text})
					VALUES ({placeholders});
					'''
				col_values = [row[col] for col in self.column_names]

				result = await self.db.execute(query, col_values)

				if result.get('rows_affected', 0) == 0:
					print('ERROR: failed to insert individual row', row)
					failed_count += 1
					# proceed to find all failing rows!

			if failed_count:
				raise Exception('%s of %s rows failed' % (failed_count, len(rows)))
			# else: keep!

			await self.db.execute('COMMIT;')

		except Exception:
			await self.db.execute('ROLLBACK;')
			raise


	async def get_all_db_data(self):
		# refactor TBD: rename to something simpler -- with optional columns=[] param and code below
		if not self.db:
			self.load_db()

		query = f'SELECT * FROM "{self.table_name}";'
		rows = await self.db.select(query)
		return rows


	async def select_db_data(self, select_columns):
		if self.app_config.is_paid_or_trial:
			select_columns.append(table_utils.mod_date)
			select_columns.append(table_utils.mod_note)
		# print('select_db_data: select_columns', select_columns)

		query = f'''
			SELECT {', '.join(f'"{col}"' for col in select_columns)}
			FROM "{self.table_name}";
			'''
		return await self.db.select(query)  # rows


	async def delete_table_columns(self, columns_to_keep):
		# documentation TBD: explain rationale -- e.g. why do we need a temp table?
		# ... e.g. SQLite allows deleting columns

		# these columns won't be used if not is_paid_or_trial but they should be there for upgrade
		columns_to_keep.append(table_utils.mod_date)
		columns_to_keep.append(table_utils.mod_note)

		if not self.db:
			await self.load_db()

		drop_temp_query = '''DROP TABLE IF EXISTS temp;'''
		res = await self.db.execute(drop_temp_query)

		temp_db_query = f'''
			CREATE TABLE temp AS
				SELECT {', '.join(f'"{col}"' for col in columns_to_keep)}
				FROM "{self.table_name}";
			'''
		# print('copying data to temporary_db...')
		res = await self.db.execute(temp_db_query)
		# print('res: ', res)

		drop_table_query = f'DROP TABLE "{self.table_name}";'
		# print('dropping main table...')
		res = await self.db.execute(drop_table_query)
		# print('res: ', res)

		rename_table_query = f'''
			ALTER TABLE temp
			RENAME TO "{self.table_name}";
			'''
		# print('RENAMING temp table')
		res = await self.db.execute(rename_table_query)
		# print('res: ', res)

		columns_query = f'PRAGMA table_info("{self.table_name}");'
		res = await self.db.select(columns_query)
		# print('Updated Columns:', res)


	async def add_table_columns(self, columns_to_add):
		if not self.db:
			await self.load_db()
		# print('COLUMNS TO ADD:', columns_to_add)
		for column in columns_to_add:
			query = f'''
				ALTER TABLE "{self.table_name}"
				ADD COLUMN "{column}" TEXT;
				'''
			# print('adding column: ', column, query)
			res = await self.db.execute(query)
			# print('res: ', res)


	async def check_table_exists(self, treat_empty_as_missing=True):
		# treat_empty_as_missing to handle case where we created a table but then hit an error when inserting data
		if not self.db:
			await self.load_db()

		if treat_empty_as_missing:
			# this will yield an error if the table doesn't exist ==> try/except below
			# 'AS name' to have the same result as below
			query = f'SELECT EXISTS(SELECT 1 FROM "{self.table_name}" LIMIT 1) AS name;'
		else:
			query = f'SELECT name FROM sqlite_master WHERE type="table" AND name="{self.table_name}";'

		try:
			row = await self.db.select_one(query)
			return bool(row.get('name'))

		except Exception as e:
			return False


	async def update_data(self, field, value, id, timestamp):
		if not self.db:
			await self.load_db()

		query = f'''
			UPDATE "{self.table_name}"
			SET "{field}" = ?, "{table_utils.mod_date}" = ?, "{table_utils.mod_note}" = ?
			WHERE "{table_utils.file_row}" = ?;
			'''
		# print('update_data: query', query)
		res = await self.db.execute(query, [value, timestamp, 'edited', id])
		return


	async def delete_row(self, row_id):
		if not self.db:
			await self.load_db()

		query = f'''
			DELETE FROM "{self.table_name}"
			WHERE {table_utils.file_row} = ?;
			'''
		# print('delete_row: query', query)
		return await self.db.execute(query, [row_id])
