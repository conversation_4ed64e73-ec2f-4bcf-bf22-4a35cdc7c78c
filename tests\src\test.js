import { expect } from 'chai';
import * as config from './config.js';
import { cleanup, driver, prepareDriver, prepareTauri, tauriDriver } from './setup.js';

before(async function () {
	this.timeout(config.beforeTimeout);
	// ensure the program has been built
	await prepareTauri();
	await prepareDriver();
});

after(async function () {
	this.timeout(config.afterTimeout);

	// for testing
	// await sleep(5000);
	await cleanup(driver, tauriDriver);
});

describe('General', () => {
	it('Should be on main', async () => {
		const url = await driver.executeScript('return location.href');
		expect(url).contains('index.html');
	});
});
