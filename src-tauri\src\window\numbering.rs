use eyre::{ Con<PERSON>Compat, Result };
use serde_json::json;
use std::collections::HashMap;
use tauri::{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager };

// #[cfg(target_os = "macos")]
use super::menu;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct NumberedWindow {
	pub path: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct NumberedWindows {
	// key is label, value is DbWindow struct
	pub windows: HashMap<String, NumberedWindow>,
}

impl NumberedWindows {
	pub fn new() -> Self {
		Self {
			windows: HashMap::new(),
		}
	}

	pub fn count(&mut self, path: String) -> usize {
		self.windows
			.values()
			.filter(|w| w.path == path.clone())
			.count()
	}

    pub fn add_window(
        &mut self,
        label: String,
        path: String,
        app_handle: &AppHandle,
    ) -> Result<NumberedWindow> {
		// New Instance number equals to the count (because index start from 0)
        let new_instance = self
            .windows
			.values()
			.filter(|w| w.path == path.clone())
			.count();
		let numbered_window = NumberedWindow {
			path: path.clone(),
		};

		let new_title = if new_instance == 0 {
			path
		} else {
			format!("{} ({})", path, new_instance + 1) // start from 2
		};
        let window = app_handle
            .get_webview_window(&label)
            .context("label not found")?;
		window.set_title(&new_title)?;
		self.windows.insert(label.clone(), numbered_window.clone());
		app_handle.emit("title_update", json!({})).unwrap();
		// add to window submenu
		// #[cfg(target_os = "macos")]
		menu::update_titles(app_handle);
		Ok(numbered_window)
	}
}
