import path from 'path';
import serveStatic from 'serve-static';
import { defineConfig } from 'vite';
import { viteStaticCopy } from 'vite-plugin-static-copy';

const host = process.env.TAURI_DEV_HOST;

export default defineConfig({
	// prevent vite from obscuring rust errors
	clearScreen: false,
	server: {
		// <PERSON><PERSON> expects a fixed port, fail if that port is not available
		strictPort: true,
		// if the host <PERSON><PERSON> is expecting is set, use it
		host: host || false,
		port: ${PORT},
	},
	// Env variables starting with the item of `envPrefix` will be exposed in tauri's source code through `import.meta.env`.
	envPrefix: ['VITE_', 'TAURI_ENV_*'],
	build: {
		// <PERSON><PERSON> uses Chromium on Windows and WebKit on macOS and Linux
		target: process.env.TAURI_ENV_PLATFORM == 'windows' ? 'chrome105' : 'safari13',
		// don't minify for debug builds
		minify: !process.env.TAURI_ENV_DEBUG ? 'esbuild' : false,
		// produce sourcemaps for debug builds
		sourcemap: !!process.env.TAURI_ENV_DEBUG,
		outDir: '../dist',
		emptyOutDir: true,
	},

	resolve: {
		alias: [
			{
				find: /^\/common\/(.*)/,
				replacement: path.resolve(__dirname, 'src/common/$1'),
			},
			{
				find: /^\/windows\/(.*)/,
				replacement: path.resolve(__dirname, 'src/windows/$1'),
			},
		],
	},
	esbuild: {
		keepNames: true,
	},
	plugins: [
		viteStaticCopy({
			targets: [
				{
					src: path.resolve(__dirname, 'src/edit1'),
					dest: '',
				},
				{
					src: path.resolve(__dirname, 'src/lib'),
					dest: '',
				},
				{
					src: path.resolve(__dirname, 'src/common'),
					dest: '',
				},
				{
					src: path.resolve(__dirname, 'src/windows'),
					dest: '',
				},
				{
					src: path.resolve(__dirname, 'src/assets'),
					dest: '',
				},
			],
		}),
		{
			name: 'serve-common',
			configureServer(server) {
				server.middlewares.use('/edit1', serveStatic(path.resolve(__dirname, 'src/edit1')));
				server.middlewares.use('/lib', serveStatic(path.resolve(__dirname, 'src/lib')));
				server.middlewares.use('/common', serveStatic(path.resolve(__dirname, 'src/common')));
				server.middlewares.use('/windows', serveStatic(path.resolve(__dirname, 'src/windows')));
				server.middlewares.use('/assets', serveStatic(path.resolve(__dirname, 'src/assets')));
			},
		},
	],
});
