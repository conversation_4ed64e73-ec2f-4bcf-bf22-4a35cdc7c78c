<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Find/Replace</title>
		<link rel="stylesheet" href="../../plugins/fontawesome/css/fontawesome.min.css" />
		<link rel="stylesheet" href="../../plugins/fontawesome/css/solid.css" />

		<link rel="stylesheet" href="../../style/makedeeply.css" />
		<link rel="stylesheet" href="../../style/theme.css" />
		<link rel="stylesheet" href="../../style/dialog-titlebar.css" />
		<link rel="stylesheet" href="../style/find-dialog-and-table.css" />
		<link rel="stylesheet" href="find-dialog.css" />
	</head>
	<body id="find-dialog" class="dialog">
		<!-- <div data-tauri-drag-region class="titlebar">
			<div data-tauri-drag-region class="box left-box">
				<div class="symbole">{;}</div>
			</div>
			<div data-tauri-drag-region class="box middle-box">
				<div id="titlebar-title" data-tauri-drag-region>Find/Replace</div>
			</div>
			<div data-tauri-drag-region class="box right-box">
				<div class="titlebar-button" id="titlebar-close">
					<i class="fa-solid fa-xmark"></i>
				</div>
			</div>
		</div> -->
		<div id="search" class="finder">
			<form id="finder-form">
				<div class="box find-box">
					<div class="input-box">
						<label for="find-value" id="find-value-label" tabindex="-1">Find</label>
						<textarea name="find" id="find-value" spellcheck="false" rows="3"></textarea>
					</div>
					<div class="btns-box">
						<button type="button" tabindex="-1" id="next-btn">Next</button>
						<button type="button" tabindex="-1" id="previous-btn">Previous</button>
					</div>
				</div>
				<div class="box replace-box">
					<div class="input-box">
						<label for="replace-value" tabindex="-1">Replace</label>
						<textarea name="replace" id="replace-value" spellcheck="false" rows="3"></textarea>
					</div>
					<div class="btns-box">
						<button type="button" tabindex="-1" id="replace-btn">Replace</button>
						<button type="button" tabindex="-1" id="replace-all-btn">Replace All</button>
						<button type="button" tabindex="-1" id="replace-find-btn">Replace & Find</button>
					</div>
				</div>
			</form>
			<div class="options-box">
				<div class="opts">
					<div class="matching-opts checkbox-row">
						<span>Matching:</span>
						<div class="opt">
							<input type="checkbox" name="case_sensitive" id="case-sensitive" tabindex="-1" />
							<label for="case-sensitive">Case sensitive</label>
						</div>
						<div class="opt">
							<input tabindex="-1" type="checkbox" name="entire_word" id="entire-word" />
							<label for="entire-word">Entire word</label>
						</div>
						<div class="opt">
							<input tabindex="-1" type="checkbox" name="grep" id="grep" checked />
							<label for="grep">Regex</label>
						</div>
					</div>
					<div class="search-in-opts checkbox-row">
						<span>Search in:</span>
						<div class="opt">
							<input tabindex="-1" type="checkbox" name="selected_only" id="sel-text-only" />
							<label for="sel-text-only">Selected only</label>
						</div>
						<div class="opt">
							<input tabindex="-1" type="checkbox" name="wrapAround" id="wrap-around" />
							<label for="wrap-around">Wrap around</label>
						</div>
					</div>
				</div>
				<div class="box">
					<button type="button" tabindex="-1" id="tabulate-btn">Tabulate</button>
				</div>
			</div>
		</div>
		<script type="module" src="find-dialog.js" defer></script>
		<script type="module" src="../../js/windows-manager.js"></script>
	</body>
</html>
