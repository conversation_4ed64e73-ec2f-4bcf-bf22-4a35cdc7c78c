#not-data-area {
	width: calc(100vw - 2rem);
	height: calc(100vh - 4rem);
	font-size: 1.1rem;
}

#not-data-note {
	font-style: italic;
	margin-bottom: 0.5rem;
}

#format-container {
	display: grid;
	grid-template-columns: auto auto auto;
	gap: 0.2rem;
}

#edits-preview-container {
	z-index: 10;
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background-color: var(--pale_blue);
	min-width:-webkit-fill-available;
	justify-items: center;
	padding: 20px; /* Add some padding */
	backdrop-filter: blur(3px);
}

#edits-preview-text {
	background-color: white;
	padding-left: 0.7rem;
}

#edits-preview-btns {
	padding: 1rem;
}

#edits-preview-btns .btn {
/* 	padding: 0.5rem; */ /* On Mac: turns round-rect buttons into square buttons! */
	margin-right: 1rem;
}
