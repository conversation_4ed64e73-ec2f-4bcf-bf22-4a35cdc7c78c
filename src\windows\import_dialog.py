"""
import_dialog.py
"""

from browser import aio, document, window
import json
import re

# local to load first
import page_runner
import tauri

# local imports
from file_sqlite import Database
import deeply_utils
import get_file_extension_and_content  # refactor TBD: rename!
import shortcuts  # runs the bind commands on import
import table_utils

app_config = None  # must be loaded async; used here for connection_string
current_window = tauri.window.getCurrentWindow()  # TBD: or None then fetch later

# -----
# caveat: there's a risk these aren't ready at import time ==> TBD: move to main()

parent_label = window['__PROPS__']['parent_label']
table_name = window['__PROPS__']['target_name']  # refactor TBD: probably rename in Rust
main_table_header_data = json.loads(window['__PROPS__']['header_columns'])

# -----
incoming = {}  # temp_keys list [field1..n], key_lookup if known, row_dicts (with 1st row initially ambiguous: header vs. data)
col_matches_dict = {}  # keys: current_column_name; values: field_n or '' -- refactor to CONSIDER: only include matches!
name_label_mapping = {}
unmatched_incoming = []  # field_n in 'Skip' section i.e. not yet matched

has_header = True  # by default: assume first data record/row is a header (not data)


def remove_data_attributes(element):
	element.innerHTML = '' # Import
	element.nextElementSibling.innerHTML = '' # Record 1

	for suffix in ('field', 'name', 'value'):  # 'current' is a property of a top row ==> never removed
		element.removeAttribute('data-%s' % suffix)


async def clean_text(value_to_clean):
	# match fields on the 'interesting' portion: only alphanumeric, i.e. no punctuation or whitespace
	return re.sub(r'[^a-zA-Z0-9]', '', value_to_clean).lower()


def generate_table_row(has_header:bool, in_skip_section:bool, field_n, import_column_name=None, import_column_value=None, current_column_name=None, empty=False):
	# has_header
	# ... True (usual case): first row of incoming file is a header
	# ... False: first row of incoming file is data

	# skip: not has_match, i.e. goes in the top section
	has_match = not in_skip_section

	if False:###debug
		deeply_utils.pp('.\n' * 3, 'generate_table_row')
		deeply_utils.pp('... has_header', has_header)
		deeply_utils.pp('... in_skip_section', in_skip_section)
		deeply_utils.pp('... field_n', field_n)
		deeply_utils.pp('... import_column_name', import_column_name)
		deeply_utils.pp('... import_column_value', import_column_value)
		deeply_utils.pp('... current_column_name', current_column_name)
		deeply_utils.pp('... empty', empty)

	tr = document.createElement('tr')

	td_label = document.createElement('td')  # friendly name
	td_name = document.createElement('td')
	td_import = document.createElement('td')  # import_column_name ... or Skip
	td_data = document.createElement('td')  # import_column_value

	td_label.classList.add('fieldname')
	td_name.classList.add('fieldname')
	td_import.classList.add('import_fieldname')

	td_import.setAttribute('draggable', 'true')
	td_import.setAttribute('data-field', field_n)  # may be blank
	td_import.setAttribute('data-name', import_column_name)
	td_import.setAttribute('data-value', import_column_value)

	# td_import hasn't yet be added to TR ==> set directly not via parentNode
	tr.setAttribute('data-current', current_column_name)  # set on TR

	td_import.bind('dragstart', on_drag_start)
	td_import.bind('dragover', on_drag_over)
	td_import.bind('drop', on_drop)

	if has_match:
		# top section: Import
		# refactor TBD: maybe move some of these to the 'not empty' section below ... e.g. if no match then doesn't need to be draggable

		td_import.setAttribute('id', f'{current_column_name}_c')  # for match_by_col_order

		td_label.innerHTML = name_label_mapping[current_column_name]
		td_name.innerHTML = current_column_name

		if empty:
			td_import.innerHTML = ''
			td_data.innerHTML = ''

	else:
		# bottom section: Skip

		td_import.setAttribute('id', field_n)  # import_column_name may be empty

		tr.classList.add('drop-zone')  # let user return an Import field to the Skip section

		td_label.innerHTML = ''
		td_name.innerHTML = ''

	if not empty:
		container = document.createElement('div')
		container.innerHTML = import_column_name or field_n  # i.e. if import_column_name is null then display field_n

		td_import.appendChild(generate_row_handle())
		td_import.appendChild(container)

		td_data.innerHTML = import_column_value

	tr.appendChild(td_label)
	tr.appendChild(td_name)
	tr.appendChild(td_import)
	tr.appendChild(td_data)

	return tr


def generate_row_handle():
	handle_container = document.createElement('div')
	handle_container.classList.add('handle-container')

	row_handle = document.createElement('div')
	row_handle.classList.add('drag-handle')

	bar1 = document.createElement('div')
	bar2 = document.createElement('div')
	bar3 = document.createElement('div')

	bar1.classList.add('bar')
	bar2.classList.add('bar')
	bar3.classList.add('bar')

	row_handle.appendChild(bar1)
	row_handle.appendChild(bar2)
	row_handle.appendChild(bar3)

	handle_container.appendChild(row_handle)

	return handle_container


async def get_matched_unmatched():
	global col_matches_dict, unmatched_incoming
	# globals not modified here: has_header, incoming

	if not has_header:
		# first row is data ==> can't automatically match ==> all fields are unmatched
		# set globals
		col_matches_dict = {}
		unmatched_incoming = incoming['temp_keys']  # field1..n
		return

	deeply_utils.pp('\n-' * 3, 'get_matched_unmatched')

	current_by_clean = {}
	field_n_by_clean = {}  # incoming

	unmatched_current_clean = []  # will check DB for these

	for field_n, import_col_name in list(incoming['key_lookup'].items()):   # keys are the artificial field1..n; values are incoming header (if any)
		field_n_by_clean[await clean_text(import_col_name)] = field_n

	# for each current field: store the label & look for an incoming field that matches
	for column_dict in main_table_header_data:
		if column_dict['name'] in table_utils.built_in_fields:
			# skip internal / parenthetical fields
			continue

		current_column_name = column_dict['name']
		deeply_utils.pp('get_matched_unmatched - current_column_name', current_column_name)

		col_matches_dict[current_column_name] = ''  # include all fields whether matched or not
		clean_col_name = await clean_text(current_column_name)
		current_by_clean[clean_col_name] = current_column_name
		# FUTURE to consider: in rare case of multiple with same clean name, this will pick the last

		if clean_col_name in field_n_by_clean:
			field_n = field_n_by_clean[clean_col_name]
			col_matches_dict[current_column_name] = field_n
		else:
			unmatched_current_clean.append(clean_col_name)

	unmatched_incoming = [field_n for field_n in incoming['temp_keys'] if field_n not in set(col_matches_dict.values())]

	deeply_utils.pp('-' * 22)
	deeply_utils.pp('get_matched_unmatched BEFORE DB -- col_matches_dict:', col_matches_dict)
	deeply_utils.pp('get_matched_unmatched BEFORE DB -- unmatched_incoming:', unmatched_incoming)
	deeply_utils.pp('-' * 22)

	if not unmatched_incoming:
		return

	unmatched_incoming_clean = []  # will check DB for these
	for clean, field_n in list(field_n_by_clean.items()):
		if field_n in unmatched_incoming:
			unmatched_incoming_clean.append(clean)

	# with any remaining unmatched fields, check DB for earlier matches
	mappings = await get_relevant_mappings(unmatched_current_clean + unmatched_incoming_clean)

	if not mappings:
		return

	for mapping in mappings:
		deeply_utils.pp('mapping', mapping)
		mapping_dict = dict(mapping)

		clean1 = mapping_dict['field1']
		clean2 = mapping_dict['field2']

		# check both directions
		if clean1 in unmatched_current_clean and clean2 in unmatched_incoming_clean:
			current_column_name = current_by_clean[clean1]
			field_n = field_n_by_clean[clean2]
			col_matches_dict[current_column_name] = field_n
			unmatched_incoming.remove(field_n)
			unmatched_current_clean.remove(clean1)
			unmatched_incoming_clean.remove(clean2)

		elif clean2 in unmatched_current_clean and clean1 in unmatched_incoming_clean:
			current_column_name = current_by_clean[clean2]
			field_n = field_n_by_clean[clean1]
			col_matches_dict[current_column_name] = field_n
			unmatched_incoming.remove(field_n)
			unmatched_current_clean.remove(clean2)
			unmatched_incoming_clean.remove(clean1)

		# else: no match

	deeply_utils.pp('/' * 22)
	deeply_utils.pp('get_matched_unmatched AFTER DB -- col_matches_dict:', col_matches_dict)
	deeply_utils.pp('get_matched_unmatched AFTER DB -- unmatched_incoming:', unmatched_incoming)
	deeply_utils.pp('/' * 22)


async def compare_update_build():

	# refactor TBD: replace this & similar code with a utility function
	# ... actually: how can this be an int or list?
# 	for i in range(len(unmatched_incoming)):
# 		if isinstance(unmatched_incoming[i], (int, list)):
# 			deeply_utils.pp('compare_update_build: unexpected int or list')
# 			unmatched_incoming[i] = str(unmatched_incoming[i])

	await get_matched_unmatched()  # compare incoming against current; map automatically where possible
	await update_n_of_m()
	await build_import_table()


async def update_n_of_m():
	# set 'Matched' html
	# globals not modified here: incoming, unmatched_incoming
	total_import_columns = len(incoming['temp_keys'])
	total_unimported_columns = len(unmatched_incoming)

	if total_unimported_columns == 0:
		document['importing-n-m'].innerHTML = f'Matched all {total_import_columns} columns'
	else:
		document['importing-n-m'].innerHTML = f'Matched {total_import_columns - total_unimported_columns} of {total_import_columns} columns'


async def build_import_table():
	# globals not modified here: has_header, col_matches_dict

	tbody = document.getElementById('import-table-body')
	tr = None
	check_db = []  # refactor TBD: should probably do this BEFORE calling build_import_table

	deeply_utils.pp('\n-' * 3, 'build_import_table with col_matches_dict', col_matches_dict)

	# top part: columns that match
	in_skip_section = False
	if has_header:
		for current_column_name in list(col_matches_dict.keys()):
			deeply_utils.pp('\n', '--> has_header build_import_table current_column_name "%s" & col_matches_dict[current_column_name] "%s"' % (current_column_name, col_matches_dict[current_column_name]))
			if col_matches_dict[current_column_name]:
				field_n = col_matches_dict[current_column_name]
				import_column_name = incoming['key_lookup'][field_n]
				import_column_value = incoming['row_dicts'][0][field_n]  # always show first row of data
				tr = generate_table_row(has_header, in_skip_section, field_n, import_column_name, import_column_value, current_column_name, empty=False)
			else:
				# no mapping ==> empty: True ... Scott TBD: is that now implied by field_n of None?
				check_db.append(await clean_text(current_column_name))
				tr = generate_table_row(has_header, in_skip_section, None, None, None, current_column_name, empty=True)

			tbody.appendChild(tr)

	else:
		for current_column_name in list(col_matches_dict.keys()):
			# Scott TBD: is this also: no mapping ==> empty: True ???
			deeply_utils.pp('\n', '**> NOT has_header build_import_table current_column_name "%s" & col_matches_dict[current_column_name] "%s"' % (current_column_name, col_matches_dict[current_column_name]))
			field_n = col_matches_dict[current_column_name]  # Scott TBD: does it matter if this is blank/None?
			tr = generate_table_row(has_header, in_skip_section, field_n, None, None, current_column_name, True)  # Scott TBD: why None, None

			tbody.appendChild(tr)

	# middle: a divider row labelled 'Skip'
	drop_row = document.createElement('tr')
	drop_row.classList.add('drop-zone')  # let user return an Import field to the Skip section
	drop_row.innerHTML = '<td class="fieldname"></td><td class="fieldname"></td><th>Skip</th><td class="record1-label">Record 1</td>'
	drop_row.bind('dragover', on_drag_over)
	drop_row.bind('drop', on_drop)
	drop_row.setAttribute('colspan', 4)

	tbody.appendChild(drop_row)

	# bottom part: columns that don't match
	in_skip_section = True
	if has_header:
		if True:###debug
			deeply_utils.pp('\n+++' * 3, "incoming.get('key_lookup')", incoming.get('key_lookup'))
			deeply_utils.pp()
			deeply_utils.pp('unmatched_incoming', unmatched_incoming)
			deeply_utils.pp()
		# TBD: why do this before the 'for' loop below
		for field_n in unmatched_incoming:
			import_column_name = incoming['key_lookup'][field_n]
			check_db.append(await clean_text(import_column_name))

		await get_relevant_mappings(check_db)  # from DB

		for i in range(len(unmatched_incoming)):
			field_n = unmatched_incoming[i]
			import_column_name = incoming['key_lookup'][field_n]
			import_column_value = incoming['row_dicts'][0][field_n]  # always show first row of data
			tr = generate_table_row(has_header, in_skip_section, field_n, import_column_name, import_column_value, current_column_name=None, empty=False)
			tbody.appendChild(tr)
	else:
		deeply_utils.pp('========\n' * 4, 'should this iterate thru unmatched_incoming?', unmatched_incoming)
		for field_n, value in list(incoming['row_dicts'][0].items()):
			# PARAMS: has_header:bool, skip:bool, field_n, import_column_name=None, import_column_value=None, current_column_name=None, empty=False
			tr = generate_table_row(has_header, in_skip_section, field_n, None, value, current_column_name=None, empty=False)
			tbody.appendChild(tr)


async def map_the_data():
	# after user accepts the field mapping, apply it to incoming data
	# globals not modified here: col_matches_dict
	mapped_data = []

	deeply_utils.pp('\n@@@' * 3, 'import_dialog.py map_the_data col_matches_dict', col_matches_dict)

	row_dicts = incoming['row_dicts']

	for row_dict in row_dicts:
		keep_row_dict = {}

		for current_column_name in list(col_matches_dict.keys()):
			if col_matches_dict[current_column_name]:
				value = row_dict[col_matches_dict[current_column_name]]
			else:
				# fill unknown columns with empty string
				# FUTURE TBD: would Postgres non-string columns need None?
				value = ''

			keep_row_dict[current_column_name] = value

		mapped_data.append(keep_row_dict)

	return mapped_data


async def merge_data(e):
	# globals not modified here: has_header
	e.stopImmediatePropagation()
	mapped_data = await map_the_data()

	if has_header:
		await save_mappings_to_db()

	# hack: append filename for file_data.py; refactor TBD: a better data structure
	filename = window['__PROPS__']['name']
	args = {'data': json.dumps(mapped_data + [filename]), 'label': parent_label}
	deeply_utils.pp('#\n' * 3, 'merge_data -- mapped_data', mapped_data)

	tauri.invoke('merge_data', args)  # emit import_data
	await close_dialog(e)


async def close_dialog(e):
	# close dialog window, whether before or after saving
	# refactor TBD: callers should probably directly call table_utils.close_window() with new optional param
	e.stopPropagation()

	table_utils.close_window()  # after emit file_tables_update


#.............................db functionality............................................
async def ensure_mappings_table():
	global app_config  # may or may not be required
	# refactor TBD: consider creating once at app startup for production ... though this might be useful during dev

	deeply_utils.pp('\n', 'ensure_mappings_table: TBD: does the following console stringify error matter?')
	if not app_config.connection_string:
		deeply_utils.pp('import_dialog.py ensure_mappings_table: FIX')

	db = await Database.load(app_config.connection_string)
	query = f'''
		CREATE TABLE IF NOT EXISTS mappings(
			field1 TEXT NOT NULL,
			field2 TEXT NOT NULL,
			timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
			PRIMARY KEY (field1, field2)
		);
		'''
	_result = await db.execute(query, [])
	# TBD: what result will be returned for CREATE TABLE since no data rows are affected?
	await Database.close(db)
	# deeply_utils.pp('\n', 'CREATE TABLE IF NOT EXISTS RESULTS', _result)


async def add_mapping(field1, field2):
	global app_config  # may or may not be required
	if not app_config.connection_string:
		deeply_utils.pp('import_dialog.py add_mapping: FIX')

	db = await Database.load(app_config.connection_string)

	field1_clean_text = await clean_text(field1)
	field2_clean_text = await clean_text(field2)
	sorted_fields = sorted((field1_clean_text, field2_clean_text))
	query = f'''
		INSERT INTO mappings(field1, field2)
		VALUES(?,?)
		ON CONFLICT(field1, field2) DO UPDATE SET timestamp=CURRENT_TIMESTAMP;
		'''
	try:
		_result = await db.execute(query, sorted_fields)
		deeply_utils.pp('ADD MAPPING RESPONSE: ', response)
	except Exception as e:
		deeply_utils.pp('ADD MAPPING ERROR: ', e)

	await Database.close(db)


async def save_mappings_to_db():
	# globals not modified here: has_header
	if not has_header:  # probably also handled by caller
		return

	for current_column_name, field_n in col_matches_dict.items():
		if field_n:
			deeply_utils.pp('save_mappings_to_db: field_n "%s"; incoming key_lookup', incoming['key_lookup'])
			await add_mapping(current_column_name, incoming['key_lookup'][field_n])


async def get_relevant_mappings(check_db):
	# the DB may have lots of mappings; this fetches any that might be relevant
	# check_db is a list of filtered fields from 'current' and 'incoming'

	global app_config  # may or may not be required
	if not app_config.connection_string:
		deeply_utils.pp('import_dialog.py get_relevant_mappings: FIX')

	db = await Database.load(app_config.connection_string)

	query = f'''
		SELECT * FROM mappings
		WHERE field1 in {tuple(check_db)}
		AND field2 in {tuple(check_db)}
		ORDER BY timestamp DESC;
		'''
	try:
		results = await db.select(query, [])
		# await restore_table_mappings(results)
	except Exception as e:
		deeply_utils.pp('ERROR in get_relevant_mappings:', e)

	await Database.close(db)

	return results


#.................................end......................................................
#................................match by col order........................................
async def match_by_col_order(event):
	# fill any remaining unmatched columns on top (current) with unmatched columns from the bottom (skip)
	# globals not modified here: incoming, match_by_col_order

	event.stopImmediatePropagation()
	deeply_utils.pp('.\n' * 4, 'match_by_col_order -- unmatched_incoming:', unmatched_incoming)  # field1..n
	deeply_utils.pp('col_matches_dict.items() BEFORE', col_matches_dict.items())

	# iterate through the top section
	# refactor to CONSIDER: just use the table instead of maintaining col_matches_dict
	for current_column_name, current_field_n in col_matches_dict.items():
		if current_field_n:
			# already has a match
			continue

		current_to_column_name = current_column_name  # use same variable as other code

		# treat this like a manual drag/drop
		drag_to_el = document.getElementById(f'{current_column_name}_c')  # the empty 'drag to' has an ID based on the Current column

		# grab the next item from the bottom section
		# refactor to CONSIDER: grab dynamically here rather than maintainting unmatched_incoming
		field_n = unmatched_incoming[0]  # always zero since will be removed below

		# variable names mimic manual drag code: "from bottom 'Skip' to top 'Import'"
		drag_from_el = document.getElementById(field_n)

		# --- same as TOP part of on_drop ---
		drag_from_column_name = drag_from_el.getAttribute('data-name')
		drag_from_column_value = drag_from_el.getAttribute('data-value')
		current_html = drag_from_el.innerHTML  # drag/drop row_handle + 'Import' or 'Skip' name

		# --- same code as manual drag ----
		drag_to_el.setAttribute('data-field', field_n)
		drag_to_el.setAttribute('data-name', drag_from_column_name)
		drag_to_el.setAttribute('data-value', drag_from_column_value)

		drag_to_el.innerHTML = current_html  # drag/drop row_handle + name
		drag_to_el.nextElementSibling.innerHTML = drag_from_column_value

		col_matches_dict[current_to_column_name] = field_n
		# ^^^ end ^^^

		unmatched_incoming.remove(field_n)
		deeply_utils.pp('match_by_col_order -- unmatched_incoming:', unmatched_incoming)

		drag_from_el.closest('tr').remove()  # when source is 'Skip', remove the entire row (instead of calling remove_data_attributes)

		if len(unmatched_incoming) == 0:
			break

	deeply_utils.pp('col_matches_dict.items() AFTER', col_matches_dict.items())
	deeply_utils.pp('^')

	await update_n_of_m()


#..............................radio button click/change.....................................................
async def on_radio_change(event):
	global has_header

	event.stopImmediatePropagation()
	if event.target.checked and event.target.value == 'header' and not has_header:
		# deeply_utils.pp('header checked!')
		has_header = True
		await first_row_changed()
	elif event.target.checked and event.target.value == 'data' and has_header:
		# deeply_utils.pp('data checked!')
		has_header = False
		await first_row_changed()


async def first_row_changed():
	global incoming, unmatched_incoming
	# globals not modified here: has_header

	table_body = document.getElementById('import-table-body')
	table_body.innerHTML = ''

	incoming = await get_incoming()  # already have the data so just shuffle it
	deeply_utils.pp('\n$$$$$$$' * 3)
	deeply_utils.pp('first_row_changed: incoming', incoming)
	deeply_utils.pp('col_matches_dict', col_matches_dict)
	deeply_utils.pp('has_header GLOBAL', has_header)

	if has_header:
		await compare_update_build()
		# deeply_utils.pp('IMPORT DATA WITH HEADER:', incoming)

	else:
		unmatched_incoming = list(incoming['temp_keys'])  # can't match any without incoming field names

		# refactor TBD: replace this & similar code with a utility function
		# ... actually: how can this be an int or list?
# 		for i in range(len(unmatched_incoming)):
# 			if isinstance(unmatched_incoming[i], (int, list)):
# 				deeply_utils.pp('first_row_changed: unexpected int or list')
# 				unmatched_incoming[i] = str(unmatched_incoming[i])

		for current_column_name in col_matches_dict.keys():
			col_matches_dict[current_column_name] = ''

		await update_n_of_m()
		await build_import_table()
		# deeply_utils.pp('IMPORT DATA HEADERLESS:', incoming)


async def get_incoming():
	# either read from disk or UPDATE after has_header changed
	# globals not modified here: incoming, has_header

	if incoming:
		# already got it; running again after has_header changed
		row_dicts = incoming['row_dicts']
		temp_keys = incoming['temp_keys']
		key_lookup = incoming['key_lookup']

		if row_dicts and key_lookup and not has_header:
			row_dicts = [key_lookup] + row_dicts  # restore the first line to the data
			key_lookup = None
	else:
		row_dicts = await get_file_extension_and_content.get_content(is_import=True)  # generate (field1)..n column names
		temp_keys = list(row_dicts[0].keys())
		key_lookup = None  # updated below if appropriate

		deeply_utils.pp('\n ====' * 3, 'get_incoming INITIAL LOAD', 'row_dicts', row_dicts)

	if has_header:  # i.e. radio button as True/False
		if key_lookup:
			deeply_utils.pp('ERROR: called get_incoming with no change? key_lookup =', key_lookup)
		elif not temp_keys[0].startswith('(field'):
			# I don't create field_n keys for json
			key_lookup = {key: key for key in row_dicts[0].keys()}

			# disable and gray out via CSS
			document.getElementById('no-header').disabled = True
			document.getElementById('no-header-label').classList.add('disabled')
		else:
			# usual case
			deeply_utils.pp('row_dicts[0] BEFORE POP', row_dicts[0])
			key_lookup = row_dicts.pop(0)  # move first row from row_dicts to key_lookup
			deeply_utils.pp('row_dicts[0] AFTER POP', row_dicts[0])
	else:
		key_lookup = None

	deeply_utils.pp('\n ****' * 3)
	deeply_utils.pp('get_incoming[row_dicts]', row_dicts)
	deeply_utils.pp('get_incoming[temp_keys]', temp_keys)
	deeply_utils.pp('get_incoming[key_lookup]', key_lookup)

	return {
		'row_dicts': row_dicts,
		'temp_keys': temp_keys,
		'key_lookup': key_lookup,
		}


#................................drag/drop......................................................
def on_drag_start(event):
	event.dataTransfer.setData('text/plain', event.target.getAttribute('id'))
	event.dataTransfer.dropEffect = 'move'
	# deeply_utils.pp('drag started!')


def on_drag_over(event):
	event.preventDefault()
	event.dataTransfer.dropEffect = 'move'
	# deeply_utils.pp('drag over drop zone!')


def on_drop(event):
	# globals not modified here: has_header
	event.preventDefault()
	event.stopImmediatePropagation()

	# DOM note: event.target is the destination (drag_to)
	if event.target.tagName == 'DIV':
		drag_to_el = event.target.closest('td')
	else:
		drag_to_el = event.target

	drag_from_id = event.dataTransfer.getData('text/plain')  # current_column_name or field_n ... or maybe the 'Skip' TH drop-zone
	drag_from_el = document.getElementById(drag_from_id)

	# --- same as TOP part of match_by_col_order ---
	# refactor TBD: move these to a dict then create a function to handle all 4 groups of setAttribute (2 regular drag_from element + 1 swap + match-by)
	field_n = drag_from_el.getAttribute('data-field')
	drag_from_column_name = drag_from_el.getAttribute('data-name')
	drag_from_column_value = drag_from_el.getAttribute('data-value')
	current_html = drag_from_el.innerHTML  # drag/drop row_handle + 'Import' or 'Skip' name

	current_from_column_name = drag_from_el.parentNode.getAttribute('data-current')  # from TR; will only have a value if dragging FROM the top section
	current_to_column_name = drag_to_el.parentNode.getAttribute('data-current')

	if str(current_from_column_name) == '[object Object]':  # TBD: find a more elegant solution!
		current_from_column_name = None

	if str(current_to_column_name) == '[object Object]':  # TBD: find a more elegant solution!
		current_to_column_name = None

	if str(drag_from_column_name) == '[object Object]':  # TBD: find a more elegant solution!
		drag_from_column_name = None

	deeply_utils.pp('on_drop -- drag_from_column_name "%s"; "%s" current_from_column_name:' % (drag_from_column_name, current_from_column_name))

	# --------------------------
	# from Import back down to Skip; put first to avoid checking is_drop_zone on the others
	# ... put this first just to avoid checking is_drop_zone in every item
	is_drop_zone = event.target.closest('tr').classList.contains('drop-zone')
	deeply_utils.pp('is_drop_zone', is_drop_zone, '|', 'drag_from_el.tagName', drag_from_el.tagName, '|', 'drag_from_column_name', drag_from_column_name, '|', 'drag_from_column_value', drag_from_column_value, '|', 'has_header', has_header)

	if is_drop_zone and (drag_from_el.tagName == 'TD'):
		# drop zone is the SKIP section ==> create a new row
		# PARAMS: generate_table_row(has_header:bool, skip:bool, field_n, import_column_name=None, import_column_value=None, current_column_name=None, empty=False)
		tr = generate_table_row(has_header, True, field_n, drag_from_column_name, drag_from_column_value, None, False)
		deeply_utils.pp('after generate_table_row', tr)

		tbody = document.getElementById('import-table-body')
		if not tbody:
			deeply_utils.pp('is_drop_zone ERROR: missing tbody?', document.getElementById('import-table-body'))
		tbody.appendChild(tr)

		deeply_utils.pp('is_drop_zone -- adding %s to unmatched_incoming' % field_n, unmatched_incoming)

		col_matches_dict[current_from_column_name] = ''
		unmatched_incoming.append(field_n)  # i.e. this field is now available to be matched

		remove_data_attributes(drag_from_el)  # and 2 HTML columns
		deeply_utils.pp('on_drop -- finished drag: is_drop_zone', '\n' * 2)

	# --------------------------
	elif not drag_from_el.innerHTML:
		deeply_utils.pp('\n' * 2, 'empty drag_from_el.innerHTML')
		return

	# --------------------------
	# usual case: from bottom 'Skip' to top 'Import'
	# event.target.innerHTML == '' should prevent dropping onto an existing row ... FUTURE TBD: could swap it down to 'Skip' section
	# and (drag_from_el.tagName == 'TD') and (event.target.tagName in ('TD', 'DIV'))
	elif (current_to_column_name and not current_from_column_name) and event.target.innerHTML == '':

		# --- same code as below ---
		drag_to_el.setAttribute('data-field', field_n)
		drag_to_el.setAttribute('data-name', drag_from_column_name)
		drag_to_el.setAttribute('data-value', drag_from_column_value)

		drag_to_el.innerHTML = current_html  # drag/drop row_handle + name
		drag_to_el.nextElementSibling.innerHTML = drag_from_column_value

		col_matches_dict[current_to_column_name] = field_n
		# ^^^ end ^^^

		deeply_utils.pp('from Skip to Import -- removing %s from unmatched_incoming' % field_n, unmatched_incoming)
		unmatched_incoming.remove(field_n)

		drag_from_el.closest('tr').remove()  # when source is 'Skip', remove the entire row (instead of calling remove_data_attributes)
		deeply_utils.pp('on_drop -- finished drag: Skip to Import', '\n' * 2)

	# --------------------------
	# move within top 'Import' (change the mapping, e.g. after 'match by column order' or dragged to the wrong place
	# destination could be empty or non-empty
	# future TBD: handle case of dropping onto itself
	#  and (drag_from_el.tagName == 'TD') and (event.target.tagName in ('TD', 'DIV'))
	elif current_from_column_name and current_to_column_name: # and (event.target.innerHTML == '' or event.target.innerHTML != ''):

		if drag_to_el.innerHTML:
			# if there's something in the destination, preserve it in order to swap
			swap_field_n = drag_to_el.getAttribute('data-field')  # get here before updating
			swap_column_name = drag_to_el.getAttribute('data-name')
			swap_column_value = drag_to_el.getAttribute('data-value')
			swap_html = drag_to_el.innerHTML

		else:
			# dragging to an empty space
			swap_field_n = ''
			swap_column_name = ''
			swap_column_value = ''
			swap_html = ''

		# --- same code as above ---
		drag_to_el.setAttribute('data-field', field_n)
		drag_to_el.setAttribute('data-name', drag_from_column_name)
		drag_to_el.setAttribute('data-value', drag_from_column_value)

		drag_to_el.innerHTML = current_html  # drag/drop row_handle + name
		drag_to_el.nextElementSibling.innerHTML = drag_from_column_value

		deeply_utils.pp('\n^^^' * 3, 'NEW col_matches_dict[current_to_column_name] "%s" from "%s" to "%s"' % (current_to_column_name, col_matches_dict.get(current_to_column_name), field_n))

		col_matches_dict[current_to_column_name] = field_n
		# ^^^ end ^^^

		# swap if any: works whether swapping with something or nothing
		# (also same code but different target)
		drag_from_el.setAttribute('data-field', swap_field_n)
		drag_from_el.setAttribute('data-name', swap_column_name)
		drag_from_el.setAttribute('data-value', swap_column_value)

		drag_from_el.innerHTML = swap_html  # drag/drop row_handle + name
		drag_from_el.nextElementSibling.innerHTML = swap_column_value  # might be empty string

		deeply_utils.pp('\n&&&' * 3, 'SWAP: col_matches_dict[current_from_column_name] "%s" from "%s" to "%s"' % (current_from_column_name, col_matches_dict.get(current_from_column_name), swap_field_n))
		col_matches_dict[current_from_column_name] = swap_field_n

		deeply_utils.pp('on_drop -- finished drag: move within Import', '\n' * 2)

	else:
		# e.g. drop from bottom 'Skip' to different bottom 'Skip' ... this will NOT change the order
		deeply_utils.pp('\n' * 2, 'mystery drop ... skipped aio.run(update_n_of_m())')
		return

	# deeply_utils.pp(col_matches_dict)
	aio.run(update_n_of_m())


def make_label_lookup():
	global name_label_mapping

	for column_dict in main_table_header_data:
		if column_dict['name'] in table_utils.built_in_fields:
			# skip internal / parenthetical fields
			continue

		name_label_mapping[column_dict['name']] = column_dict['label']


# -----
async def main():
	global incoming
	# globals not modified here: main_table_header_data

	deeply_utils.pp('-\n' * 5, 'import_dialog.py')
	deeply_utils.pp('main_table_header_data', main_table_header_data)

	global app_config
	app_config = await window.get_app_config()
	table_utils.app_config = app_config  # must be async so cannot be set on import

	import_filename = window['__PROPS__']['name']

	make_label_lookup()  # from main_table_header_data

	incoming = await get_incoming()

	await compare_update_build()  # assume the first row is a header

	await ensure_mappings_table()

	document['importing-from-to'].innerHTML = 'Importing %s from "<code>%s</code>" into "<code>%s</code>"' % (
		deeply_utils.plural_phrase(len(incoming['row_dicts']), 'record'),
		import_filename,
		table_name,
		)

	document['import-btn'].bind('click', lambda e: aio.run(merge_data(e)))
	document['cancel-btn'].bind('click', lambda e: aio.run(close_dialog(e)))
	document['match-col-order'].bind('click', lambda e: aio.run(match_by_col_order(e)))

	# radio buttons: First row is (header, data)
	document['header-row'].bind('click', lambda e: aio.run(on_radio_change(e)))
	document['no-header'].bind('click', lambda e: aio.run(on_radio_change(e)))

	current_window.listen('tauri://close-requested', lambda *_args: aio.run(tauri.window.getCurrentWindow().destroy()))

page_runner.register_for_js()  # hardcoded for 'main'
