use crate::{common, config};
use crate::cmd::window::{create_window, NewWindowOptions};
use anyhow::Result;

/// Create main window
pub async fn create(app: &tauri::App) -> Result<()> {
    let product_name = common::get_product_name(app.handle().clone());
    let product_id = common::get_product_id(app.handle().clone());
    let home = format!("/windows/home_{}.html", product_id);

    let options = NewWindowOptions {
		width: Some(config::WINDOW_SIZE_MAP.get_width("main")),
		height: Some(config::WINDOW_SIZE_MAP.get_height("main")),
		allow_duplicate: None,
		props: None,
		init_script: None,
		parent_label: None,
	};

    create_window(
        app.handle().clone(),
        product_name,
        "main".to_string(),
        home,
        options,
        false, // not a data window
        None,  // no insert_id
        None   // no disable_file_drop
    ).await;

    Ok(())
}
