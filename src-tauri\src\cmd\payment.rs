use crate::{ common, config, lemon_squeezy, license };
use tauri::{ async_runtime::spawn, Manager };

#[tauri::command]
pub async fn open_buy_page(app_handle: tauri::AppHandle) {
	let label = "buy";
	let width = config::WINDOW_SIZE_MAP.get_width(label);
	let height = config::WINDOW_SIZE_MAP.get_height(label);

	let app_handle_clone = app_handle.clone();

	// TBD July 2025: maybe refactor -- use crate::cmd::window::{create_window, NewWindowOptions};
	let _window = tauri::WebviewWindowBuilder::new(
		&app_handle,
		label,
		tauri::WebviewUrl::App("/windows/buy.html".into())
	)
	.title("Buy")
	.inner_size(width, height)
	.visible(true)
	.accept_first_mouse(true)  // macOS: 1st click performs action rather than merely activating the window
	.auto_resize()
	.on_navigation(move |url| {
		// catch URL from Lemon Squeezy 'Success'
		// https://docs.lemonsqueezy.com/help/licensing/generating-license-keys#redirect-variable

		let app_handle_inner = app_handle_clone.clone();

		for (key, val) in url.query_pairs() {
			if key == "license_key" {
				if let Some(window) = app_handle_inner.get_webview_window("buy") {
					window.close().unwrap();
				}
				let val_owned = val.into_owned();
				spawn(activate_license(app_handle_inner, val_owned));
				break;
			}
		}
		log::trace!("navigating to url {}", url);
		true // Allow navigation
	})
	.build()
	.unwrap();
}

pub async fn activate_license(app_handle: tauri::AppHandle, license_key: String) -> Result<(), String> {
	let lm = lemon_squeezy::LemonSqueezy::try_create().map_err(|e| e.to_string())?;
	let instance_id = lm.activate(&license_key).await.map_err(|e| e.to_string())?;
	license::save(&app_handle, license_key, instance_id).map_err(|e| e.to_string())?;
	// Restart app with new license
	tauri::process::restart(&app_handle.env());
}

#[tauri::command]
pub async fn revoke_license(app_handle: tauri::AppHandle) -> Result<(), String> {
	let mut lm = lemon_squeezy::LemonSqueezy::try_create().map_err(|e| e.to_string())?;
	let license = license::load(&app_handle).map_err(|e| e.to_string())?;
	lm.deactivate(&license.license_key, &license.instance_id).await.map_err(|e| e.to_string())?;
	license::delete(&app_handle).map_err(|e| e.to_string())?;
	// Restart app with new license
	tauri::process::restart(&app_handle.env());
}

#[tauri::command]
pub fn get_checkout_url(app_handle: tauri::AppHandle) -> String {
	// FYI: Test card from https://docs.lemonsqueezy.com/help/getting-started/test-mode
	// Visa 4242 4242 4242 4242

	let product_name = common::get_product_name(app_handle.clone());

	match product_name.as_str() {
		"Data Deeply" => "https://makedeeply.lemonsqueezy.com/buy/e26192aa-2360-46f2-a59c-81ba2eec5387?media=0",
		"Now Deeply" => "https://makedeeply.lemonsqueezy.com/checkout/buy/ecc7b0cf-a2b7-41c3-9d41-d5e31f3c6a3a?media=0",
		_ => "",
	}.to_string()
}
