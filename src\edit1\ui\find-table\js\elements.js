export const tableEl = document.getElementById('table');

// export const bottomSection = document.getElementById('scroll-to');
export const replaceInBox = document.querySelector('#scroll-to .replace-in-box');
export const replaceSetBox = document.querySelector('#scroll-to .replace-set-box');

export const findLabel = document.getElementById('find-value-label');
export const findIn = document.getElementById('find-value');
export const replaceIn = document.getElementById('replace-value');

export const caseCheck = document.getElementById('case-sensitive');
export const entireWordCheck = document.getElementById('entire-word');
export const regexCheck = document.getElementById('grep');

export const beforeCheck = document.getElementById('before-check');
export const beforeCount = document.getElementById('before-count');

export const selectedCheck = document.getElementById('selected-check');
export const selectedCount = document.getElementById('selected-count');

export const afterCheck = document.getElementById('after-check');
export const afterCount = document.getElementById('after-count');

// export const refreshBtn = document.getElementById('refresh');
export const tabulateBtn = document.getElementById('tabulate-btn');

export const openDocsCheck = document.getElementById('open-docs');
export const selFoldersCheck = document.getElementById('selected-folders');
export const selFilesCheck = document.getElementById('selected-files');

// export function refreshOrTabulateToggle(openDocs) {
// 	refreshBtn.style.display = openDocs ? 'none' : 'block' + '!important';
// 	tabulateBtn.style.display = openDocs ? 'block' : 'none' + '!important';
// }

// export const searchEl = document.querySelector('#search.search-tab');
export const searchEl = document.querySelector('#finder-form');
export const newFilesBtn = document.getElementById('new-files');
export const newDirsBtn = document.getElementById('new-dirs');
export const filesListEl = document.getElementById('files-list');
export const foldersListEl = document.getElementById('folders-list');

// Filters
export const matchTextArea = document.getElementById('gitMatchArea');
export const ignoreTextArea = document.getElementById('gitIgnoreArea');
export const useGitIgnoreCheck = document.getElementById('useGitIgnoreCheck');
export const omitInvisibleFilesCheck = document.getElementById('omitInvisibleFilesCheck');
export const omitInvisibleFoldersCheck = document.getElementById('omitInvisibleFoldersCheck');
export const reviewBtn = document.getElementById('review-btn');
