"""
sort_dialog.py
"""

from browser import document, window, aio
import json
import tauri

# local imports
import shortcuts  # runs the bind commands on import
import table_utils

sort_editor_table = None
header_data = window['__PROPS__'].header_data
sort_order = 'asc'
initial_sorters = window['__PROPS__'].initial_sorters
not_sorted = False  # refactor TBD: probably switch to is_sorted and update appropriate code

current_window = tauri.window.getCurrentWindow()
label = current_window.label[:current_window.label.rindex('_sort_editor')]

def create_sort_editor(evt):
	global sort_editor_table
	sort_columns = [
		{
			'field': 'combined',
			'formatter': sort_editor_combined_column_formatter,
			'width': 'auto',
			'headerSort': False,
			'cellClick': on_combined_column_click,
			'headerFilter': 'input',
			'editorParams': {'elementAttributes': {'spellcheck': False}},
		}
	]

	sort_data = []

	sort_table_config = {
		'height': 'auto',
		'rowHeight': 36,
		'data': sort_data,
		'layout': 'fitColumns',
		'headerVisible': False,
		'movableRows': True,
		'columns': sort_columns,
		'rowHeader': {
			'rowHandle': True,
			'formatter': 'handle',
			'width': table_utils.handle_width,
		},
		'editorParams': {'elementAttributes': {'spellcheck': False}},
	}
	sort_editor_table = window.Tabulator.new(
		'#sort-table',
		sort_table_config,
	)

	sort_editor_table.on('rowMoved', on_sort_table_row_moved)
	# sort_editor_table.on('tableBuilt', lambda: (set_initial_sorters(), left_right_align()))
	sort_editor_table.on('tableBuilt', set_initial_sorters)
	populate_column_dropdown()
	update_sort_button_label()


def populate_column_dropdown():
	selected = False
	existing_columns = [
		row.getData()['label']
		for row in sort_editor_table.getRows()
	]

	column_select = document['column-select']
	column_select.clear()
	options_added = False

	for col in header_data:
		if col['label'] not in existing_columns and col['label'].lower() not in table_utils.exclude:
			# for now: can't sort on icons (exclude) -- but should be able to sort by restore_window_icon_field
			option = document.createElement('option')
			option.text = col['label']
			option.value = col['name']
			if not selected and not col['label'].startswith('('):
				option.selected = True
				selected = True

			column_select.appendChild(option)
			options_added = True

		sort_button = document['done']
		sort_toggle_button = document['sort-toggle']
		if options_added:
			sort_button.disabled = False
			sort_toggle_button.disabled = False
		else:
			sort_button.disabled = True
			sort_toggle_button.disabled = True


def update_sort_button_label():
	sort_button = document['sort-toggle']
	sort_button.textContent = ('↑' if sort_order == 'asc' else '↓')


def sort_editor_combined_column_formatter(cell, *args):
	row_data = cell.getRow().getData()
	row_id = row_data['name']

	# Create a container for the combined cell
	combined_container = document.createElement('div')
	combined_container.style.display = 'flex'
	combined_container.style.alignItems = 'center'
	combined_container.style.justifyContent = 'space-between'
	combined_container.style.width = '200px'
	combined_container.style.paddingLeft = '20px'

	# Remove button
	remove_btn = document.createElement('button')
	remove_btn.textContent = 'x'
	remove_btn.onclick = lambda event: remove_sort_column(event, cell)
	combined_container.appendChild(remove_btn)

	# Column label
	label_span = document.createElement('span')
	label_span.textContent = cell.getRow().getData()['label']
	combined_container.appendChild(label_span)

	# Sort order button
	sort_order_btn = document.createElement('button')
	order = cell.getRow().getData()['sort_order']
	sort_order_btn.textContent = '↑' if order == 'asc' else '↓'
	sort_order_btn.onclick = lambda event: toggle_sort_order(event, cell)
	sort_order_btn.id = f'sort-btn-{row_id}'

	combined_container.appendChild(sort_order_btn)

	row_data = cell.getRow().getData()
	row_id = row_data['name']
	sort_order_btn.setAttribute('data-row-id', row_id)

	return combined_container


def set_initial_sorters():
	global initial_sorters
	initial_sorters.reverse()
	if len(initial_sorters) > 0:
		for sorter in initial_sorters:
			add_to_sorting_editor(sorter['column'], sorter['dir'])
		initial_sorters = []
	else:
		add_generic_info()


def add_generic_info(show_table=False):
	global not_sorted
	if show_table:
		document['not-sorted-info'].classList.add('hide-info')
		document['sort-table'].classList.remove('hide-info')
		not_sorted = False
	else:
		document['not-sorted-info'].classList.remove('hide-info')
		document['sort-table'].classList.add('hide-info')
		not_sorted = True


def remove_sort_column(event, cell):
	if event:
		event.stopPropagation()
	try:
		cell.getRow().delete()
	except Exception as e:
		cell.delete()
	populate_column_dropdown()
	update_main_table_sort()

	rows = sort_editor_table.getRows()
	if len(rows) == 0:
		add_generic_info()
		# left_right_align()


def add_sort_column(evt):
	global not_sorted

	# print('sort_dialog: add_sort_column')

	if evt:
		evt.stopImmediatePropagation()

	if not_sorted:
		add_generic_info(True)

	selected_label = document['column-select'].selectedOptions[0].text
	fieldname = selected_label

	selected_column_key = next(
		(
			item['name']
			for item in header_data
			if item['label'] == fieldname
		),
		None,
	)

	if selected_column_key is None:
		print('ERROR in sort_dialog.py -- key not found for selected label: {}'.format(fieldname))
		return

	sort_editor_table.addRow(
		{
			'name': selected_column_key,
			'label': selected_label,
			'sort_order': sort_order,
		}
	)

	populate_column_dropdown()
	update_main_table_sort()
	# left_right_align()


def add_to_sorting_editor(field, order):
	label = next(
		(
			item['label']
			for item in header_data
			if item['name'] == field
		),
		field,
	)

	sort_editor_table.addRow(
		{'name': field, 'label': label, 'sort_order': order}
	)

	update_main_table_sort()
	populate_column_dropdown()


def update_sort_button_text(cell, new_order):
	row_data = cell.getRow().getData()
	row_id = row_data['name']
	sort_button_id = f'sort-btn-{row_id}'
	sort_order_btn = document.getElementById(sort_button_id)

	if sort_order_btn:
		sort_order_btn.textContent = '↑' if new_order == 'asc' else '↓'
	else:
		print(f'ERROR: Sort order button with ID {sort_button_id} not found.')


def toggle_sort_order(event, cell):
	event.stopImmediatePropagation()
	current_order = cell.getRow().getData()['sort_order']
	new_order = 'desc' if current_order == 'asc' else 'asc'
	cell.getRow().update({'sort_order': new_order})

	update_sort_button_text(cell, new_order)
	update_main_table_sort()


def toggle_sort_order_in_control_panel(event):
	global sort_order
	event.stopImmediatePropagation()
	sort_order = 'desc' if sort_order == 'asc' else 'asc'
	update_sort_button_label()

	add_sort_column(None)


#events
def on_sort_table_row_moved(row):
	update_main_table_sort()


def on_combined_column_click(event, cell):
	target = event.target
	if 'sort-order-btn' in target.classList:
		toggle_sort_order(event, cell)
	elif 'remove-btn' in target.classList:
		remove_sort_column(event, cell)


def get_sorters():
	sorters = []
	key = None
	label = None
	if sort_editor_table:
		for row in sort_editor_table.getRows():
			data = row.getData()
			label = data['label']
			key = next(
				(
					item['name']
					for item in header_data
					if item['label'] == label
				),
				None,
			)

			if key:
				sorters.append({
					'column': key,'dir': data['sort_order']
				})
			else:
				window.console.error(f'No key found for label: {label}')
		sorters.reverse()
	return sorters


def update_main_table_sort():
	args = {'sorters': json.dumps(get_sorters()), 'label': label}
	# print('sort_dialog.py - update_main_table_sort - args:', args)
	tauri.invoke('sort_main_table', args)  # TBD: call new run tauri invoke?


def update_sort_table(tauri_message):
	global not_sorted

	if not_sorted:
		add_generic_info(True)

	field = json.loads(dict(tauri_message)['payload'])
	existing_row = None
	button = None

	for row in sort_editor_table.getRows():
		if row.getData()['name'] == field:
			existing_row = row
			break
	if existing_row:
		current_order = existing_row.getData()['sort_order']
		new_order = None
		if current_order == 'desc':
			remove_sort_column(None, existing_row)
		else:
			new_order = 'desc'
			existing_row.update({'sort_order': new_order})

			button = existing_row.getElement().querySelector(f'button[data-row-id="{field}"]')

		if button:
			button.textContent = '↑' if new_order == 'asc' else '↓'
			update_main_table_sort()
	else:
		add_to_sorting_editor(field, 'asc')


def recreate_and_apply_sorters(tauri_message):
	global header_data, initial_sorters
	sorters = json.loads(dict(tauri_message)['payload'])['sorters']
	columns = json.loads(dict(tauri_message)['payload'])['columns']
	header_data = columns
	initial_sorters = sorters
	if sort_editor_table:
		sort_editor_table.destroy()
		create_sort_editor(None)

def rearrange_columns(tauri_message):
	global header_data, initial_sorters
	header_data = sorted(header_data, key=lambda x: json.loads(dict(tauri_message)['payload'])['new_order'].index(x['name']))
	initial_sorters = json.loads(dict(tauri_message)['payload'])['sorters']
	if sort_editor_table:
		sort_editor_table.destroy()
		create_sort_editor(None)


def left_right_align():
	return
# 	print('\n-' * 3, 'left_right_align')
# 	table_rect = document.querySelector('.tabulator-table').getBoundingClientRect()
# 	root = document.documentElement
# 	root.style.setProperty('--sort-table-width', f'{table_rect.width}px')


current_window.listen('sort_from_main_table', lambda payload: sync_deb_sort_from_table(payload))
current_window.listen('columns_edited', lambda payload: sync_deb_cols_changed(payload))
current_window.listen('change_sort_col_order', lambda payload: sync_deb_col_order(payload))

current_window.listen('tauri://close-requested', lambda *_args: aio.run(tauri.window.getCurrentWindow().destroy()))

sync_deb_sort_from_table = table_utils.debounce(lambda payload: update_sort_table(payload), 1000)
sync_deb_cols_changed = table_utils.debounce(lambda payload: recreate_and_apply_sorters(payload), 1000)
sync_deb_col_order = table_utils.debounce(lambda payload: rearrange_columns(payload), 1000)

document['done'].bind('click', add_sort_column)
document['sort-toggle'].bind('click', toggle_sort_order_in_control_panel)

if len(initial_sorters) > 0:
	not_sorted = False
	add_generic_info(True)

create_sort_editor(None)  # no app_config so this should suffice

