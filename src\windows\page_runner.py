# app_runner.py
from browser import aio, document, window
import sys

print('*********** imported page_runner')

def register_for_js():
	"""Automatically find and register the main function"""
	# Get the calling module
	frame = sys._getframe(1)
	calling_module = frame.f_globals

	if 'main' in calling_module:
		main_func = calling_module['main']

		# -----
		def run_main():
			aio.run(main_func())
		# -----

		# python_runner is called by init_this_page
		window.python_runner = run_main

		if not hasattr(window, 'python_main_registered'):
			window.python_main_registered = True
			event = document.createEvent('CustomEvent')

			# send signal to be caught by init_this_page.js
			event.initCustomEvent('python-ready', True, True, None)
			document.dispatchEvent(event)
