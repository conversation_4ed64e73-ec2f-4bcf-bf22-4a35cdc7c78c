use std::{ fs, path:: PathBuf};

use chrono::Local;
use serde_json::json;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Listener, Manager};

use crate::edit1::{auto_save::AutosaveFile, create_new_editor, create_new_editor_with_id, load_autosave_map, save_autosave_map, AutosaveFileData, MyFile, State};



#[tauri::command]
pub fn autosave_file(app: AppHandle, name: String, original_path: Option<String>, content: String) -> Result<AutosaveFile, String> {
    let data_dir = app.path().app_data_dir().unwrap();
    let autosave_dir = data_dir.join("autosave");

    let mut map = load_autosave_map(&app)?;

    let temp_filename = format!("{}.autosave", name);
    let temp_path = autosave_dir.join(&temp_filename);

    fs::write(&temp_path, content).map_err(|e| format!("Failed to write temp file: {}", e))?;

    println!("Original Path {:?}", original_path);
    let autosave = AutosaveFile {
        window_label: name.clone(),
        original: original_path.clone(),
        autosave: temp_path.to_string_lossy().to_string(),
        last_saved: Local::now().to_string(),
        iclose: false,
    };

    map.files.entry(original_path.clone().unwrap_or_else(|| name.clone()))
        .or_insert_with(|| autosave.clone());

    save_autosave_map(&app, &map)?;

    Ok(autosave)
}


#[tauri::command]
pub fn update_iclose_flag(app: AppHandle, key: String, iclose: bool) -> Result<(), String> {
    let mut map = load_autosave_map(&app)?;

    if let Some(entry) = map.files.get_mut(&key) {
        entry.iclose = iclose;
        save_autosave_map(&app, &map)?;
        Ok(())
    } else {
        Err(format!("No autosave entry found for key '{}'", key))
    }
}


#[tauri::command]
pub fn delete_autosave(app: AppHandle, key: String) -> Result<bool, String> {
    let mut map = load_autosave_map(&app)?;
	println!("Delete Autosave {:?}",key);
    if map.files.remove(&key).is_some() {
        save_autosave_map(&app, &map)?;
		println!("Deleted Autosave {:?}",key);
        Ok(true)
    } else {
		println!("Autosave Not Found {:?}",key);
        Ok(false)
    }
}

#[tauri::command]
pub async fn open_unsaved_autosave(app: AppHandle) -> Result<Vec<AutosaveFile>, String> {
    let map = load_autosave_map(&app)?;

    let unsaved_entries: Vec<AutosaveFile> = map.files
        .values()
        .filter(|entry| !entry.iclose)
        .cloned()
        .collect();

	for entry in unsaved_entries.iter() {
		println!("Entry {:?}", entry);

		let autosave_file: MyFile =
			MyFile::read_file_data(PathBuf::from(entry.autosave.clone())).unwrap();
		let e = entry.clone();

		let window = if entry.original.is_none() {
			create_new_editor_with_id(app.clone(), entry.window_label.clone(), None).await
		} else {
			create_new_editor(app.clone(), entry.original.clone()).await
		};

		let w = window.clone();

		window.listen("window:on-loaded", move |_| {
			if let Some(original_path) = e.original.clone() {
				let my_file =
					MyFile::read_file_data(PathBuf::from(original_path)).expect("error");
				w.emit_to(
					w.label(),
					"file:on-open",
					&State {
						label: w.label().to_string(),
						file: my_file,
					},
				)
				.unwrap();
			}

			w.emit_to(
				w.label(),
				"editor:load-autosave",
				json!({ "content": autosave_file.content, "autosave": e }),
			)
			.unwrap();
    	});
	}


    Ok(unsaved_entries)
}

// #[tauri::command]
// pub async fn get_file_autosave(app: AppHandle,key:String) -> Result<Option<AutosaveFile>, String> {
//     let map = load_autosave_map(&app)?;

//     let unsaved_entrie: Option<AutosaveFile> = map.files.get(key.as_str()).cloned();


//     Ok(unsaved_entrie)
// }

#[tauri::command]
pub async fn get_file_autosave(app: AppHandle, key: String) -> Result<Option<AutosaveFileData>, String> {
    let map = load_autosave_map(&app)?;

    let entry = map
        .files
        .get(&key)
        .filter(|entry| entry.iclose)
        .cloned();
	if let Some(e) = entry {
		let autosave_file: MyFile =
				MyFile::read_file_data(PathBuf::from(e.autosave.clone())).unwrap();
		let result = AutosaveFileData { autosave: e, content: autosave_file.content };
		return Ok(Some(result));

	}
    Ok(None)
}

