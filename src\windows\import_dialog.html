<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Import</title>

		<!-- Brython -->
		<script type="text/javascript" src="/lib/brython/brython.js"></script>
		<script type="text/javascript" src="/lib/brython/brython_stdlib.js"></script>

		<!-- imports -->
		<script src="/common/tauri.py" type="text/python" id="tauri"></script>
		<script src="/common/app_data.py" type="text/python" id="app_data"></script>
		<script src="/common/file_sqlite.py" type="text/python" id="file_sqlite"></script>
		<script src="/common/table_utils.py" type="text/python" id="table_utils"></script>

		<script src="/windows/shortcuts.py" type="text/python" id="shortcuts"></script>

		<script type="module" src="/lib/passive-events/index.js"></script>

		<!-- styles -->
		<link rel="stylesheet" href="/common/makedeeply.css" />
		<link rel="stylesheet" href="/windows/editors.css" />
		<link rel="stylesheet" href="/windows/file_data.css" />
		<link rel="stylesheet" href="/windows/db/details.css" />
		<link rel="stylesheet" href="/windows/import_dialog.css" />
	</head>

	<body id="import-dialog" class="editor-page">
		<div id="editor-container">
			<div id="importing-from-to"></div>

			<table id="table" class="import-table">
				<thead>
					<tr>
						<th colspan="2">Current</th>
						<th>Import</th>
						<td class="record1-label">Record 1</td>
					</tr>
				</thead>
				<tbody id="import-table-body">

				</tbody>
			</table>

			<div id="after-table">
				<div class="paragraph">
					<button id="match-col-order" class="btn small">Match By Column Order</button>
				</div>

				<div id="header-row-container">
					<div>First row is </div>
					<div class="first-row-wrapper">
						<div>
							<input type="radio" id="header-row" name="first-row" value="header" checked />
							<label for="header-row" id="header-row-label">header</label>
						</div>
						<div>
							<input type="radio" id="no-header" name = "first-row" value="data"/>
							<label for="no-header" id="no-header-label">data</label>
						</div>
					</div>
				</div>
				<div id="importing-n-m" class="button-font-size paragraph"></div>

				<div id="ok-cancel-btn-group">
					<button id="cancel-btn" class="ok-cancel">Cancel</button>
					<button id="import-btn" class="ok-cancel">Import</button>
				</div>
				<textarea id="error-text-area"></textarea>
			</div>
		</div>

		<script src="process_files.py" type="text/python"></script>
		<script src="import_dialog.py" type="text/python"></script>
	</body>
</html>
