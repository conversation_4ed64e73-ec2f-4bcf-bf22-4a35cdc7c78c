@import './alert.css';

/* Sizes */
:root{
	--header-footer-h:24px;
}

/* was 'system.css' */
:root {
	--xm-text: 0.6rem;
	--sm-text: 0.8rem;
	--menu-text: 1rem;
	--lg-text: 1.2rem;
	--xlg-text: 1.4rem;
}

/* Light Theme */
:root {
	--hover-bg: #eef;

	--bg-color: red; /* #ddd; */
	--primary-text: #111;
	--input-bg: white;
	--input-border: #888;

	--neutral-btn-bg: #ccc;
	--disabled-btn-bg: #eee;
	--disabled-btn: #aaa;

	--titlebar-bg: #bbb;
	--highlight-color: #ffff0054;
	--selection-color: #ff6a0054;

	/* Menu */
	--menu-color: #eee;
	--menu-border-color: #aaa;

	/* --selection-match-color: #00000031; */
	--neutral: #fff;
	--error: #800000;
	--success: #006000;

	--no-active-editor-bg: #ddd;
	--no-active-active-line-bg: #dddddd80;
	/* editor selection color */
	--pale_tan: hsl(33, 55%, 77%);

	/* editor/gutters colors */
	--editor-blured-bg: #eee;
	--gutters-blured-bg: #eee;
	--gutters-bg: #eee;

	/* Split editor colors */
	--resizer-bg: #eee;
	--resizer-border-clr: #888;
	--resizer-circle-clr: #333;
	--resizer-thumb-bg: #666;
}

.dark-theme {
	--menu-color: #555d;
	--hover-bg: #778;

	--bg-color: #333;
	--primary-text: #eee;
	--input-bg: #555;
	--input-border: #888;

	--neutral-btn-bg: #555;
	--disabled-btn-bg: #888;
	--disabled-btn: #ccc;

	--titlebar-bg: #222;
	--highlight-color: #66660673;
	--selection-color: #2f00ffe5;
	/* --selection-match-color: #ffffff3b; */
	--neutral: #333;
	--error: #f85e5e;
	--success: #64e764;
}

* {
	transition: color 0.2s, background 0.3s;
}
