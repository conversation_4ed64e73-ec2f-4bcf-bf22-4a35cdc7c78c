import fs from 'fs';
import os from 'os';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';

// Current file path
const __filename = fileURLToPath(import.meta.url);
// Parent folder of this file
const __dirname = dirname(__filename);

/********** ENV **********/
process.env.PLAN = process.env.PLAN || 'PAID'; // default plan is paid
process.env.RUST_LOG = '';
export const skipBuild = process.env.SKIP_BUILD === '1'; // skip build if binary exists
export const isRelease = process.env.RELEASE === '1'; // use cargo release mode

/********** CONFIG **********/

export const cargoArgs = ['build'];
if (isRelease) {
	cargoArgs.push('--release');
}
// Binary Paths
export const cargoHome = process.env['CARGO_HOME'] ?? path.resolve(os.homedir(), '.cargo');
export const srcTauriPath = path.resolve(__dirname, '../../src-tauri');
export const application = path.resolve(
	__dirname,
	srcTauriPath,
	isRelease ? 'target\\release\\edit_deeply.exe' : 'target\\debug\\edit_deeply.exe',
);

// Data folders
export const appDataPath = path.resolve(process.env['LOCALAPPDATA'], 'com.makedeeply.editdeeply');
export const localAppDataPath = path.resolve(process.env['APPDATA'], 'com.makedeeply.editdeeply');
export const testingPath = path.resolve(appDataPath, 'tests');

// Read rust config
const rustConfigPath = path.join(srcTauriPath, 'src/config.rs');
const rustConfig = fs.readFileSync(rustConfigPath).toString();

// Read database path from Rust
export const dbFilename = rustConfig.match(/DATABASE_FILE_NAME.+= \"(.+)\";/)[1];

// Read main window label from Rust
export const mainWindowLabel = rustConfig.match(/MAIN_WINDOW_LABEL.+= "(.+)";/)[1];

export const appDbPath = path.resolve(localAppDataPath, dbFilename);
export const tmpDbPath = path.resolve(os.tmpdir(), dbFilename);
export const webviewDirName = 'EBWebView';

// Webview and selenium
export const webDriverUrl = 'http://127.0.0.1:4444/';
export const tauriDriverPath = path.resolve(cargoHome, 'bin/tauri-driver.exe');
export const nativeDriverPath = path.resolve(srcTauriPath, 'msedgedriver.exe');
export const tauriDriverArgs = ['--native-driver', nativeDriverPath];
export const webViewOptions = {};
export const applicationArgs = [`--db-path=${tmpDbPath}`];

// Test timeouts
export const waitForActionTimeout = 10000;
export const waitForDbTimeout = 6000;
export const waitforInitTimeout = 20000;
export const afterTimeout = 10000;
// set timeout to 2 minutes to allow the program to build if it needs to
export const beforeTimeout = 120000;

// Checks
if (!dbFilename) {
	throw `Cannot parse database filename from ${rustConfigPath}`;
}
if (!fs.existsSync(nativeDriverPath)) {
	throw new Error(`Native driver not found in ${nativeDriverPath} Please extract it into`);
}
if (!fs.existsSync(tauriDriverPath)) {
	throw new Error(`Tauri driver not found at ${tauriDriverPath}. Please install it with cargo install`);
}
// Logs
console.log(`Using native driver at ${nativeDriverPath}`);
console.log(`Using tauri driver at ${tauriDriverPath}`);
console.log(`Using temp database path at ${tmpDbPath}`);
