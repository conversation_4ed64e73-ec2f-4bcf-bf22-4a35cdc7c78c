export const minMaxFilterEditor = function (cell, onRendered, success, cancel, editorParams) {
	var end;
	var container = document.createElement('span');

	//create and style inputs
	var start = document.createElement('input');

	start.setAttribute('type', 'text'); // "number" on macOS shows useless up/down arrows
	start.setAttribute('inputmode', 'numeric'); // for mobile
	start.setAttribute('oninput', "this.value = this.value.replace(/[^0-9]/g, '')");

	start.setAttribute('placeholder', 'Min');
	start.setAttribute('min', 0);
	start.style.padding = '4px';
	start.style.marginRight = '1px';
	start.style.width = '50%';
	start.style.boxSizing = 'border-box';

	start.value = cell.getValue();

	function buildValues() {
		success({
			start: start.value,
			end: end.value,
		});
	}

	function keypress(e) {
		if (e.keyCode == 13) {
			// return
			buildValues();
		}

		if (e.keyCode == 27) {
			// esc
			cancel();
		}
	}

	end = start.cloneNode();
	end.style.marginLeft = '1px';
	end.setAttribute('placeholder', 'Max');

	start.addEventListener('change', buildValues);
	start.addEventListener('blur', buildValues);
	start.addEventListener('keydown', keypress);

	end.addEventListener('change', buildValues);
	end.addEventListener('blur', buildValues);
	end.addEventListener('keydown', keypress);

	container.appendChild(start);
	container.appendChild(end);
	return container;
};

export function minMaxFilterFunction(headerValue, rowValue, rowData, filterParams) {
	//headerValue - the value of the header filter element
	//rowValue - the value of the column in this row
	//rowData - the data for the row being filtered
	//filterParams - params object passed to the headerFilterFuncParams property

	if (rowValue) {
		if (headerValue.start != '') {
			if (headerValue.end != '') {
				return rowValue >= headerValue.start && rowValue <= headerValue.end;
			} else {
				return rowValue >= headerValue.start;
			}
		} else {
			if (headerValue.end != '') {
				return rowValue <= headerValue.end;
			}
		}
	}

	return true; //must return a boolean, true if it passes the filter.
}

export function removeRedundantData(dataArray) {
	const uniqueMap = new Map();

	return dataArray.filter((data) => {
		const key = `${data.file_folder}/${data.file_name}`;

		if (!uniqueMap.has(key)) {
			uniqueMap.set(key, true);
			return true;
		}

		return false;
	});
}
