"""
export_dialog.py
"""

from browser import aio, document, window
import javascript
import os

# local to load first
import page_runner
import tauri

# local imports
import deeply_utils
import shortcuts  # runs the bind commands on import
import save_utils
import table_utils
import app_data

app_config = None  # must be loaded async

# -----
current_window = tauri.window.getCurrentWindow()
main_window_label = current_window.label[:current_window.label.rindex('_export_editor')]

export_editor_table = None
static_layout_table = None
save_table = None
last_format = window.localStorage.getItem('format')

export_editor_data = {
	'Rows': 'Found Set',
	'Columns': 'Current',
	'Header': 'OriginalName',
	'Sort': 'CurrentSort',
	}

# refactor TBD: is there a better way of sharing data?
current_filters = window.__PROPS__.current_filters
header_data = window.__PROPS__.header_data
save_table_data = window.__PROPS__.save_table_data
main_table_sorters = window.__PROPS__.main_table_sorters
total_rows = window.__PROPS__.total_rows
filtered_rows = window.__PROPS__.filtered_rows

visible_columns_count = 0
for row in header_data:
	row_dict = dict(row)
	if not row_dict.get('visible', False):
		continue
	if row_dict.get('name') in table_utils.built_in_fields:
		continue
	visible_columns_count += 1

total_columns = 0

using_windows = window.__PROPS__.using_windows
using_other = window.__PROPS__.using_other


# ..................................setup export editor and save table............................................
def create_export_settings():
	deeply_utils.pp('export_dialog -- create_export_settings')

	for row in header_data:
		if dict(row)['name'] in table_utils.built_in_fields:
			row['visible'] = False

	has_search = len(current_filters) > 0
	has_layout = not all(dict(row).get('visible', False) for row in header_data)
	# has_labels = not all(row['name'] == row['label'] for row in header_data)
	export_labels = False  # unlike with search & columns, most users probably want the original column names
	has_sort = len(main_table_sorters) > 0

	row_hide_show = '' if has_search else 'hide'
	columns_hide_show = '' if has_layout else 'hide'
	sort_hide_show = '' if has_sort else 'hide'

	on_change = 'onchange="handle_radio_change(event, this)"'

	# refactor TBD: build with jinja2 or similar
	return [
		{'setting': 'Rows',
		'all': f'''
<div class="one-liner">
	<div class="labelled-pairs">
		<input type="radio" name="Rows" value="All" id="rows-all" {on_change} {"checked" if not has_search else ""}>
		<label for="rows-all">All {total_rows}</label>
	</div>
	<div class="labelled-pairs {row_hide_show}">
		<input type="radio" name="Rows" value="Found Set" id="rows-found-set" {on_change} {"checked" if has_search else ""}>
		<label for="rows-found-set">Found Set: {filtered_rows}</label>
	</div>
</div>'''},

		{'setting': 'Columns',
		'all': f'''
<div class="one-liner">
	<div class="labelled-pairs">
		<input type="radio" name="Columns" value="All" id="columns-all" {on_change} {"checked" if not has_layout else ""}>
		<label for="columns-all">All {total_columns}</label>
	</div>
	<div class="labelled-pairs {columns_hide_show}">
		<input type="radio" name="Columns" value="Current" id="columns-current" {on_change} {"checked" if has_layout else ""}>
		<label for="columns-current">Current {visible_columns_count}</label>
	</div>
</div>'''},

		{'setting': 'Header',
		'all': f'''
<div class="one-liner">
	<div class="labelled-pairs">
		<input type="radio" name="Header" value="OriginalName" id="header-original-name" {on_change} {"checked" if not export_labels else ""}>
		<label for="header-original-name">Original Name</label>
	</div>
	<div class="labelled-pairs">
		<input type="radio" name="Header" value="LabelTitleCase" id="header-label" {on_change} {"checked" if export_labels else ""}>
		<label for="header-label">Label</label>
	</div>
</div>'''},

		{'setting': 'Sort',
		'all': f'''
<div class="one-liner">
	<div class="labelled-pairs">
		<input type="radio" name="Sort" value="OriginalOrder" id="sort-original-order" {on_change} {"checked" if not has_sort else ""}>
		<label for="sort-original-order">Original Order</label>
	</div>
	<div class="labelled-pairs {sort_hide_show}">
		<input type="radio" name="Sort" value="CurrentSort" id="sort-current-sort" {on_change} {"checked" if has_sort else ""}>
		<label for="sort-current-sort">Current Sort</label>
	</div>
</div>'''},

		{'setting': '<div>Format</div>',
		'all': f'''
<div id="format-container">
	<div class="radio-column">
		<div for="">text:</div>
		<div class="labelled-pairs">
			<input type="radio" id="csv-format" name="format" value="csv" checked/>
			<label for="csv-format">csv</label>
		</div>
		<div class="labelled-pairs">
			<input type="radio" id="tsv-txt-format" name="format" value="tsv-txt" {is_checked("tsv-txt")}/>
			<label for="tsv-format">txt/tsv</label>
		</div>
	</div>
	{export_type('json')}
	{export_type('html')}
</div>'''},
		{'setting': 'Line Ending',
		'all': f'''
<div class="one-liner">
	<div class="labelled-pairs">
		<input type="radio" id="lf" name="line-ending" value="LF" {using_other}/>
		<label for="lf">LF (macOS, Linux)</label>
	</div>
	<div class="labelled-pairs">
		<input type="radio" id="crlf" name="line-ending" value="CRLF" {using_windows}/>
		<label for="crlf">CRLF (Windows)</label>
	</div>
</div>''',
			}

	]


def export_type(value):
	# refactor TBD: move these to jinja2 templates or similar
	# globals not modified here: app_config

	if value == 'json' and app_config.flags.can_handle_json:
		return f'''
				<div class="radio-column">
					<div for="">json:</div>
					<div class="labelled-pairs">
						<input type="radio" id="pretty-printed" name="format" value="pretty-printed" {is_checked("pretty-printed")}/>
						<label for="pretty-printed">pretty printed</label>
					</div>
					<div class="labelled-pairs">
						<input type="radio" id="minimal-whitespace" name="format" value="minimal-whitespace" {is_checked("minimal-whitespace")}/>
						<label for="minimal-whitespace">minimal whitespace</label>
					</div>
				</div>
		'''
	elif value == 'html' and app_config.flags.can_handle_html:
		return f'''
				<div class="radio-column">
					<div>html:</div>
					<div class="labelled-pairs">
						<input type="radio" id="with-page-template" name="format" value="with-page-template" {is_checked("with-page-template")}/>
						<label for="with-page-template">with page template</label>
					</div>
					<div class="labelled-pairs">
						<input type="radio" id="just-the-table" name="format" value="just-the-table" {is_checked("just-the-table")}/>
						<label for="just-the-table">just the table</label>
					</div>
				</div>
		'''

	return ''


def create_export_editor():
	global export_editor_table

	settings_data = create_export_settings()

	columns = [
		{'field': 'setting', 'formatter': 'html', 'headerSort': False, 'title': 'Export'},  # title is for dialog not column
		{'field': 'all', 'formatter': 'html', 'headerSort': False}
	]

	export_editor_table = window.Tabulator.new('#export-editor-content', {
		'height': 'auto',
		'data': settings_data,
		'columns': columns,
		'layout': 'fitData',
		'editorParams': {'elementAttributes': {'spellcheck': False}},
	})

	document['export-editor'].style.display = 'block'


def create_save_table():
	global save_table

	save_table = window.Tabulator.new(
		'#save-table',
		{
			'height': 'auto',
			'data': save_table_data,
			'columns': header_data,
			'movableColumns': True,
			'editorParams': {'elementAttributes': {'spellcheck': False}},
		},
	)


def create_static_layout_content_table():
	global static_layout_table

	static_layout_table = window.Tabulator.new(
		'#static-layout-table',
		{
			'height': 'auto',
			# 'rowHeight': __,  # computed ~31px
			# 'rowFormatter': lambda row: row.getElement().style.setProperty('padding-top', '5px'),
			'data': header_data,
			'layout': 'fitDataTable',
			'columns': [
				{
					'title': 'Show',
					'field': 'visible',
					'formatter': 'tickCross',
					'editor': False,
					'headerSort': False,
				},
				{'title': 'Name', 'field': 'name', 'headerSort': False, },
				{'title': 'Label', 'field': 'label', 'headerSort': False, },
				{'title': 'Sort', 'field': 'sort', 'headerSort': False, },
			],
			'editorParams': {'elementAttributes': {'spellcheck': False}},
		},
	)
	window.setTimeout(lambda: update_static_layout_table(), 150)


#.....................................static layout table utils.....................................
def update_static_layout_table_for_all():
	deeply_utils.pp('update_static_layout_table_for_all')

	rows = static_layout_table.getRows()

	for row in rows:
		cell = row.getCell('visible')
		if not cell.getValue():
			static_layout_table_change_tick_color(cell, True)

		row.getElement().classList.add('pale')
		row.getElement().classList.remove('subtle')


def update_static_layout_table_for_found_set():
	rows = static_layout_table.getRows()

	for row in rows:
		cell = row.getCell('visible')
		if cell.getValue():
			row.getElement().classList.add('pale')
			row.getElement().classList.remove('sublte')

		else:
			row.getElement().classList.remove('pale')
			row.getElement().classList.add('subtle')
			static_layout_table_change_tick_color(cell, False)


def change_column_styles_static_layout_table(column_field, color, bg_color):
	cells = static_layout_table.getColumn(column_field).getCells()
	for cell in cells:
		pass
		#cell.getElement().style.color = color
		#cell.getElement().style.backgroundColor = bg_color


def static_layout_table_change_tick_color(cell, is_exporting):
	svg_element = cell.getElement().querySelector('path')
	if svg_element is None:
		# print('SVG element not found in the cell')
		return

	if is_exporting:
		svg_element.style.fill = app_data.gray_color
	else:
		svg_element.style.fill = '#CE1515'


def get_sorters_in_sort_editor():
	sorters = main_table_sorters
	return sorters


def highlight_cell(cell):
	cell.getElement().classList.remove('subtle')
	cell.getElement().classList.add('pale')


def reset_cell(cell):
	cell.getElement().classList.remove('pale')
	cell.getElement().classList.add('subtle')


def update_static_layout_table():
	global static_layout_table
	# globals not modified here export_editor_data

	sorters = get_sorters_in_sort_editor()

	updated_header_data = [row for row in header_data]

	sort_map = {sorter['column']: (idx + 1, sorter['dir']) for idx, sorter in enumerate(sorters)}

	for row in updated_header_data:
		if row['name'] in sort_map:
			sort_order, sort_direction = sort_map[row['name']]
			direction_symbol = '↑' if sort_direction == 'asc' else '↓'
			row['sort'] = f'{sort_order}  {direction_symbol}'
	if static_layout_table:
		static_layout_table.setData(updated_header_data)

	columns_value = export_editor_data['Columns']
	header_value = export_editor_data['Header']
	sort_value = export_editor_data['Sort']

	rows = static_layout_table.getRows()
	for row in rows:
		visible_cell = row.getCell('visible')
		name_cell = row.getCell('name')
		label_cell = row.getCell('label')
		sort_cell = row.getCell('sort')

		is_visible = visible_cell.getValue()

		# for now: don't export built-in fields though perhaps someone would want one or more of file_row, mod_date, mod_note
		is_exporting_row = (columns_value == 'All' or is_visible) and (name_cell.getValue() not in table_utils.built_in_fields)

		if is_exporting_row:
			highlight_cell(visible_cell)

			if not is_visible:
				static_layout_table_change_tick_color(visible_cell, True)

		else:
			reset_cell(visible_cell)

		# Name Cell
		if header_value == 'OriginalName' and is_exporting_row:
			highlight_cell(name_cell)
		else:
			reset_cell(name_cell)

		# Label Cell
		if header_value == 'LabelTitleCase' and is_exporting_row:
			highlight_cell(label_cell)
		else:
			reset_cell(label_cell)

		# Sort Cell
		if sort_value == 'CurrentSort' and is_exporting_row and sort_cell.getValue():
			highlight_cell(sort_cell)

		else:
			reset_cell(sort_cell)

#.....................................end............................................
#.................................... save data......................................

async def assemble_and_save(filepath):
	global file_type  # refactor TBD: why is this global?

	line_ending = await save_utils.get_line_ending()  # refactor TBD: simplify

	file_type = deeply_utils.get_file_type(filepath)  # for Rust: extension with leading dot
	# print('export_confirmed: file_type', file_type)

	rows_all = document['rows-all'].checked
	columns_all = document['columns-all'].checked
	header_original = document['header-original-name'].checked
	sort_original = document['sort-original-order'].checked

	if not rows_all:
		save_table.setFilter(current_filters)

	if header_original:
		name_or_label = 'name'  # actual fieldname
	else:
		name_or_label = 'label'  # Mixed Case display name

	if not sort_original:
		save_table.setSort(main_table_sorters)

	field_names = []
	header_row_if_different = []

	for column in header_data:
		column_definition_dict = dict(column)
		if column_definition_dict['name'] in table_utils.built_in_fields:
			# for now: don't export the internal 'parenthetical' fields
			continue

		if columns_all or column_definition_dict['visible']:
			field_names.append(column_definition_dict['name'])  # need the actual fieldname for extracting the data

			if not header_original:
				header_row_if_different.append(column_definition_dict['label'])

	active_data_to_save = save_table.getData('active')

	# here's where the action is
	await save_utils.save_file(filepath, active_data_to_save, file_type, field_names, header_row_if_different)

	save_table.clearSort()
	save_table.clearFilter(True)

	await close_editor(None)


async def get_format():
	format_elements = document.querySelectorAll('[name="format"]')
	vals = None
	for f_element in format_elements:
		if f_element.checked:
			if f_element.value is 'tsv-txt':
				vals = ['txt', 'tsv']
			elif f_element.value in ['with-page-template', 'just-the-table']:
				vals = ['html', 'htm']
			elif f_element.value in ['pretty-printed', 'minimal-whitespace']:
				vals = ['json']
			else:
				vals = ['csv']
			break

	# print('get_format: vals', vals)
	vals.append('*')
	return vals


async def on_save():
	# when user clicks Export, it's like 'Save As' so needs a path
	# - call Tauri to post the standard File dialog
	# -

	# print('\n-' * 5, 'async save_file')
	ext = await get_format()  # get file_type / 'extensions' (no leading dot) based on user selection

	# post the 'save' dialog to get destination folder & filename
	file = await window.__TAURI__.dialog.save({
		'filters': [{ 'name': 'File', 'extensions': ext }],
	})

	if file is not javascript.NULL:  # or just if not file?
		# print('save_file', file)
		await assemble_and_save(file)


def handle_export_editor_radio_change(event, radio):
	global export_editor_data
	try:
		export_editor_data[radio.name] = radio.value
		update_static_layout_table()
	except Exception as e:
		print('ERROR in handle_export_editor_radio_change:', e)


def close_editor(event):
	checked_value = document.querySelector('[name="format"]:checked').value
	window.localStorage.setItem('format',checked_value)

	aio.run(table_utils.close_modal_window(main_window_label))


def is_checked(value):
	if last_format == value:
		return 'checked'


# -----
async def main():
	global app_config
	app_config = await window.get_app_config()
	table_utils.app_config = app_config  # must be async so cannot be set on import

	deeply_utils.pp('export_dialog -- main')

	await table_utils.disable_main_windows(main_window_label)  # export_dialog is modal

	export_btn = document.querySelector('#export-confirm-btn')
	export_btn.bind('click', lambda _: aio.run(on_save()))
	current_window.listen('tauri://close-requested', lambda _: close_editor(None))

	global total_columns
	total_columns = len(header_data)
	deeply_utils.pp('export_dialog main -- about to create')
	create_export_editor()
	create_static_layout_content_table()
	create_save_table()


# -----

window.handle_radio_change = handle_export_editor_radio_change

document['export-cancel-btn'].bind('click', close_editor)

page_runner.register_for_js()  # hardcoded for 'main'
