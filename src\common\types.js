/**
 * @typedef {Object} TrialInfo
 * @property {string} active_date - The date when the trial was activated.
 * @property {string | null} [last_popup_date] - The date of the last popup, if any.
 */

/**
 * @typedef {Object} License
 * @property {string} license_key - the license key
 * @property {string} [instance_id] - the instance ID (of specific usage limit)
 */

/**
 * Enum representing the plan types.
 * @readonly
 * @enum {string}
 */
export const Plan = {
	Free: 'FREE',
	Trial: 'TRIAL',
	Paid: 'PAID',
};

/**
 * Represents the application configuration object.
 * @typedef {Object} AppConfig
 * @property {Plan} plan - The plan type, can be FREE, TRIAL, or PAID.
 * @property {License} license - The license
 * @property {string} status - The status of the promise, can be "fulfilled".
 * @property {TrialInfo} trial_info - last UTC date when the popup showed.
 */
