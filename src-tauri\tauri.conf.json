{"$schema": "gen/schemas/desktop-schema.json", "app": {"withGlobalTauri": true, "security": {"csp": {"default-src": "'self' customprotocol: asset:", "connect-src": "ipc: https://ipc.localhost", "img-src": "'self' asset: https://asset.localhost blob: data:", "style-src": "'unsafe-inline' 'self' asset: https://asset.localhost"}, "assetProtocol": {"enable": true, "scope": ["**"]}}, "windows": []}, "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:5173", "frontendDist": "../dist"}, "plugins": {"shell": {"open": true}}, "bundle": {"active": true, "icon": ["icons/icon.png", "icons/icon.icns", "icons/icon.ico"], "targets": ["nsis", "deb", "dmg"], "copyright": "Copyright © 2023-2025 Make Deeply\nAll rights reserved."}, "identifier": "com.makedeeply.editdeeply", "productName": "Edit Deeply", "version": "0.1.0"}