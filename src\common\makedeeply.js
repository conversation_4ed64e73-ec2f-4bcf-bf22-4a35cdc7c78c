/**
 * makedeeply.js
 */

import { invoke } from './tauri.js';

window.addEventListener('DOMContentLoaded', async () => {
	const app_config = await invoke('get_app_config');
	// console.log('app_config', app_config);
	document.body.classList.add(app_config.plan);
	console.log('makedeeply.js - app_config.plan', app_config.plan);

	// TBD: add platform_class from app_config which returns macOS, windowsOS, linuxOS
	const os_name = await invoke('get_os');  // returns macos, windows, linux
	document.body.classList.add('os_name');
});

/* ============================================================================== */
/* trial period days remaining */

function parseSqliteDate(dateString) {
	const parts = dateString.split(/[\s:-]/);
	const year = parseInt(parts[0], 10);
	const month = parseInt(parts[1], 10) - 1; // Months are zero-indexed in JavaScript
	const day = parseInt(parts[2], 10);
	const hour = parseInt(parts[3], 10);
	const minute = parseInt(parts[4], 10);

	const utcDate = new Date(Date.UTC(year, month, day, hour, minute));
	return utcDate;
}

async function setDaysRemaining() {
	// Get feature flags
	/**
	 * @type {import("/common/types.js").app_config}
	 */
	const app_config = await window.__TAURI__.core.invoke('get_app_config');
	console.log('makedeeply.js - trial_info => ', app_config.trial_info);
	const currentDate = new Date();
	const active_date = parseSqliteDate(app_config.trial_info.active_date);
	const differenceInMs = currentDate.getTime() - active_date.getTime();
	const daysPassed = Math.floor(differenceInMs / (1000 * 60 * 60 * 24));
	const daysRemainingDiv = document.querySelector('.days-remaining');
	daysRemainingDiv.textContent = `${31 - daysPassed}`;
}

/* ============================================================================== */
export { setDaysRemaining };
