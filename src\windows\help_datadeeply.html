<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />

		<title>Help - Data Deeply</title>

		<script type="module" src="/common/makedeeply.js" defer></script>
		<script type="module" src="/windows/help.js" defer></script>

		<link rel="stylesheet" href="/common/makedeeply.css" />
	</head>

	<body id="help-page" class="dialog">
		<section class="if_paid">
			<h1 id="paid">Licensed copy</h1>
			<p>Serial number: <span class="serial">__</span></p>
			<p>Licensed for a single user on up to 5 machines or for all users on 1 shared machine. If you no longer need the licensed version on this machine then:
			</p>
			<div class="indent-tab">
				<button class="revoke secondary small">Convert to free version</button>
			</div>
		</section>

		<section class="if_free_or_trial">
			<div class="if_trial">
				<h1 id="trial">Trial version</h1>
				<p><span class="days-remaining">__</span> days remaining</p>
			</div>

			<div class="if_free">
				<h1 id="free">Your free version</h1>
			</div>

			<div class="purchase_buttons">
				<button class="buy">Buy Now</button>
				or
				<input type="text" class="license-input" size="40" maxlength="36" placeholder="enter license key" spellcheck="false" />
				<button class="activate">Activate</button>
				<div class="error"></div>
				<div class="lemon-squeezy">Payments are securely handled by Lemon Squeezy, a Stripe company.</div>
			</div>
		</section>

		<section>
			<hr><!-- ==================== -->

			<h1 id="about">About</h1>
			<p><span class="logotype">Data Deeply</span> app: a better way to interact with your CSV and tabbed data. View & edit data as a table, with powerful search (including regex), multi-level sort, flexible column order, and configurable export.</p>

			<p>Runs locally; not dependent on the cloud.</p>

			<p>Perpetual license; no subscription fee. (Some future updates may be free; others paid.)</p>

			<p>30 day trial version with all features, then buy a license or use the free data viewer at no charge.</p>
		</section>

		<section>
			<hr><!-- ==================== -->
			<h2>Free version: csv/txt data viewer with powerful search, sort, layout, and export</h2>

			<ul>
				<li>Small, lightweight app. Initially for macOS; contact us about Windows 11 and Linux.</li>
				<li>Search <span class="small_gray">(not case sensitive except where noted)</span>
					<ul>
						<li>like, not like</li>
						<li>=, !=</li>
						<li>any, not any</li>
						<li>starts, ends</li>
						<li>&le;, &lt;, &gt;, &ge;</li>
						<li>regex, not regex (optionally case sensitive)</li>
					</ul>
				</li>
				<li>Multi-level sort
					<ul>
						<li>quick: click the arrow at the top of any column</li>
						<li>powerful: use dialog to change order</li>
					</ul>
				</li>
				<li>Column layout
					<ul>
						<li>change order</li>
						<li>hide/show any column</li>
						<li>optional row number</li>
					</ul>
				</li>
				<li>Export
					<ul>
						<li>all rows or found set</li>
						<li>all columns or current set</li>
						<li>original header or friendly labels</li>
						<li>original order or current sort</li>
						<li>format: csv or txt/tsv (tabbed)</li>
						<li>line ending: LF (macOS, Linux) or CRLF (Windows)</li>
					</ul>
				</li>
				<li>File History <span class="small_gray">(in the free version: history within a session)</span>
					<ul>
						<li>see what you opened, when</li>
						<li>see open files, with 1 click to bring the window to the front or open a new window</li>
					</ul>
				</li>
				<li>Window management
					<ul>
						<li>files and dialogs open in separate windows; you're not locked into a fixed layout with multiple datasets hidden behind tabs</li>
						<li>access any window via the Windows menu</li>
						<li>can open the same file in multiple windows, each with its own search, sort and column layout</li>
					</ul>
				</li>
				<li>Reading data
					<ul>
						<li>reads csv or txt/tsv (tabbed)</li>
						<li>handles either commonn line ending: LF or CRLF</li>
						<li>automatically skips comments (lines beginning with '#')</li>
						<li>automatically skips leading & trailing whitespace</li>
					</ul>
				</li>
			</ul>

		</section>

		<section>
			<hr><!-- ==================== -->
			<h2>Paid version: edit csv/txt data, add or delete rows and columns, save changes</h2>

			<ul>
				<li>(All features listed above.)</li>
				<li>Edit data in the main table or a detail window</li>
				<li>Add or delete rows</li>
				<li>Add or delete columns</li>
				<li>Save file, with automatic backup</li>
				<li>File History persists across sessions</li>
			</ul>
		</section>

		<h1 id="help">Help</h1>
		<ul>
			<li>
				<b>Limitations:</b>
				<ul>
					<li>The current version is designed for modest-sized data sets. Loading data may take 1-2 seconds per thousand rows.</li>
					<li>The app manages automatic backups by filename so is not designed for files with the same name but in different folders.</li>
				</ul>
			</li>
		</ul>

		<h1 id="contact">Contact Us</h1>
		<ul>
			<li>
				We welcome your comments, questions and feature requests:
				<a href="mailto:<EMAIL>"><EMAIL></a>
			</li>
			<li>Home page: <a href="https://makedeeply.com">MakeDeeply.com</a></li>
			<li>X: <a href="https://x.com/MakeDeeply">@MakeDeeply</a></li>
			<li>Location: Burlington, MA, USA</li>
		</ul>

		<footer>
			<p class="small_gray">Data Deeply™, Make Deeply™, and Pumafab™ are trademarks of Pumafab LLC.</p>
		</footer>
	</body>
</html>
