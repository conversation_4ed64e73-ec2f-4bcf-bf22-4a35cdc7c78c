# Make Deeply

Code name: MakeDeeply

A generic Make Deeply app -- expanded into Data Deeply but that will become its own repo.

---

## Legal

-   Copyright 2023-2025 by Make Deeply; All Rights Reserved.
-   Confidential and Proprietary.
-   All code (other than narrow exceptions clearly stated in writing by <PERSON> Deeply) developed by contractors is under 'work for hire' terms with all rights belonging to Make Deeply.

---

## Dev, Debug, Build

### Prerequisites

-   [Rust](https://www.rust-lang.org/tools/install)
-   [Node](https://nodejs.org/en/download)

-   update as needed: `cargo install tauri-cli --force`

### Setup

Install dev dependencies (from root directory):

```console
npm install -D
```

For logs on Mac or Git Bash, one of::

-   `export RUST_LOG=trace`
-   `export RUST_LOG=debug`
-   `unset RUST_LOG`

For logs in cmd.exe, one of::

-   `set RUST_LOG=trace`
-   `set RUST_LOG=debug`

### Running in dev mode

-   `npx tauri dev` with auto-reload
-   `npx tauri dev --no-watch` to avoid auto-reload of Rust files (html, css & js will still reload).

Some behaviors will be different in dev mode compared to the built version.

-   scraping windows will be visible in dev mode but not in the production build
-   MacOS 'About' dialog shows generic folder icon instead of actual product icon

To clear the previous build and start fresh, cd to `src-tauri` then run `cargo clean`.

### Building

To build for the current platform, execute `npx tauri build`. On Windows, this will build both NSIS and MSI installers. Both function identically and are located under `src-tauri/target/release/bundle/<msi or nsis>`.

### Client side db queries

It's possible to do db queries from client side
possible commands: `load / close / select / execute`, arguments can bind safely with `$1` and `?` and then passed through array
More info at [v2/plugins/sql](https://github.com/tauri-apps/plugins-workspace/tree/v2/plugins/sql)

```js
const dbName = 'sqlite:makedeeply.sqlite'; // automatically taken from our app config path
window.__TAURI__.core.invoke('plugin:sql|load', { db: dbName });
window.__TAURI__.core.invoke('plugin:sql|select', { db: dbName, query: 'SELECT * FROM posts', values: [] });
```

### Tauri patches

Currenly we patch the library `wry` inside Tauri from custom Fork.
And we use specific version of `tauri` and `tauri-build` so it will work.
In the future we when Tauri fix global macos shortcuts on multiwebview we will use tauri stable,
and the integration will be simpler since currenly we use tauri v2.
For developing with we should use tauri-cli from `npm` by

```console
npm i -g @tauri-apps/cli@2.0.0-beta.1
```

And use it with

```console
npx tauri dev
```

### Samples for tests

[northwind.db](https://github.com/jpwhite3/northwind-SQLite3/blob/main/dist/northwind.db)

[Chinook_Sqlite.sqlite](https://github.com/lerocha/chinook-database/raw/master/ChinookDatabase/DataSources/Chinook_Sqlite.sqlite)

### Databse migration

To create new migration, execute from `src-tauri`

MacOS

```console
sqlx migrate add <name>
```

More info in [crates/sqlx-cli](https://crates.io/crates/sqlx-cli)

### Folders

MacOS

-   `/Users/<USER>/Library/Application Support/com.makedeeply.makedeeply` for the settings files including hidden .window-state
-   `/Users/<USER>/Library/Preferences/Make Deeply.plist` for ???

Windows

-   `C:\{username}\Appdata\Roaming\com.makedeeply.makedeeply\` for the settings files
-   `C:\{username}\Appdata\Roaming\com.makedeeply.makedeeply\.window-state` for window size & location; written at QUIT (?)
-   `C:\{username}\Appdata\Local\com.makedeeply.makedeeply` currently has EBWebView folder

Settings files

-   `makedeeply.sqlite` - DB

optional

-   `api_url.txt`
-   `token.txt`

created after changing column width/order: [TBD: include defaults!]

-   `actions_layout.json`
-   `posts_layout.json`
-   `users_layout.json`

# App Windows

-   Settings
-   Help
-   History
-   Database view

    -   DB Tables
    -   Table Records
    -   Record Details
    -   New Record

-   File view
    -   HTML - rendered
    -   Textual - textarea

### macOS menu

macOS menu configured in `src-tauri/src/window/menu.rs`.

It uses tauri v2 API for menu [docs](https://docs.rs/tauri/latest/tauri/struct.Menu.html)

The app Menu is set of subMenus, where each submenu contains menu items.

Every menu item contains uniquie ID generated by Tauri API.

The menu emit events with ID of the clicked item.

For adding new submenu, you should add it to `Menu::with_items(...)`

For adding menu items to submenu, you should create it with `MenuItem::new(...)`, store the ID, and handle it inside `on_menu_event` by matching against `event.id()`.

### Trial version

Invisible files **above** our folders so they survive uninstall + reinstall

MacOS: invisible by virtue of starting with '.' e.g. '.123a9b8c7'.

To list: `ls -a | grep '^\.\w'`

-   `/Users/<USER>/Library/Application Support/`
-   `/Users/<USER>/Library/Preferences/`

Windows:

To list in cmd.exe: `dir /a:h`

To show/hide invisible files: click '...' menu (after the View menu), then View tab in the dialog, then 'Show hidden files, folders, and drives' (or Don't show)

To see if a file is hidden: click on it, click Properties (e.g. bottom right of window), the look at checkbox (bottom right of dialog).

-   Registry key (edit with regedit): `COMPUTER\HKEY_CURRENT_USER\Software\Make Deeply\` then the encoded key name
-   `C:\{username}\Appdata\Local\`
-   `C:\{username}\Appdata\Roaming\`

---

## Test

### Clear some settings on Mac

-   `rm "$HOME/Library/Application Support/com.makedeeply.makedeeply/.window-state"`
-   `rm "$HOME/Library/Preferences/Make Deeply.plist"`

### Control behaviors with ENV variables

-   `SHOW_TRIAL_POPUP=1` to open trial popup on launch (`0` for not)
-   `PLAN=FREE` Forced plan (enable or disable features): `FREE`, `TRIAL`, `PAID`
-   set to 0 for testing: `TRIAL_INFO_ENCRYPTION=1` to enable encryption for trial info invisible files (`0` for disabled)

Windows cmd.exe:

```console
set name=value
cargo run
```

MacOS & Linux:

```
export name=value
cargo run
```

### e2e tests

Additional info:

-   You shouldn't run another makedeeply instance when testing
-   The window should remain in focus until finish test so the browser keep load things for testing
-   Edge webview version should match the version of webdriver version (eg. canary, stable etc...). In Edge, use edge://settings/help to check that the version is 120.0.2210.144 or greater. When running MakeDeeply, check the version in the first log message.

End to end tests are done using [selenium](https://www.npmjs.com/package/selenium-webdriver) based on [testing/webdriver/example/selenium/](https://tauri.app/v1/guides/testing/webdriver/example/selenium/)
**Currenly should work on windows only because of missing tauri feature, I added in windows and sent them PR**

1. Install tauri driver `cargo install tauri-driver`
2. Download [msedge-driver](https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/?form=MA13LH&ch=1#downloads) into `src-tauri/`.
   it should be same release channel of your webview eg (`canargy`, `stable` etc)
3. Install npm via their installer, then open a new Command Prompt (cmd.exe) so that it's in the path
4. Open tests and run `npm i`
5. Execute `npm test`

If you didn't changed the code and you want to skip the build when testing, you can set `SKIP_BUILD` to `1` eg.

```console
set SKIP_BUILD=1
npm run test
```

### Create zip archive from repository (git-ignored)

```console
git archive --format=zip --output=make1.zip HEAD
```

### Formatting via VS Code plug-ins

Using prettier, applied also for Rust using

[esbenp.prettier-vscode](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

[jinxdash.prettier-rust](https://marketplace.visualstudio.com/items?itemName=jinxdash.prettier-rust)

### Payment

Payment done with Lemon squeezy, with their license feature and overlay checkout.
To test payment, you need to enable test mode in [lemon squeezey](https://app.lemonsqueezy.com/dashboard) (bottom left)
Then pay with test card `4242 4242 4242 4242` (visa)
After successful payment a success button of lemon squeezy will be shown, once he clicked, a success page will be shown (success.html) which activate it immediately and restart.
License stored then in `license.json` file (in app config directory) along with instance_id (used for lm to keep track of usages)

# Docs

### Custom extensions

At startup, will read any `css` and `js` files added to the settings folder. (The 'manifest.json' of Chrome & Firefox extensions is neither required nor supported.)

1. In the `Settings` dialog under `Customize` click `Open Customization Folder`
2. Create a new `.js` file with contents such as:

```js
console.log('My first script');
```

3. Create a new `.css` file with contents such as:

```css
* {
	outline: 1px solid purple;
}
```

4. Inside `settings` dialog under `Customize` click the `Reload App` button.

That's it!

To remove, delete or move one or both files then click `Reload App` again.

### Python API

There's python API for the app, inside `python_api`
It provides a way for automate actions on the app from HTTP api.
Examples inside `python_api` folder.

### regex to fix some double quotes, noting that BBEdit uses \r for general LF/CRLF

caveat: breaks html snippets in create_export_settings() ... which should be refactored anyway

```regex
^([^'"\r]*)"([^'"\r]+)"([^'"\r]*)$
\1'\2'\3
```

quoted pairs:

```regex
^([^'"\r]*)"([^'"\r]+)"([^'"\r]*)"([^'"\r]+)"([^'"\r]*)$
\1'\2'\3'\4'\5
```

### row detail

Mantains does not take data 'sorting' or 'filtering' into consideration.
Simply displays all the data in the table, starting with the 'clicked on' data row.
Other rows can be navigated to using the prev '<' and next '>' buttons respectively.
prev '<' and next '>' don't wrap around.
If the table is sorted and/or filtered while row detail is open � row detail won't be affected by any of or a combination of these changes.

## Update to tauri v2:

The Tauri v2 CLI includes a migrate command that automates most of the process and helps you finish the migration:

```
yarn upgrade @tauri-apps/cli@latest
yarn tauri migrate
```

> I get some error with incompatible functions and plugins with
> the current version (v2.0.6)

### migrate functions:

###### get current window:

From (v1):

```js
tauri.window.getCurrent();
```

To (v2.0.6):

```js
tauri.window.getCurrentWindow();
```

##### Dialog:

Open a file:

```py
file = await window.__TAURI__.dialog.open({
		"multiple": False,
		"directory": False,
		"name": "File",
	})
```

to get the file path:

-   in version 1:

```py
file.path   #return the file path
```

-   in version 2.0.6:

```py
file   #return the file path
```

### migrate plugins:

removing the old version of the tauri-plugin-sql and then install the new one using:

```shell
npm run tauri add sql
```

# csv/txt/json/html/json/tsv file open

-   Reads the file contents and extracts tabular data into a list.
-   Creates a sqlite file with the list data 'columns'. If successful, the sqlite table is populated with the data from the list.
-   If successful, the data is read from the sqlite table and a tabulator table is created and populated with the data.
-   If a column is deleted, a 'csv' file with two columns('column name', 'id(pk)') is created. Data for the two respective columns is saved in the 'csv' file. The sqlite table is then edited to remove/add columns. Lastly, data is loaded from the sqlite table and the tabulator table is recreated.

-   row detail now uses DB queries for previous/next. We no longer need to pass the table data to the window.
-   row detail also saves all the edits made to individual rows to the DB using the row's primary key.
-   newly added rows are also saved to the DB.
-   All search/sort/pagination are still done locally via tabulator.
-   Potential advantages of creating a sqlite file could be:
    > not having to read the file contents and extracting data. That's skipping 2 potentially expensive(time and resource hungry) steps.
    > recovering edited data or newly added data if/when the window/app reloads.
    > leaving the origional file unchanged for future use.
    > choosing to recreate the sqlite file with the origional file data or continuing with the data in the current sqlite file.

# Troubleshooting

-   In the unlikely case where you get a 404 not found Error. Follow these steps:
    > Delete the project folder "make1"

## On Windows

> Navigate to "~path to user"\AppData\Roaming and delete the folder "com.makedeeply.makedeeply"
> Navigate to "~path to user"\AppData\Local and delete the folder "com.makedeeply.makedeeply"
> Follow the setup steps from above for a clean install

## On Mac
