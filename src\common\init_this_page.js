/*
 * init_this_page.js - creates window.get_app_config() for Python; adds PLAN & OS to body; calls Python main()
 */

(function() {
	let appConfigPromise = null;

	// Make get_app_config available immediately, not just after DOMContentLoaded
	window.get_app_config = () =>
		appConfigPromise || (appConfigPromise = window.__TAURI__.core.invoke('get_app_config'));

	document.addEventListener('DOMContentLoaded', async () => {
		try {
			const config = await window.get_app_config();
			window.__app_config__ = config;

			// add PLAN and OS
			document.body.classList.add(config.plan, config.platform_class);
			window.dispatchEvent(new CustomEvent('config-ready', { detail: config }));

		} catch (error) {
			console.error('Failed to init config:', error);
		}
	});

	// Listen for Python ready signal sent by page_runner.py, then call it
	document.addEventListener('python-ready', () => {
		console.log('Python is ready, calling python_runner which page_runner hardcodes to main()');
		if (window.python_runner) {
			window.python_runner();
		}
	});
})();
