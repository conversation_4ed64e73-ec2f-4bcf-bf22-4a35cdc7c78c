import Database from 'better-sqlite3';
import { spawn, spawnSync } from 'child_process';
import fs from 'fs';
import { Builder, Capabilities } from 'selenium-webdriver';
import * as config from './config.js';
import { findWindow, sleep } from './utils.js';
/** GLOBALS */
/**
 * @type {Database.Database}
 */
export let db;
/**
 * @type {import("selenium-webdriver").ThenableWebDriver}
 */
export let driver;

// keep track of the tauri-driver process we start
export let tauriDriver;

export async function prepareTauri() {
	if (!config.skipBuild || !fs.existsSync(config.application)) {
		spawnSync('cargo', config.cargoArgs, {
			cwd: config.srcTauriPath,
			stdio: [null, process.stdout, process.stderr],
		});
	}
}

export async function prepareDriver() {
	// start tauri-driver
	tauriDriver = spawn(config.tauriDriverPath, config.tauriDriverArgs, {
		stdio: [null, process.stdout, process.stderr],
	});

	const capabilities = new Capabilities();
	capabilities.set('tauri:options', {
		application: config.application,
		args: config.applicationArgs,
		webviewOptions: config.webViewOptions,
	});

	capabilities.setBrowserName('wry');

	// start the webdriver client
	driver = new Builder().withCapabilities(capabilities).usingServer(config.webDriverUrl).build();

	// wait for ready state and jungle between windows
	await findWindow(driver, config.mainWindowLabel);

	// connect database and check state
	db = new Database(config.tmpDbPath);
}

/**
 *
 * @param {import("selenium-webdriver").ThenableWebDriver} driver
 */
export async function cleanup() {
	// kill the tauri-driver process
	tauriDriver.kill();

	await sleep(2000); // wait for cleanup
	// some bug requires to cleanup the directory
	fs.rmSync(config.testingPath, { recursive: true, force: true });
}

// handle ctrl + c
process.on('SIGINT', async () => {
	await cleanup(driver, tauriDriver);
});
