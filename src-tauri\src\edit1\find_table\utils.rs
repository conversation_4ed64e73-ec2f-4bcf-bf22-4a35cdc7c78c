use std::{
    collections::HashSet,
    fs::File,
    io::{<PERSON>ufR<PERSON>, BufReader},
    path::{Path, PathBuf},
};

use ignore::gitignore::GitignoreBuilder;

use super::dto::Filters;

pub fn should_exclude_path(
    path: &Path,
    path_str: &str,
    filters: &Filters,
    rules: &[String],
    excluded_dirs: &HashSet<PathBuf>,
) -> bool {
    if !is_whitelisted(path_str, &rules) && excluded_dirs.iter().any(|dir| path.starts_with(dir)) {
        return true;
    }

    if filters.omit_invisible_files && path.is_file() && is_hidden(path) {
        return true;
    }

    if filters.omit_invisible_folders && path.is_dir() && is_hidden(path) {
        return true;
    }

    if !rules.is_empty() && matches_any_rule(path_str, &rules) {
        return true;
    }

    false
}

pub fn is_hidden(path: &Path) -> bool {
    path.file_name()
        .and_then(|name| name.to_str())
        .map(|name| name.starts_with('.'))
        .unwrap_or(false)
}

pub fn matches_any_rule(path: &str, rules: &[String]) -> bool {
    let mut builder = GitignoreBuilder::new("");

    // Add gitignore rules from the array
    for rule in rules {
        builder
            .add_line(None, rule)
            .expect("Invalid gitignore rule");
    }

    // Build the Gitignore matcher
    let gitignore = builder.build().expect("Failed to build Gitignore");

    // Check if the path is excluded
    let result = gitignore.matched(Path::new(path), false);
    // Return true if the path is excluded, false otherwise
    result.is_ignore()
}

pub fn is_whitelisted(path: &str, rules: &[String]) -> bool {
    let mut builder = GitignoreBuilder::new("");

    // Add gitignore rules from the array
    for rule in rules {
        builder
            .add_line(None, rule)
            .expect("Invalid gitignore rule");
    }

    // Build the Gitignore matcher
    let gitignore = builder.build().expect("Failed to build Gitignore");

    // Check if the path is excluded
    let result = gitignore.matched(Path::new(path), false);
    // Return true if the path is excluded, false otherwise
    result.is_whitelist()
}

pub fn load_gitignore(root: &Path) -> Vec<String> {
    let mut rules = Vec::new();
    let mut current_dir = root.to_path_buf();

    while current_dir.exists() {
        let gitignore_path = current_dir.join(".gitignore");
        if gitignore_path.exists() {
            if let Ok(file) = File::open(&gitignore_path) {
                let reader = BufReader::new(file);
                for line in reader.lines().filter_map(Result::ok) {
                    let trimmed = line.trim();
                    if !trimmed.is_empty() && !trimmed.starts_with('#') {
                        rules.push(trimmed.to_string());
                    }
                }
            }
        }
        if !current_dir.pop() {
            break;
        }
    }

    rules
}
