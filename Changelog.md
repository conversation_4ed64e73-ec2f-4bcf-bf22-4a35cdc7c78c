## 2024-07-28 by J.

-   Send events from column editor window to update main table
-   Populate search editor window with current filters
-   Notes for <PERSON>:
    Avoid using generic names like "tabulator" for files, as it conflicts with the name of the library we use and doesn't convey any meaningful information.
    Try to organize and group logic into separate files instead of relying on comments to demarcate sections.
    Limit the use of optional arguments in functions and ensure each function performs a single, specific task.
    Limit the use of global variables. Brython doesn't handle them well.
-   Prevent crash if window doesn't have title

## 2024-07-26 by J.

-   Setup Edit Columns window table
-   Setup Edit Search window table
-   Handle cancel and close buttons in Edit Columns

## 2024-07-26 by J.

-   Migrate part of file.js to file.py
-   Create new pages with random label suffix
-   Correct Edit Columns and Edit Search window titles
-   Disable parent window while Edit Columns is open

## 2024-07-25 by J.

-   Setup searchEditor and columnEditor windows

## 2024-07-15 by J.

-   Fix warnings
-   Add basic custom type hints for Brython
-   Fix: display firnedly foreign column in table window
-   Fix: show multi friendly values in details page in select and radio
-   Fix: capture variables when edit in details in lambdas correctly
-   Fix: match friendly columns including with remove_plural_suffix
-   Fix: show blob correctly in details

## 2024-07-05 by J.

-   Fix: show {firstName} {lastName} in friendly column value in table window
-   Improve exists by file ID function
-   Correct get_friendly_fk_columns for multiple patterns

## 2024-07-04 by J.

-   Sort foreign keys in main db window

## 2024-07-03 by J.

-   Change changelog dates format to yyyy-mm-dd
-   Fix: return correct friendly column name
-   Optimize table window with lru cache
-   Show primary key value in details window title
-   Sort table window columns
-   Fix foreign keys format in main db window
-   Fix: friendly FK column immediately before the actual FK

## 2024-07-02 by J.

-   Register / Unregister global shortcut on blur/focus in table window
-   Setup native HTML table in details window
-   Add edit for native table in details window
-   Friendly FK editing in details:
    -   input with friendly value on blur
    -   radio buttons
    -   select dropdown
-                                                                 Display friendly column in db window and tables window
-   Sort primary keys alphabetically
-   Sort foreign keys alphabetically
-   Fix: open row ID as string, and escape with row id when query.
-   Remove tabulator warnings
-   Fix: create new record

## 2024-07-01 by J.

-   Add cargo workspace
-   Update cargo dependencies
-   Initial setup for friendly foreign details edit
-   Handle non existing friendly value

## 2024-06-28 by J.

-   Show multiple primary keys columns in db window
-   Show foreign keys in db window across lines with full information on reference

## 2024-06-28 by J.

-   Setup friendly foreign keys in db window
-   Setup primary key and foreign keys in db window

## 2024-06-26 by J.

-   Create dummy database for test foreign keys [main.py](https://gist.github.com/thewh1teagle/ef676daadf30c8c9675ca66ae0993292) [company.db](https://github.com/ImageDeeply/make1/releases/download/v0.0.1/company.db)

## 2024-06-19 by J.

-   Remove unused code
-   Fix: Update window menu when window closed
-   Fix: update window restore icons in history when second instance closed

## 2024-06-17 by J.

-   Save details on hit return key
-   Fix details issues from renamed Label rows to Name

## 2024-06-14 by J.

-   Fix: reset changes after clicking next / prev
-   Update table winodw on details change
-   Fix: reload rows after clicking next / prev
-   Check unsaved changes before navigating to prev / next row

## 2024-06-13 by J.

-   Show blob cell as (BLOB) in table rows window
-   Set save button to gray by default and update to deep blue if there's edit changes

## 2024-06-11 by J.

-   Make details rows editable and access edited data on save
-   Prepare row data types when clicking save
-   Make primary key and blob rows non editable in details
-   Make `created`, `updated`, types `BLOB`, `TIMESTAMP` readonly in details
-   Setup unsaved changes confirm dialog on close details window
-   Show error if clicking save and passed incorrect value type when editing details
-   Count row editing diff before close window
-   Update row when editing existing row, from save button or when exit window and clicking save
-   Initial setup for custom tabulator editor
-   Hide row in details if suffix is `_html`
-   Update details values in single sql update
-   Save one row per edit field in edits table inside app db
-   Implement insert new row window
-   Open file from history by ID
-   Change 300px columns width to be maxWidth by default
-   Fix windows build

## 2024-06-10 by J.

-   Fix command + I duplicate shortcut event per comment in opened issue
-   Open details page for new row and pass is_new_row as true

## 2024-06-09 by J.

-   Implement create window with offset
-   Disable window position workaround to keep window state working
-   Set default width to columns
-   Prepare link-out icon for details
-   Setup row details window
-   Add help button in details page
-   Add circular rows navigation buttons to details page
-   Setup save button
-   Setup `Command` + `I` shortcut with global shortcut plugin
-   Create tauri issue about global shortcut fire duplicate event

## 2024-06-08 by J.

-   Open tauri issue about window position issue

## 2024-06-07 by J.

-   Document available windows in readme

## 2024-06-01 by J.

-   BBEdit like window numbering (keep increment until reopen app)
-   Preserve numbering windows hashmap order with indexmap
-   Restore the lowest window instance

## 2024-05-31 by J.

-   Connect sqlite for sql plugin from Rust
-   Add numbering for sqlite / text files / HTML
-   Update numbering in both DOM and title
-   Hide title in new window / restore columns
-   Open specific table window with table rows
-   Restore/New window from sqlite tables window
-   Add loading indicator to table view
-   Update macOS window menu when window closed / open / new instance
-   Fix window icons order

## 2024-05-29 by J.

-   Create PR in tauri sql plugin [plugins-workspace/pull/1380](https://github.com/tauri-apps/plugins-workspace/pull/1380) to allow open any sqlite file
-   Create PR in tauri sql plugin [plugins-workspace/pull/1381](https://github.com/tauri-apps/plugins-workspace/pull/1381) to allow accessing DB instances from Rust too.
-   Open multiple `sqlite` files with numbered window and setup tables query
-   Rename `record_count` to `count` (require delete sqlite file)
-   Setup tabulator table in sqlite file view window
-   Setup single table view window
-   Setup restore / new window icons in history table
-   Set layout mode as `fitData` for tabulator tables

## 2024-05-28 by J.

-   Add `numbering.rs` for dynamic numbered db windows instances (titles)
-   Add `sql.py` for sql plugin with Brython

## 2024-05-27 by J.

-   Add `can_open_sqlite` feature flag
-   Add `app_config` Brython type hints
-   Add `index.py` and show error message if file is `sqlite` and `can_open_sqlite` `FF` is OFF.

## 2024-05-22 by J.

-   Unminimize windows on focus from Windows menu / History
-   Set default columns width, and rename record_count -> count, extension -> ext
-   Add option to drag and move columns
-   Count json file by array length or 1 if object
-   Treat txt file as tabbed if first character is tab
-   fix: treat txt as tsv if first LINE contains tab not only first char

## 2024-05-21 by J.

-   Setup Brython scripts
-   Move tabulator to lib folder
-   Convert `history.js` to `history.py` with `Brython`
-   Open / Focus file from history with icons
-   Update Open / Focus file icons in history table in realtime
-   Update local rendered files (Such as HTML) status by custom init script
-   Add custom read text file and show error if not exists

## 2024-05-17 by J.

-   Fix source map erros from tabulator

## 2024-05-16 by J.

-   Add name, ext, and nice path to file history
-   Allow any file extension

## 2024-04-16 by J.

-   Merge migrations
-   Update schema to fildid
-   Setup refind crate
-   Add edit row if opened path different from most recent
-   Omit home directory in database window using custom tabulator formatter
-   Select queries in database as local time dates

## 2024-05-15 by J.

-   Create `refined` crate and publish in `Github`, `CratesIO`

## 2024-05-10 by J.

-   Setup tabulator with jsdocs
-   Add tauri sql plugin definitions
-   Improve common tauri exports
-   Update files rows in realtime when opening new file
-   Counted window title for duplicate titles

## 2024-05-09 by J.

-   File dialog for `csv, txt, md, json, html, css, js`
-   File window, json prettified for JSON / HTML rendered / visible as text

## 2024-05-03 by J.

-   Fix tests for windows
-   Improve test config by autoparsing Rust

## 2024-05-02 by J.

-   Init
    -   Payment
    -   Trial
    -   Global app config
    -   HTTP API
    -   Editor settings
    -   Omit unused libraries
    -   Omit unused files
    -   SQLX tauri plugin
    -   SQLX Rust setup
    -   Migrations
