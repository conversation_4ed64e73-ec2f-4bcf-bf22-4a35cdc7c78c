import { TauriEvent, emit, listen } from '@tauri-apps/api/event';
import { getCurrentWebviewWindow, WebviewWindow } from '@tauri-apps/api/webviewWindow';
import { LogicalSize, PhysicalSize, Window, currentMonitor } from '@tauri-apps/api/window';
import {
	afterCheck,
	afterCount,
	beforeCheck,
	beforeCount,
	caseCheck,
	entireWordCheck,
	findIn,
	findLabel,
	ignoreTextArea,
	matchTextArea,
	openDocsCheck,
	regexCheck,
	replaceIn,
	replaceInBox,
	replaceSetBox,
	reviewBtn,
	selFilesCheck,
	selFoldersCheck,
	selectedCheck,
	selectedCount,
	tableEl,
	tabulateBtn,
} from './js/elements';
import {
	Tabulator,
	SortModule,
	FormatModule,
	EditModule,
	FilterModule,
	ResizeColumnsModule,
	ResizeTableModule,
	InteractionModule,
	GroupRowsModule,
} from 'tabulator-tables';
import './js/search-resizing';
import './js/files-folders-js/open-files-folders';
import { <PERSON><PERSON>, AlertType } from '../../js/alert';
import { minMaxFilterEditor, minMaxFilterFunction, removeRedundantData } from './js/filters';
import { rightTabIcon, replaceBtnFormat, fileNameGroupHeader } from './js/formatters';
import filesFoldersStorage from './js/files-folders-js/files-folders-storage';
import { invoke } from '@tauri-apps/api/core';
import { getReplacement } from '../../js/utils/search';
import { getSearchState, setSearchState } from './js/storage';
import { checkSelectedFilesStatus, filterValidPaths, getFilters, getReviewData } from './js/gitignore';
import { handleDirList, handlefilesList, updateFilesFolderStatus } from './js/files-folders-js/open-files-folders';
const appWindow = getCurrentWebviewWindow();

//set focus to find textarea when window focused
appWindow.listen(TauriEvent.WINDOW_FOCUS, (e) => {
	if (e.windowLabel == appWindow.label) findIn.focus();
});

//listen to theme change
listen('theme:change', (e) => {
	if (e.payload.dark) document.body.classList.add('dark-theme');
	else document.body.classList.remove('dark-theme');
});

let openFilesPath = [];

let data = [
	{
		title: '',
		file_name: '',
		file_folder: '',
		parent_folder: '',
		window: '',
		opendocs: false,
		finder: {
			find: '',
			replace: '',
			case_sensitive: false,
			entire_word: false,
			grep: false,
			selected_only: false,
		},
		selection: { from: 0, to: 0 },
		rows: [],
	},
];
let counts = { before: [], selected: [], after: [] };

let tableData = [];
let parentWind;

let unlisten = () => {};

listen('tabulater:set-file-path', (d) => {
	openFilesPath.push(d.payload);
});

export async function getOpenDocsPath() {
	if (openDocsCheck.checked) {
		openFilesPath = [];
		await emit('all:get-file-path');
	}
}

window.addEventListener('DOMContentLoaded', async () => {
	findIn.focus();
	const searchState = await getSearchState();
	setFinderValues(searchState);
	await emit('tabulater:created', {});
});

function toggleTableDisplay() {
	if (table.getDataCount() > 0) {
		tableEl.style.display = 'block';
		replaceInBox.style.display = 'flex';
		replaceSetBox.style.display = 'block';
		tableEl.scrollIntoView({
			behavior: 'smooth',
		});
	} else {
		tableEl.style.display = 'none';
		replaceInBox.style.display = 'none';
		replaceSetBox.style.display = 'none';
	}
}

function setParentWind() {
	const tabLabel = appWindow.label;
	const index = tabLabel.indexOf('tabulated');
	const parentLabel = tabLabel.substring(0, index);
	parentWind = new Window(parentLabel);
}

function initFinderValues() {
	findLabel.innerText = 'Find';
	findIn.value = data[0].finder.find;
	replaceIn.value = data[0].finder.replace;
	caseCheck.checked = data[0].finder.case_sensitive;
	entireWordCheck.checked = data[0].finder.entire_word;
	regexCheck.checked = data[0].finder.grep;
}

function countMatches() {
	counts = { before: [], selected: [], after: [] };
	if (tableData.length > 0) {
		tableData.forEach((row) => {
			let { from, to } = row.sel;
			if (row.to <= from) counts.before.push({ context: 'Before', ...row });
			else if (row.from >= to) counts.after.push({ context: 'After', ...row });
			else if (row.from >= from && row.to <= to) counts.selected.push({ context: 'Selected', ...row });
		});
		tableData = counts.before.concat(counts.selected.concat(counts.after));
		if (data[0].finder.selected_only) {
			beforeCheck.checked = false;
			selectedCheck.checked = true;
			afterCheck.checked = false;
		} else {
			beforeCheck.checked = counts.before.length > 0;
			selectedCheck.checked = counts.selected.length > 0;
			afterCheck.checked = counts.after.length > 0;
		}
	}
	beforeCount.innerText = counts.before.length.toString();
	afterCount.innerText = counts.after.length.toString();
	selectedCount.innerText = counts.selected.length.toString();
}

async function updateWindowHeight() {
	const main = document.querySelector('main');
	if (!main) {
		return;
	}

	let contentHeight = main.offsetHeight;
	let monitorHeight = (await currentMonitor()).size.height;
	let currentSize = await appWindow.outerSize();
	if (contentHeight < monitorHeight) {
		await appWindow.setSize(new PhysicalSize(currentSize.width, contentHeight));
	} else if (monitorHeight > currentSize.height) {
		await appWindow.setSize(new LogicalSize(currentSize.width, monitorHeight));
	}
}

listen('tabulater:set-data', async (d) => {
	data = [d.payload];
	setParentWind();
	await appWindow.setTitle(data[0].title);
	if (!d.payload.opendocs) {
		initFinderValues();
		await tabulate();
	} else {
		openDocsCheck.checked = true;
	}
	await updateWindowHeight();
	toggleTableDisplay();
});

listen('tabulater:add-data', async (d) => {
	let x = { ...d.payload, label: d.windowLabel };
	console.log('X => ', x);
	if (!data.includes(x)) data.push(x);
});

listen('tabulater:refresh-data', async (d) => {
	let x = { ...d.payload, label: d.windowLabel };
	if ((openDocsCheck.checked || data[0].opendocs) && !data.includes(x)) data.push(x);
	else data = [x];
	// await appWindow.setTitle(data[0].title);
	await tabulate();
});

function wrapMatchWithSpan(text, from, to, replace = null) {
	const before = text.slice(0, from);
	const word = text.slice(from, to);
	const after = text.slice(to);

	return `${before}<span class="match${replace ? '-replace' : ''}">${replace ?? word}</span>${after}`;
}

async function tabulate() {
	table.clearData();
	tableData = [];
	data = removeRedundantData(data);
	console.log('tabulate(): data =', data);
	data.forEach((result, rsInd) => {
		console.log('tabulate(): result =', result);
		const rows = result.rows;
		const replace = result.finder.replace;
		if (replace) {
			table.showColumn('replaceLine');
			rows.forEach((row) => {
				let replacement = replace;
				if (result.finder.grep) {
					const findRegex = new RegExp(result.finder.find, result.finder.case_sensitive ? 'i' : '').toString();
					const matches = row.found.match(new RegExp(result.finder.find, !result.finder.case_sensitive ? 'i' : ''));
					replacement = getReplacement(matches, result.finder.replace);
				}
				tableData.push({
					id: `${result.file_folder}-${row.m.from}-${row.m.to}`,
					line: row.number,
					findLine: wrapMatchWithSpan(row.content, row.match_from, row.match_to),
					replaceLine: wrapMatchWithSpan(row.content, row.match_from, row.match_to, replacement),
					found: row.found, //row.content.slice(row.match_from, row.match_to),
					replacement,
					from: row.m.from,
					to: row.m.to,
					sel: result.selection,
					file_name: result.file_name,
					file_folder: result.file_folder,
					window: result.window,
					parent_folder: result.parent_folder,
				});
			});
		} else {
			table.hideColumn('replaceLine');
			rows.forEach((row) => {
				tableData.push({
					id: `${result.file_folder}-${row.m.from}-${row.m.to}`,
					line: row.number,
					findLine: wrapMatchWithSpan(row.content, row.match_from, row.match_to),
					found: row.found, //row.content.slice(row.match_from, row.match_to),
					from: row.m.from,
					to: row.m.to,
					sel: result.selection,
					file_name: result.file_name,
					file_folder: result.file_folder,
					window: result.window,
					parent_folder: result.parent_folder,
				});
			});
		}
	});
	countMatches();
	console.log('Table Data', tableData);
	await table.setData(tableData);

	await filterTable();
}

async function filterTable() {
	let filters = [];
	if (beforeCheck.checked) filters.push('Before');
	if (selectedCheck.checked) filters.push('Selected');
	if (afterCheck.checked) filters.push('After');
	table.setFilter('context', 'in', filters);
}

[beforeCheck, selectedCheck, afterCheck].forEach((check) => {
	check.addEventListener('click', async () => {
		await filterTable();
	});
});

async function updateTarget() {
	data = [];
	await getOpenDocsPath();
	const searchState = getFinderValues();
	await setSearchState(searchState.finder);
	setTimeout(async () => {
		console.log('# OPEN DOCS =>', openFilesPath);
		let paths = [];
		if (openDocsCheck.checked) {
			await emit('tabulater:get-data', searchState);
		}
		let files_paths = [];
		if (selFilesCheck.checked) {
			files_paths = await filesFoldersStorage.getFilesPaths();
			console.log('FILES_PATHS', files_paths);
			if (files_paths.length > 0) {
				paths = files_paths;
			}
		}
		let dir_paths = [];
		if (selFoldersCheck.checked) {
			dir_paths = await filesFoldersStorage.getDirsPaths();
			console.log('DIR_PATHS', dir_paths);
			if (dir_paths.length > 0) {
				const filters = getFilters();
				console.log('filters', filters);
				const dir_files = await invoke('get_files_in_folders', { paths: dir_paths, filters });
				paths.push(...dir_files);
				paths = [...new Set(paths)];
			}
		}
		if (selFilesCheck.checked && openDocsCheck.checked) {
			console.log('Open files Path', openFilesPath);
			if (openFilesPath.length > 0) {
				paths = paths.filter((path) => !openFilesPath.includes(path));
			}
		}
		if (selFoldersCheck.checked || selFilesCheck.checked) {
			console.log('paths BEFORE', paths);
			if (selFoldersCheck.checked && dir_paths.length > 0) {
				const ignoredPaths = await checkSelectedFilesStatus();
				console.log('ignoredPaths', ignoredPaths);
				paths = paths.filter((path) => !ignoredPaths.includes(path));
			}
			console.log('## PAths', paths);
			let x = await invoke('search_in_files', { paths, finder: searchState.finder });
			data = data.concat(x);
		}
	}, 300);
	setTimeout(async () => {
		if (openDocsCheck.checked && !selFilesCheck.checked && !selFoldersCheck.checked) {
			console.log('-------------');
			console.log('openFilesPath', openFilesPath);
			let valid_paths = await filterValidPaths(openFilesPath);
			console.log('Valid Paths', valid_paths);
			console.log('data', data);
			data = data.filter((d) => valid_paths.includes(d.file_folder) || d.file_folder == 'unsaved');
		}
		console.log('');
		await tabulate();
		table.setGroupBy('file_folder');
		table.showColumn('file_name');
		toggleTableDisplay();
	}, 1000);
	await updateFilesFolderStatus();
}

openDocsCheck.addEventListener('input', async (e) => {
	await getOpenDocsPath();
});

tabulateBtn.addEventListener('click', async (e) => {
	e.stopImmediatePropagation();
	e.stopPropagation();
	data[0] = { ...data[0], finder: getFinderValues().finder };
	await updateTarget();
});

[matchTextArea, ignoreTextArea].map((textarea) => {
	textarea.addEventListener('blur', async () => {
		await updateFilesFolderStatus();
	});
});

async function openReviewWindow() {
	const label = appWindow.label;
	await invoke('open_matchignore', { label });
}

listen('find-table:get-review-data', async (e) => {
	const data = await getReviewData();
	console.log('app label', appWindow.label);
	console.log('Review Data', data, e.windowLabel);
	await new Window(e.windowLabel).emit('review:set-data', { data });
});

reviewBtn.addEventListener('click', async (e) => {
	e.stopImmediatePropagation();
	e.stopPropagation();

	await openReviewWindow();
});

async function gotoLine(row) {
	let { from, to, window } = row.getData();
	let wind = new Window(window);
	if (wind) {
		await wind.emit('finder:go-to', { from, to });
	}
}

async function updatePositions(from, delta, target, type = 'replace') {
	const elements = tableData.filter((row) => row.from >= from && row.file_name === target);
	let update = elements.map((row, index) => {
		const delta0 = type === 'replace' ? row.replacement.length : row.found.length;
		if (index == 0) {
			return {
				...row,
				from: row.from, //* (index + 1),
				to: row.to + delta, //* (index + 1),
			};
		}
		return {
			...row,
			from: Math.max(0, row.from + delta), //* (index + 1),
			to: row.to + delta, //* (index + 1),
		};
	});
	await table.updateData(update);
}

export async function replaceInstance(e, cell) {
	let btnType = e.target.innerText;
	let { from, to, window, file_name, file_folder, line, found, replacement } = cell.getRow().getData();
	let finder = getFinderValues().finder;
	let wind = new Window(window);
	if (btnType == 'Replace') {
		if (window && wind) {
			await wind.emit('finder:replace-tab', {
				pos: { from, to, found, replacement },
				replace: data[0].finder.replace,
				find: found, //data[0].finder.find,
			});
			unlisten = await wind.listen('tabulater:replace-tab-success', async () => {
				const delta = replacement.length - found.length;
				await updatePositions(from, delta, file_name);
				cell.getRow().getElement().classList.add('gray-row');
				e.target.innerText = 'Undo';
				unlisten();
			});
		} else {
			// alert('IN FILE');
			invoke('replace_value', {
				filePath: file_folder,
				instance: {
					pos: { from, to },
					replace: replacement, //data[0].finder.replace,
					find: data[0].finder.find,
					line: line - 1,
				},
				options: {
					case_sensitive: finder.case_sensitive,
					entire_word: finder.entire_word,
					regex: finder.grep ?? false,
				},
			})
				.then(async (res) => {
					if (res) {
						const delta = replacement.length - found.length;
						await updatePositions(from, delta, file_name);
						cell.getRow().getElement().classList.add('gray-row');
						e.target.innerText = 'Undo';
					}
				})
				.catch((e) => {
					console.error(e);
					new Alert(e, AlertType.ERR, 1500);
				});
		}
	} else if (btnType == 'Undo') {
		if (window && wind) {
			await wind.emit('finder:undo-tab', {
				pos: { from, to },
				find: found, //data[0].finder.find,
				replace: replacement, //data[0].finder.replace,
			});
			unlisten = await wind.listen('tabulater:undo-tab-success', async () => {
				cell.getRow().getElement().classList.remove('gray-row');
				e.target.innerText = 'Replace';
				const delta = /* data[0].finder.find.length */ found.length - replacement.length;
				await updatePositions(from, delta, file_name, 'undo');
				unlisten();
			});
		} else {
			console.log({
				filePath: file_folder,
				instance: {
					pos: { from, to: from + found.length },
					find: replacement, //data[0].finder.replace,
					replace: found,
					line: line - 1,
				},
				options: {
					case_sensitive: finder.case_sensitive,
					entire_word: finder.entire_word,
					regex: false,
				},
			});
			invoke('replace_value', {
				filePath: file_folder,
				instance: {
					pos: { from, to },
					find: replacement, //data[0].finder.replace,
					replace: found,
					line: line - 1,
				},
				options: {
					case_sensitive: finder.case_sensitive,
					entire_word: finder.entire_word,
					regex: false,
				},
			})
				.then(async (res) => {
					if (res) {
						cell.getRow().getElement().classList.remove('gray-row');
						e.target.innerText = 'Replace';
						const delta = /* data[0].finder.find.length */ found.length - replacement.length;
						await updatePositions(from, delta, file_name);
					}
				})
				.catch((e) => {
					console.error(e);
					new Alert(e, AlertType.ERR, 1500);
				});
		}
	}
}

async function updateCurrentSetPositions(from, window) {
	let cumulativeDelta = 0;
	const elements = tableData.filter((row) => row.from >= from && row.window == window);
	elements.sort((a, b) => a.from - b.from);
	let update = elements.map((row) => {
		const oldLength = row.found.length;
		const newLength = row.replacement.length; //getFinderValues().finder.replace.length;
		const newFrom = row.from + cumulativeDelta;
		const newTo = newFrom + newLength;

		cumulativeDelta += newLength - oldLength;

		return {
			...row,
			from: newFrom,
			to: newTo,
		};
	});

	await table.updateData(update);
}

async function updateFilesCurrentSetPositions(from, path) {
	let cumulativeDelta = 0;
	let elements = tableData.filter((row) => !row.window && row.file_folder === path && row.from >= from);
	elements.sort((a, b) => a.from - b.from);
	let update = elements.map((row) => {
		const oldLength = row.found.length;
		const newLength = row.replacement.length; //getFinderValues().finder.replace.length;
		const newFrom = row.from + cumulativeDelta;
		const newTo = newFrom + newLength;

		cumulativeDelta += newLength - oldLength;

		return {
			...row,
			from: newFrom,
			to: newTo,
		};
	});

	await table.updateData(update);
}

async function replaceCurrentSet() {
	let rows = table.getRows('active');
	let payload = {
		replace: data[0].finder.replace,
		find: data[0].finder.find,
		positions: [],
	};
	let groups = { windows: {}, files: {} };
	rows.forEach((row) => {
		const { from, to, window, file_folder, line, found, replacement } = row.getData();
		if (window) {
			const windowData = { from, to, found, replacement };
			if (window in groups.windows) groups.windows[window].push(windowData);
			else groups.windows[window] = [windowData];
		} else if (file_folder) {
			const fileData = {
				pos: { from, to },
				line,
				find: found,
				replace: replacement,
			};
			if (groups.files[file_folder]) groups.files[file_folder].push(fileData);
			else groups.files[file_folder] = [fileData];
		}
	});
	if (Object.keys(groups.windows).length > 0) {
		for (let key in groups.windows) {
			payload.positions = groups.windows[key];
			let wind = new Window(key);
			await wind.emit('finder:tab-replace-current-set', payload);
			unlisten = await wind.listen('tabulater:tab-replace-current-set-success', async () => {
				await updateCurrentSetPositions(0, key);
				rows.forEach((row) => {
					const rowEl = row.getElement();
					const replaceEl = row.getElement().getElementsByClassName('replace-btn')[0];
					if (window) {
						rowEl.classList.add('gray-row');
						replaceEl.innerText = 'Undo';
					}
				});
				unlisten();
			});
		}
	}
	if (Object.keys(groups.files).length > 0) {
		for (let key in groups.files) {
			const file_path = key;
			const payload = groups.files[key];

			invoke('replace_values', { instances: payload, filePath: file_path })
				.then(async (res) => {
					rows.forEach((row) => {
						const { from, to, window, file_folder, line } = row.getData();
						const rowEl = row.getElement();
						const replaceEl = row.getElement().getElementsByClassName('replace-btn')[0];
						if (!window && file_folder === file_path && !res.includes({ from, to })) {
							rowEl.classList.add('gray-row');
							replaceEl.innerText = 'Undo';
						}
					});
					await updateFilesCurrentSetPositions(0, file_path);
				})
				.catch((e) => {
					console.error(e);
					new Alert(e, AlertType.ERR, 1500);
				});
		}
	}
}

document.getElementById('replace-current-set').addEventListener('click', replaceCurrentSet);

export function getTabulatedPaths() {
	let paths = new Set();
	if (tableData.length > 0) {
		tableData.map((data) => {
			if (data.file_folder != '') {
				paths.add(data.file_folder);
			}
		});
		return [...paths];
	} else return [];
}

export async function updateGroupsStatus(ignoredPaths) {
	if (tableData.length > 0) {
		tableData = tableData.map((data) => {
			return { ...data, ignored: ignoredPaths.includes(data.file_folder) };
		});
	}
	console.log('TableData Check Stats', tableData);
	await table.replaceData(tableData);
}

Tabulator.registerModule([
	SortModule,
	FormatModule,
	FilterModule,
	EditModule,
	ResizeColumnsModule,
	ResizeTableModule,
	InteractionModule,
	GroupRowsModule,
]);

var table = new Tabulator('#table', {
	autoColumns: false,
	autoResize: true,
	layoutColumnsOnNewData: true,
	layout: 'fitColumns',
	reactiveData: true,
	data: tableData,
	groupHeader: fileNameGroupHeader,
	initialSort: [{ column: 'file_folder', dir: 'desc' }],
	columns: [
		{
			formatter: rightTabIcon,
			width: 60,
			hozAlign: 'center',
			headerSort: false,
			cellClick: async (_, cell) => {
				await gotoLine(cell.getRow());
			},
		},
		{
			title: 'Context',
			field: 'context',
			width: 120,
			hozAlign: 'left',
			headerSort: false,
		},
		{
			title: 'Line',
			field: 'line',
			width: 100,
			hozAlign: 'center',
			headerFilter: minMaxFilterEditor,
			headerFilterFunc: minMaxFilterFunction,
			headerFilterLiveFilter: false,
		},
		{
			title: 'Found',
			field: 'findLine',
			formatter: 'html',
			headerFilter: 'input',
			editorParams: {
				elementAttributes: {
					spellcheck: false,
					autocapitalize: false,
					autocorrect: false,
				},
			},
		},
		{
			title: 'With Replace',
			field: 'replaceLine',
			formatter: 'html',
			headerFilter: 'input',
		},
		{
			title: 'From',
			field: 'from',
			width: 70,
			hozAlign: 'center',
			headerFilter: 'number',
			visible: false,
		},
		{
			title: 'To',
			field: 'to',
			width: 70,
			hozAlign: 'center',
			headerFilter: 'number',
			visible: false,
		},
		{
			title: 'Document name',
			field: 'file_name',
			formatter: 'html',
			headerFilter: 'input',
			visible: false,
		},
		{
			title: 'Found',
		},
	],
});

// Finder Clone

export function getFinderValues() {
	return {
		finder: {
			find: findIn.value,
			replace: replaceIn.value,
			case_sensitive: caseCheck.checked,
			entire_word: entireWordCheck.checked,
			grep: regexCheck.checked,
			selected_only: data[0] ? data[0].finder.selected_only : false,
		},
	};
}

export function setFinderValues(state) {
	console.log('State', state);
	findIn.value = state.find ?? '';
	replaceIn.value = state.replace ?? '';
	caseCheck.checked = state.case_sensitive ?? false;
	entireWordCheck.checked = state.entire_word ?? true;
	regexCheck.checked = state.grep ?? false;
}

listen('alert', (d) => {
	const { msg, type } = d.payload;
	new Alert(msg, type);
	unlisten();
});

//like a constructure

(async () => {
	findIn.focus();
	const searchState = await getSearchState();
	setFinderValues(searchState);
	await emit('tabulater:created', {});
})();
