"""
sync_utils.py - update data table & dialogs as user makes changes

	i.e. changes in 1 place often affect several others places
"""

from browser import aio, document, window
import copy
import javascript
import json
import queue
import tauri

# local imports
import deeply_utils
import table_utils
import app_data

app_config = None  # must be loaded async
current_window = tauri.window.getCurrentWindow()  # TBD: or None then fetch later

sort_label = f'{current_window.label}_sort_editor'
layout_label = f'{current_window.label}_layout_editor'
search_label = f'{current_window.label}_search_editor'

# ---
# documentation TBD: add notes for all of these!

window_label = None
window_title = None

table = None  # tabulator object with functions like getColumn, getRows, on, searchRows, setSort

edits_db = None
cache_db = None
actions_db = None

table_data = []  # row_objects
header_data = []
filtered_data = []
sorted_data = []

main_table_sorters = []  # documentation TBD: why?
sorters_copy = []  # documentation TBD: why?

columns = []  # TBD: probably rename to column_names, but first verify

checked_checkbox_filter_status = False
hidden_checkbox_filter_status = False

column_width_queue = queue.Queue()  # TBD: why only column_width? Should probably queue most changes
current_filters = []

checked_rows = []
hidden_rows = []

search_options_dropdown = dict()

create_table_done = True  # a 'lock' for column definitions; documentation TBD: why only file_data.py but not elsewhere?

platform = {  # TBD: obsolete? or refactor
	'using_windows': '',
	'using_other': '',
}

# -----
def make_header_data(table):
	# create a simple dict from tabulator column info
	global header_data
	# globals not modified here: columns (column_names)

	header_data = []
	for col in columns:  # for column_name in column_names
		column = table.getColumn(col)  # column_obj
		if column:
			column_data = {
				'visible': column.isVisible(),
				'width': column.getWidth(),
				'name': column.getField(),
				'label': table_utils.create_label(column.getField()),
				'type': '(unknown)',  # not currently in use but we should implement it
			}
			header_data.append(column_data)

	# documentation TBD
	import open_file
	open_file.header_data = header_data


def update_row_visibility(event, table, table_type):
	if event:  # checkbox clicked
		event.stopImmediatePropagation()

	if not (table and app_config.flags.can_select_multiple_rows):
		return

	if not app_config.is_paid_or_trial: #tbd redundant
		table_utils.notify_user()
		return

	try:
		show_hidden = document['show-hidden-checkbox'].checked
		rows = table.getRows()
		active_rows_len = len(table.getRows('active'))

		for row in rows:
			if not row.getData()[table_utils.row_visibility]:
				if show_hidden:
					row.getElement().style.backgroundColor = app_data.light_gray_color_main_table
					row.getElement().style.display = ''
				else:
					if not row.getElement().style.display == 'none':
						row.getElement().style.display = 'none'
						active_rows_len = active_rows_len - 1
			else:
				row.getElement().style.backgroundColor = app_data.white_color

		table_utils.update_shown_entries(active_rows_len, table, table_type)

		if event:  # checkbox clicked
			if show_hidden:
				val = 'True'
			else:
				val = 'False'
			update_search_hidden_checkbox(val)

	except Exception as e:
		print('ERROR in update_row_visibility', e)


def bind_header_filter_events(table):
	global checked_checkbox_filter_status
	header_filters = document.select("#table .tabulator-header input[type='text']")

	for input_field in header_filters:
		def handler(event, input_field=input_field):
			global current_filters
			# refactor TBD: looks like element isn't being used
			try:
				element = input_field.closest('.tabulator-col')
			except Exception as e:
				print('ERROR in sync_utils -- bind_header_filter_events handler', e)
				element = None

			if element is None:
				# Handle the case where no matching parent was found
				field_name = None
				field_name = input_field.getAttribute('field')
			else:
				field_name = input_field.getAttribute('field') #element.getAttribute('tabulator-field')

			default_operator = search_options_dropdown.get(field_name, 'like')
			for cur_filter in current_filters:
				if cur_filter['field'] == field_name:
					default_operator = search_options_dropdown.get(field_name, 'like')
					break

			update_main_tabulator_filter(field_name, input_field.value, default_operator, False)

			args = {
				'filters': json.dumps({'field': field_name, 'value': input_field.value, 'operator': default_operator}),
				'label': search_label,
				}
			tauri.invoke('update_search_editor_data', args)  # emit update_editor_field

		input_field.setAttribute('field', input_field.closest('.tabulator-col').getAttribute('tabulator-field'))
		input_field.bind('blur', handler)

	def checkbox_click(evt):
		evt.stopImmediatePropagation()
		checked_checkbox_filter_status = evt.target.checked
		# print('checked_checkbox_filter_status', checked_checkbox_filter_status)
		args = {
			'filter': json.dumps({'checked_value':evt.target.checked}),
			'label': search_label,
			}
		tauri.invoke('update_search_editor_checkbox', args)

	checkbox = document.querySelector('#table .tabulator-header input[type="checkbox"]')
	if checkbox:
		checkbox.bind('click', checkbox_click)


def update_header_filters(table):
	deeply_utils.pp('update_header_filters -- table', table)
	all_header_inputs = document.select("#table .tabulator-header input[type='text']")
	for input_field in all_header_inputs:
		input_field.value = ''
		input_field.setAttribute('spellcheck', 'false')

	deeply_utils.pp('sync_tuils.py - update_header_filters - all_header_inputs:', all_header_inputs)
	if table:
		for f in current_filters:
			header_inputs = document.select(
				f"div[tabulator-field='{f['field']}'] input[type='text']")

			if header_inputs and len(header_inputs) > 0:
				header_inputs[0].value = f['value']

		bind_header_filter_events(table)


def update_data_column_order(new_order):
	# called by update_column_order: iterates thru each row_dict and makes a copy in the new order
	# globals not modified here: table_data

	# performance TBD: is this operation expensive for a large file? if so, is there an alternative?
	# performance/memory TBD: this makes a copy (and then doesn't update the global) but should probably work in place

	# print('update_data_column_order', table_data)

	updated_data = []
	for entry in table_data:  # for row_object in row_objects ... though works like row_dicts
		# since Python 3.7, a regular dict preserves insertion order (with less overhead than OrderedDict)
		ordered_entry = {key: entry[key] for key in new_order if key in entry}
		updated_data.append(ordered_entry)

	return updated_data

#...................................end...........................


def update_hide_button_text():

	if not (table and app_config.flags.can_select_multiple_rows):
		return

	rows = table.getRows()
	selected_rows = []
	for row in rows:
		checkbox = row.getCell(table_utils.row_checked).getElement().querySelector('input[type="checkbox"]')
		if checkbox and checkbox.checked:
			selected_rows.append(row)

	if selected_rows:
		document['hide-btn'].removeAttribute('disabled')
		all_hidden = all(not row.getData()[table_utils.row_visibility] for row in selected_rows)
		document['hide-btn'].text = 'Unhide' if all_hidden else 'Hide'
	else:
		document['hide-btn'].setAttribute('disabled', "")
		document['hide-btn'].text = 'Hide'


# Save and restore the state of the rows in the table
def save_row_states(table):
	global checked_rows, hidden_rows

	# performance TBD: is this expensive for a large table?

	if not (table and app_config.flags.can_select_multiple_rows):
		return

	for row in table.getRows():
		row_data = row.getData()

		if row_data[table_utils.row_checked]:
			checked_rows.append(int(row_data[table_utils.file_row]))

		if row_data[table_utils.row_hidden]:
			hidden_rows.append(int(row_data[table_utils.file_row]))


def restore_row_states(table, table_data):
	# globals not modified here: checked_rows, hidden_rows

	# refactor TBD: delete table_data param?

	if not (table and app_config.flags.can_select_multiple_rows):
		return

	show_hidden = document['show-hidden-checkbox'].checked
	for checked_index in checked_rows:
		#checked_row = table.getRowFromPosition(checked_index, True)
		checked_row = table.searchRows(table_utils.file_row, '=', checked_index)[0]
		if checked_row:
			checked_row.update({table_utils.row_checked: True})
			checkbox = checked_row.getElement().querySelector("input[type='checkbox']")
			if checkbox:
				checkbox.checked = True  # Update the checkbox visually
			update_hide_button_text()

	for hidden_index in hidden_rows:
		#hidden_row = table.getRowFromPosition(hidden_index+1, True)
		hidden_row = table.searchRows(table_utils.file_row, '=', hidden_index)[0]
		if hidden_row:
			hidden_row.update({table_utils.row_hidden: True, table_utils.row_visibility: False})
			if show_hidden:
				# If 'Show Hidden' is checked, change the background color
				hidden_row.getElement().style.backgroundColor = app_data.light_gray_color_main_table
				hidden_row.getElement().style.display = ''  # Ensure it's visible
			else:
				# If 'Show Hidden' is not checked, hide the row
				hidden_row.getElement().style.display = 'none'
#.................................end..................................


def apply_sorting_to_table(table, table_sorters=None):
	global main_table_sorters, sorters_copy
	# globals not modified here: header_data
	# print('sync_utils -- apply_sorting_to_table')
	if table_sorters:
		sorters = table_sorters
		sorters_copy = sorters
		main_table_sorters = sorters
	else:
		sorters = main_table_sorters.copy()

	if sorters_copy:
		sorters = sorters_copy.copy()

	if sorters:
		sorters.reverse()

	if table:
		table.setSort(sorters)


# layout editor and main table sync

# LAYOUT editor dialog to main table Window
def update_column_visibility(tauri_message):
	# globals not modified: table

	col_data = json.loads(dict(tauri_message)['payload'])
	if table:
		column = table.getColumn(col_data['name'])
		if column:
			if col_data['is_visible']:
				column.updateDefinition({'visible': True})
				column.show()
			else:
				column.hide()
				column.updateDefinition({'visible': False})
	else:
		print('ERROR or WARNING: no table')

	for header in header_data:
		if header['name'] == col_data['name']:
			header['visible'] = col_data['is_visible']
			break

	bind_header_filter_events(table)
	restore_search_vals(current_filters)
	restore_sorters(main_table_sorters, table)


def queue_column_width(tauri_message):
	global column_width_queue

	col_data = json.loads(dict(tauri_message)['payload'])

	deeply_utils.pp('queue_column_width; col_data', col_data)

	column_width_queue.put(col_data)


def do_column_width(col_data):
	# globals not modified: table

	deeply_utils.pp('do_column_width -- col_data', col_data)
	cur_sorters = main_table_sorters
	column = table.getColumn(col_data['name'])

	deeply_utils.pp('do_column_width -- column', column)

	if column:
		column.updateDefinition({'width': col_data['width']})
		window.setTimeout(lambda: update_header_filters(table), 200)
		window.setTimeout(lambda: (restore_sorters(cur_sorters, table), apply_sorting_to_table(table)), 400)
	else:
		print('ERORR: called with no column')


def update_column_order(tauri_message, table, create_tabulator_table_CALLBACK, get_table_CALLBACK):
	global header_data
	# globals not modified here: sorters_copy -- maybe others

	# FYI: I tried to use table as global and omit from column_order_DEBOUNCE but that didn't work (and not worth debugging)

	# summary
	# - update_data_column_order to change the order within each row (an implied OrderedDict)
	# - save_row_states: global lists of checked or hidden rows
	# - create a new tabulator table via callback into db_*, file_data, edits, history
	# - restore_row_states
	# - apply search/sort

	# refactor TBD: why pass table AND get_table_CALLBACK which returns table data
	# ... they seem to usually be the same but maybe not always? TBD: add debug code to compare!
	# ... I also don't understand why history.py get_table() is different, but works

	deeply_utils.pp('update_column_order with tauri_message', tauri_message)

	cur_filters = current_filters  # refactor TBD: why a 2nd var?
	new_order = json.loads(dict(tauri_message)['payload'])['order']
	header_data = sorted(header_data, key=lambda x: new_order.index(x['name']))

	# refactor TBD: the other code should probably work in place! ... if not perhaps rename new_table_data ?
	new_tabulator_data = update_data_column_order(new_order)  # depends on global table_data

	sorters = sorters_copy.copy()

	if table:
		save_row_states(table)  # checked or hidden rows; not in 1.0
		table.destroy()
		table = None

		assert create_tabulator_table_CALLBACK, 'no create_tabulator_table_CALLBACK'
		create_tabulator_table_CALLBACK(new_tabulator_data, False)  # does changing column order require a new table?

		assert get_table_CALLBACK, 'no get_table_CALLBACK'
		table = get_table_CALLBACK()

		# restore_row_states is for checked or hidden rows; not in 1.0
		window.setTimeout(lambda:(restore_row_states(table, new_tabulator_data), restore_search_vals(cur_filters), add_checkbox_filters(table)), 150)
	else:
		table = None
		# documentation TBD: why wouldn't there be a table -- and if not, should probably return right away
		print('WARNING or ERROR: no table in update_column_order')

	args = {
		'order': json.dumps({'new_order': new_order}),
		'label': search_label,
		}
	tauri.invoke('update_search_column_order', args)  # emit change_column_order

	args = {
		'order': json.dumps({'new_order': new_order, 'sorters': sorters}),
		'label': sort_label,
		}
	tauri.invoke('update_sort_column_order', args)  # emit change_sort_col_order

	window.setTimeout(lambda: apply_sorting_to_table(table, sorters), 250)


changin_width = False  # documentation TBD -- seems to be a signal that code is pulling from the queue, but not sure why that should block anything


def check_column_width_queue():
	global column_width_queue, changin_width
	# globals not modified here: table -- passed to do_column_width (but maybe better for that to access directly?)

	if changin_width:  # documentation TBD: why not just check the queue?
		print('processing queue')
		return

	while not column_width_queue.empty():
		changin_width = True
		col_data = column_width_queue.get()  # pull the first item off the queue

		deeply_utils.pp('pulled from queue', col_data)
		do_column_width(col_data)

	changin_width = False


async def run_check():
	# TBD: why ONLY this queue?
	while True:
		check_column_width_queue()
		await aio.sleep(3)  # 3 seconds


# main table window to LAYOUT editor dialog
# width updated from main table
def column_width_updated(column):
	global header_data
	# column is a tabulator object

	key = column.getField()
	new_width = column.getWidth()

	# header_data is a list so need to check each item until find the desired column
	for col in header_data:
		if col['name'] == key:
			col['width'] = new_width
			break

	args = {
		'column': json.dumps({
			'key': key,
			'new_width': new_width,
			'table_data': header_data}),  # the LAYOUT editor's table is based on the regular data's header
		'label': layout_label}

	tauri.invoke('column_width_updated', args)  # emit 'update_layout_row_width'

#.................................end...................................

# sync between sort dialog and main table

# SORT dialog to main table window
def sort_from_sort_dialog(tauri_message):
	global main_table_sorters, sorters_copy
	# global not modified: table
	sorters = json.loads(dict(tauri_message)['payload'])
	main_table_sorters = sorters.copy()
	sorters_copy = sorters.copy()

	# print('sync_utils.py - sort_from_sort_dialog - sorters:', sorters)
	if table:
		table.setSort(sorters)
	else:
		print('ERROR or WARNING? called sort_from_sort_dialog without a table')


# main table window to SORT dialog
def sort_from_main_table(event, column):
	field = column['getField']()
	args = {'field': json.dumps(field), 'label': sort_label}
	tauri.invoke('sort_from_main_table', args)  # emits sort_from_main_table [same!]

#..................................end................................


# sync between SEARCH dialog and main table
def checked_checkbox_filter(data, filter_params, *args):
	data_value = data[table_utils.row_checked]
	value_to_match = filter_params['value']
	if not value_to_match:
		return True

	return data_value == value_to_match


def checked_checkbox_filter_function(data, filter_params):
	# documentation TBD: why have this function?
	return checked_checkbox_filter(data, filter_params)


def hidden_checkbox_filter(data, filter_params, *args):
	data_value = data[table_utils.row_hidden]
	value_to_match = filter_params['value']
	if not value_to_match:
		return True

	return data_value == value_to_match


def hidden_checkbox_filter_function(data, filter_params):
	# documentation TBD: why have this function?
	return hidden_checkbox_filter(data, filter_params)


def add_checkbox_filters(table):
	# documentation TBD: describe this code
	# 'hidden_checkbox_filter_status' may need a new name since future code will do other things with checked rows

	# globals not modified here: checked_checkbox_filter_status, hidden_checkbox_filter_status
	header_filter = document.querySelector('.tabulator-header-filter input[type="checkbox"]')

	if not header_filter:
		# e.g. disabled via FEATURE FLAG
		return

	# refactor TBD: more DRY e.g. value should be checked_checkbox_filter_status (converted to Boolean if needed)
	if checked_checkbox_filter_status:
		header_filter.checked = True
		table.addFilter(
			checked_checkbox_filter_function,
			{'field': table_utils.row_checked, 'type': '=', 'value': True}
			)
	else:
		table.addFilter(
			checked_checkbox_filter_function,
			{'field': table_utils.row_checked, 'type': '=', 'value': False}
			)

		header_filter.checked = False

	# refactor TBD: more DRY e.g. value should be hidden_checkbox_filter_status (converted to Boolean if needed)
	if hidden_checkbox_filter_status:
		table.addFilter(hidden_checkbox_filter_function, {'field': table_utils.row_hidden, 'type': '=', 'value': True})
	else:
		table.addFilter(hidden_checkbox_filter_function,{'field': table_utils.row_hidden, 'type': '=', 'value': False})


# SEARCH dialog to main table window
def filter_from_filter_dialog(tauri_message):
	global current_filters
	# globals not modified: table
	p_load = json.loads(dict(tauri_message)['payload'])
	current_filters = p_load['current_filters']
	op_change = p_load['op_change']

	for k, v in op_change.items():
		search_options_dropdown[k] = v

	if not table:
		print('ERROR or WARNING: no table')
		return

	try:
		table.clearFilter(True)
	except Exception as e:
		print('ERROR in filter_from_filter_dialog clearFilter:', e)

	table.setFilter(current_filters)
	update_header_filters(table)
	add_checkbox_filters(table)
	restore_search_vals(current_filters)


def filter_from_filter_dialog_checkbox(tauri_message):
	global checked_checkbox_filter_status, hidden_checkbox_filter_status
	# globals not modified: table

	p_load = json.loads(dict(tauri_message)['payload'])
	checked = p_load['checked']
	hidden = p_load['hidden']

	if checked:
		checked_checkbox_filter_status = p_load['checked_checkbox_filter_status']
	if hidden:
		hidden_checkbox_filter_status = p_load['hidden_checkbox_filter_status']

	if table:
		table.clearFilter(True)
		table.setFilter(current_filters)
		update_header_filters(table)
		add_checkbox_filters(table)
	else:
		print('ERROR or WARNING: no table')


def update_show_hidden(tauri_message):
	show_or_hide = False if dict(tauri_message)['payload'] == 'False' else True
	document['show-hidden-checkbox'].checked = show_or_hide

	if False:###DEBUG
		print('-\n' * 5)  ### 2025-02-03: I saw a bug that update_row_visibility lacked table param but not sure how to reproduce
		if table:
			print('update_show_hidden: table (global)', type(table), table)
		else:
			print('update_show_hidden: table is null/none/empty:', table)

		print('update_show_hidden: tauri_message', type(tauri_message), tauri_message)

	update_row_visibility(None, table)  # 2025-02-03: added table but maybe it should come from new update_show_hidden param


# main table window to SEARCH dialog
def update_search_hidden_checkbox(val):
	arg = {'showHide': val, 'label': search_label}
	tauri.invoke('update_search_hidden', arg)  # emit update_hidden_checkbox

#.................................end.............................

def apply_sorting_to_table(table, table_sorters=None):
	global main_table_sorters, sorters_copy
	# #globals not modified here: header_data
	# print('sync_utils -- apply_sorting_to_table')
	if table_sorters:
		sorters = table_sorters
		sorters_copy = sorters
		main_table_sorters = sorters
	else:
		sorters = main_table_sorters.copy()

	if sorters_copy:
		sorters = sorters_copy.copy()

	if sorters:
		sorters.reverse()

	if table:
		try:
			table.setSort(sorters)
		except Exception as e:
			print('ERROR in apply_sorting_to_table setSort:', e)


def compare_column_names(column_name, comparison_columns):
	# low priority performance TBD: create a proper data structure so the caller can check directly rather than this loop (which is called twice for every column)
	return any(col['name'] == column_name for col in comparison_columns)


# sync between DEFINE (edit columns) dialog and parent table
async def update_table_columns(tauri_message, table_type, create_table_CALLBACK=None, cache_db_object=None, get_cached_data_CALLBACK=None):
	global header_data, sorters_copy
	# globals not modified: table

	# documentation TBD: summarize each param; many are not clear
	# also globals and vars created here, e.g. column_dicts is essentially new_header_data
	# e.g. 2nd 'if not from_db' calls db.delete... so 'db' means different things!

	added_col_names = []
	removed_col_names = []
	columns_to_keep = []
	columns_to_add = []
	columns_to_select = [table_utils.file_row]  # start with primary key (this might change if from_db is True)

	column_dicts = json.loads(dict(tauri_message)['payload'])['columns']

	if table_type == 'db_table':  # from db_data.py
		from_db = True
	else:
		# 'csv_tsv' or json, html
		from_db = False

	if not from_db:  # TEMPORARY check; this code should apply to both
		# determine column changes -- TBD: implement for SQLite DBs too!
		# performance TBD: probably faster by intersecting sets, but not material

		# 1. determine columns_to_add
		for col in column_dicts:  # iterate thru the 'AFTER' columns
			if not compare_column_names(col['name'], header_data):
				added_col_names.append(col['name'])
				if col['name'] not in table_utils.built_in_fields:
					columns_to_add.append(col['name'])
					columns_to_select.append(col['name'])

		# 2. determine removed_col_names & columns_to_keep
		for col in header_data:  # iterate thru the 'BEFORE' columns
			if not compare_column_names(col['name'], column_dicts):
				removed_col_names.append(col['name'])
			elif col['name'] not in table_utils.built_in_fields:
				columns_to_keep.append(col['name'])
				columns_to_select.append(col['name'])

		# print('sync_utils -- columns to keep', columns_to_keep)
		# print('sync_utils -- columns to add', columns_to_add)

		# 3. save a copy of deleted column data, then delete
		if removed_col_names:
			columns_to_keep.insert(0, table_utils.file_row)
			# performance TBD: is table.getData() a bottleneck? If so, any better option?
			await save_utils.save_deleted_columns(table.getData(), removed_col_names)  # save to disk!
			await db.delete_table_columns(columns_to_keep)

		if added_col_names:
			await db.add_table_columns(columns_to_add)

		# print('sync_utils -- removed col names', removed_col_names)
		# print('sync_utils -- added col names', added_col_names)

		# 4. summarize
		if removed_col_names or added_col_names:
			timestamp = deeply_utils.now_string()
			the_field = 'COLUMN CHANGES'  # a hack to squeeze this data into 'edits' schema, including to display if user opens a 'changed' file
			old_value = ''

# 			old_value = []
# 			for col in header_data:
# 				if col['name'] not in table_utils.built_in_fields:
# 					old_value.append(col['name'])

			# earlier draft kept a copy of entire set of cols but I think it's better to focus on changes
			# - i.e. I think of the edit as the change itself, not as going from one list of cols to another list of cols
			# - it's also difficult for users to make sense of 2 lists (before/after)
# 			new_value = copy.deepcopy(columns_to_select)
# 			new_value.remove(table_utils.file_row)

			# refactor TBD: these should call a common function
			if removed_col_names:
				the_id = 'removed'
				new_value = ', '.join(removed_col_names)

				edits_data = [the_id, the_field, old_value, new_value, timestamp, edits_db.path_relative]
				await edits_db.insert_data(edits_data)

			if added_col_names:
				the_id = 'added'
				new_value = ', '.join(added_col_names)

				edits_data = [the_id, the_field, old_value, new_value, timestamp, edits_db.path_relative]
				await edits_db.insert_data(edits_data)

	header_data = column_dicts  # set the new columns
	cur_sorters = main_table_sorters  # TBD: why 2 sorters?
	sorters_copy = main_table_sorters

	if not from_db:
		# documentation TBD: what will this look like when 'from_db' is supported
		if len(columns_to_select) > 1:
			await get_cached_data_CALLBACK(False, columns_to_select)
		# else: when would this ever NOT be true

	# performance TBD: is there a more efficient solution vs. rebuilding from scratch?
	create_table_CALLBACK(table_data, False)  # for csv/txt or db


async def definitions_changed(tauri_message, table_s, table_type, cache_db_object=None, create_table_CALLBACK=None, get_cached_data_CALLBACK=None):
	# sync between DEFINE (column editor) and data table
	# (only applies to the csv/txt and (when implemented) DB data tables i.e. file_data.py & db_data.py)

	# Scott TBD July: test this function!

	# documentation TBD: what is table_s and why that name?
	await update_table_columns(tauri_message, table_s, table_type, cache_db_object, create_table_CALLBACK, get_cached_data_CALLBACK)

	while not create_table_done:  # wait for table to be re-created
		# TBD: sleep briefly, and set max attempts to prevent getting stuck here
		pass

	window.setTimeout( lambda: (restore_sorter_window_sorters(sorters_copy)), 500)
	window.setTimeout( lambda: (apply_sorting_to_table(table, sorters_copy)), 500)
	window.setTimeout( lambda: (update_header_filters(table), table_utils.update_shown_entries(len(table.getRows('active')), table, table_type)), 2000)


def restore_sorter_window_sorters(sorters):
	sorters.reverse()
	for h_data in header_data:
		if h_data['width'] == javascript.NULL:
			h_data['width'] = 0
	args = {'sorters': json.dumps({'sorters': sorters, 'columns': header_data}), 'label': sort_label}
	tauri.invoke('columns_changed', args)  # emit columns_edited

	window.setTimeout(lambda: (update_layout_columns()), 50)


# refactor TBD: other code should probably call these directly
def update_search_columns():
	deeply_utils.pp('sync_utils.py -- update_search_columns')
	args = {'columns': json.dumps({'header_data': header_data, 'columns': columns}), 'label': search_label}
	tauri.invoke('update_search_columns', args)  # emit 'change_search_columns' to call update_search_columns in search_dialog.py


def update_layout_columns():
	deeply_utils.pp('sync_utils.py -- update_layout_columns')
	args = {'columns': json.dumps({'header_data': header_data, 'columns': columns}), 'label': layout_label}
	tauri.invoke('update_layout_columns', args)

	### Scott TBD July: is this a copy/paste error??? Check to see if it's in OLD code.
	# window.setTimeout(lambda: (update_search_columns()), 50)  # TBD: why only search but not sort or others?

#....................................end.....................................

# 'on' table events

def on_header_click(event, column, table):
	header_sort_event_bind(event, column, table)
	sort_from_main_table(event, column)


def on_table_column_moved(column, columns, table):
	global header_data
	new_order = [col.getField() for col in columns]

	header_data = sorted(
		header_data,
		key=lambda x: new_order.index(x['name'])
		if x['name'] in new_order
		else -1,
	)
	for h_data in header_data:
		if h_data['width'] == javascript.NULL:
			h_data['width'] = 0
	args = {'order': json.dumps({'order': header_data}), 'label': layout_label}
	tauri.invoke('main_table_column_moved', args)  # emit column_order_changed

	args = {'order': json.dumps({'new_order': new_order}), 'label': search_label}
	tauri.invoke('update_search_column_order', args)  # emit change_column_order


def on_table_data_sorted(sorters, rows, table):
	deeply_utils.pp('on_table_data_sorted')
	global sorted_data, main_table_sorters
	main_table_sorters = []
	sorted_data = rows
	for sorter in sorters:
		sorter_dict = dict(sorter)
		main_table_sorters.append({'column': sorter_dict['field'], 'dir': sorter_dict['dir'] })


def on_table_data_filtered(filters, rows, table):
	# performance TBD: this might get called for every column even when there's no change
	deeply_utils.pp('sync_utils.py -- on_table_data_filtered')

	global filtered_data, main_table_filters
	main_table_filters = []
	filtered_data = rows  # set the global

	for filter in filters:
		filter_dict = dict(filter)
		main_table_filters.append(filter_dict)
		deeply_utils.pp('filter_dict', filter_dict)

		### DEBUG: but Tabulator allows custom filters e.g. for our checkbox
		# import inspect
		# field = filter_dict.get('field')
		# if inspect.isfunction(field) or callable(field):
		# 	raise TypeError(f"Invalid filter: 'field' is a function ({field})")
		### ^^^ DEBUG

	deeply_utils.pp('main_table_filters', main_table_filters)

	num_rows = len(rows)
	for row in rows:
		if row.getElement().style.display == 'none':
			num_rows = num_rows -1
	table_utils.update_shown_entries(num_rows, table, 'csv_tsv')


def on_table_data_loaded(evt, table):
	if not table:
		print('ERROR: Tabulator instance is not available.')
		return

	for header in header_data:
		if not header['visible']:
			col = table.getColumn(header['name'])
			col.hide()

	table.setFilter(current_filters)
	update_header_filters(table)


def on_table_data_loaded_init(evt, table):

	deeply_utils.pp('on_table_data_loaded_init')

	if table:
		make_header_data(table)  # sets the global
	else:
		print('ERROR: Tabulator instance is not available.')
		pass

	document['edit-column'].removeAttribute('disabled')  # Define button
	document['edit-layout'].removeAttribute('disabled')
	document['edit-search'].removeAttribute('disabled')
	document['edit-sort'].removeAttribute('disabled')
	document['export-btn'].removeAttribute('disabled')

	bind_header_filter_events(table)

#.................................end..............................

# hide or show rows

def main_tabulator_row_toggle_visibility(event, table):
	if event:
		event.stopImmediatePropagation()

	if not (table and app_config.flags.can_select_multiple_rows):
		return

	if not app_config.is_paid_or_trial: #tbd redundant
		table_utils.notify_user()
		return

	rows = table.getRows()
	active_rows_len = len(table.getRows('active'))
	selected_rows = [] #[row for row in rows if row.getElement().querySelector("input[type='checkbox']").checked]
	for row in rows:
		checkbox = row.getElement().querySelector("input[type='checkbox']")
		if checkbox and checkbox.checked:
			selected_rows.append(row)

	is_hide = document['hide-btn'].text == 'Hide'
	show_hidden = document['show-hidden-checkbox'].checked

	for row in selected_rows:
		if is_hide:
			row.update({table_utils.row_visibility: False, table_utils.row_hidden: True})
			if show_hidden:
				row.getElement().style.backgroundColor = app_data.light_gray_color_main_table
				row.getElement().style.display = ''
			else:
				if not row.getElement().style.display == 'none':
					row.getElement().style.display = 'none'
					active_rows_len = active_rows_len - 1

		else:
			row.getElement().style.display = ''
			row.update({table_utils.row_visibility: True, table_utils.row_hidden: False})
			row.getElement().style.backgroundColor = app_data.white_color

	update_hide_button_text()
	table_utils.update_shown_entries(active_rows_len, table, 'csv_tsv')


def select_all_checkboxes():
	if not (table and app_config.flags.can_select_multiple_rows):
		return

	if not app_config.is_paid_or_trial: #tbd redundant
		table_utils.notify_user()
		return

	rows = table.getRows('active')  # TBD: visible?
	for row in rows:
		checkbox = row.getElement().querySelector("input[type='checkbox']")
		if checkbox:
			checkbox.checked = True
	update_hide_button_text()


def deselect_all_checkboxes():
	if not (table and app_config.flags.can_select_multiple_rows):
		return

	if not app_config.is_paid_or_trial: #tbd redundant
		table_utils.notify_user()
		return

	rows = table.getRows('active')  # TBD: visible?
	for row in rows:
		checkbox = row.getElement().querySelector("input[type='checkbox']")
		if checkbox:
			checkbox.checked = False
	update_hide_button_text()


#...............search editor code.....................

def update_main_tabulator_filter(field=None, value=None, operator=None, only_operator_changes=False):
	global current_filters
	filters = current_filters
	deeply_utils.pp('sync_utils.py -- update_main_tabulator_filter; field %s, value %s:' % (field, value))

	if only_operator_changes :
		for filter_dict in filters:
			if filter_dict['field'] == field:
				filter_dict['type'] = operator
				break
	else:
		filters = [f for f in filters if f['field'] != field]
		if value != '':
			filter_dict = {
				'field': field,
				'type': operator,
				'value': value,
				}
			filters.append(filter_dict)

	current_filters = filters

	if table:
		try:
			table.clearFilter(True)
		except Exception as e:
			print('ERROR in update_main_tabulator_filter clearFilter:', e)
		table.setFilter(filters)
		update_header_filters(table)
		restore_search_vals(filters)

	# refactor TBD: more DRY e.g. value should be checked_checkbox_filter_status (converted to Boolean if needed)
	if checked_checkbox_filter_status:
		table.addFilter(checked_checkbox_filter_function, {'field': table_utils.row_checked, 'type': '=', 'value': True})
	else:
		table.addFilter(checked_checkbox_filter_function, {'field': table_utils.row_checked, 'type': '=', 'value': False})

	# refactor TBD: more DRY e.g. value should be hidden_checkbox_filter_status (converted to Boolean if needed)
	if hidden_checkbox_filter_status:
		table.addFilter(hidden_checkbox_filter_function, {'field': table_utils.row_hidden, 'type': '=', 'value': True})
	else:
		table.addFilter(hidden_checkbox_filter_function, {'field': table_utils.row_hidden, 'type': '=', 'value': False})


def restore_search_vals(filters):
	deeply_utils.pp('sync_utils.py -- restore_search_vals; filters:', filters)

	all_header_inputs = document.select(
		"#table .tabulator-header input[type='text']")
	for input_field in all_header_inputs:
		element = input_field.closest('.tabulator-col')
		if element is None:
			continue
		else:
			field_name = element.getAttribute('tabulator-field')
			for filter in filters:
				if filter['field'] == field_name:
					input_field.value = filter['value']
					break


def checked_checkbox_filter(data, filter_params, *args):
	data_value = dict(data).get(table_utils.row_checked, None)
	value_to_match = filter_params['value']
	if not value_to_match:
		return True

	return data_value == value_to_match


def checked_checkbox_filter_function(data, filter_params):
	return checked_checkbox_filter(data, filter_params)


def hidden_checkbox_filter(data, filter_params, *args):
	data_value = dict(data).get(table_utils.row_hidden, None)
	value_to_match = filter_params['value']
	if not value_to_match:
		return True

	return data_value == value_to_match


def hidden_checkbox_filter_function(data, filter_params):
	# documentation TBD: why have this function?
	return hidden_checkbox_filter(data, filter_params)


#......................................end.................................................

# sorting editor code

def header_sort_event_bind(event, column, table):
	global sorters_copy
	field = column['getField']()
	existing_row = None
	idx = -1
	for index, sorter in enumerate(sorters_copy):
		if sorter['column'] == field:
			existing_row = sorter
			idx = index
			break

	if existing_row:
		current_order = existing_row['dir']
		if current_order == 'desc':
			sorters_copy.pop(idx)
		else:
			new_order = 'desc'
			sorters_copy[idx]['dir'] = new_order
	else:
		sorters_copy.append({
			'column': field,
			'dir': 'asc'
		})

	apply_sorting_to_table(table)


def restore_sorters(sorters, table):
	sorters_copy = sorters
	apply_sorting_to_table(table)


#.............................end..................................

# open editors
# refactor to CONSIDER: move somewhere other than sync_utils.py

async def open_layout_editor(e):
	if e:
		e.stopImmediatePropagation()

	current_title = await tauri.window.getCurrentWindow().title()
	options = {
		'props': {
			'parent_label': current_window.label,
			'table_data': header_data,
		},
		'height': 440,  # TBD: avoid hardcoding specific values
		'width': 800,
	}
	layout_window_title = f'{current_title} - Layout'
	layout_window_label = f'{current_window.label}_layout_editor'
	url = '/windows/layout_dialog.html'
	await table_utils.open_window(layout_window_title, layout_window_label, url, options)


async def open_search_editor(e):
	if e:
		e.stopImmediatePropagation()

	current_title = await tauri.window.getCurrentWindow().title()
	options = {
		'props': {
			'parent_label': current_window.label,
			'header_data': header_data,
			'columns': columns,
			'current_filters': current_filters,
			'show_hidden': document['show-hidden-checkbox'].checked,
			'search_options_dropdown': search_options_dropdown,
		},
		'height': 520,
		'width': 800,
	}
	args = {
		'title': f'{current_title} - Search',
		'url': '/windows/search_dialog.html',
		'label': f'{current_window.label}_search_editor',
		'options': options
	}
	search_window_title = f'{current_title} - Search'
	search_window_label = f'{current_window.label}_search_editor',
	url = '/windows/search_dialog.html'
	# await table_utils.open_window(search_window_title, search_window_label, url, options) #wierd error
	await tauri.invoke('create_document_window', args)


async def open_sort_editor(e):
	if e:
		e.stopImmediatePropagation()

	if main_table_sorters:
		initial_sorters = main_table_sorters
	else:
		initial_sorters = []

	current_title = await tauri.window.getCurrentWindow().title()
	options = {
		'props': {
			'parent_label': current_window.label,
			'header_data': header_data,
			'initial_sorters': initial_sorters
		},
		'height': 340,
		'width': 800,
	}
	sort_window_title = f'{current_title} - Sort'
	sort_window_label = f'{current_window.label}_sort_editor'
	url = '/windows/sort_dialog.html'
	await table_utils.open_window(sort_window_title, sort_window_label, url, options)


async def open_export_editor(e):
	# globals not modified here: current_window, current_filters, main_table_sorters, platform, table, table_data
	if e:
		e.stopImmediatePropagation()

	deeply_utils.pp('open_export_editor')

	current_title = await tauri.window.getCurrentWindow().title()

	export_header_data = []
	for row in header_data:
		if dict(row)['name'] not in table_utils.built_in_fields:
			export_header_data.append(row)

	options = {
		'props': {
			'parent_label': current_window.label,
			'header_data': export_header_data,
			'current_filters': current_filters,
			'main_table_sorters': main_table_sorters,
			'total_rows': len(table.getRows()),
			'filtered_rows': len(table.getRows('active')),  # TBD: visible?
			'using_windows': platform['using_windows'],
			'using_other': platform['using_other'],
			'save_table_data': table_data,
			},
		'height': 600,
		'width': 900,
		}

	export_window_title = f'{current_title} - Export'
	export_window_label = f'{current_window.label}_export_editor'
	url = '/windows/export_dialog.html'

	await table_utils.open_window(export_window_title, export_window_label, url, options)


async def open_column_editor(e):
	# 'Define' button; was 'Edit Columns' but that wasn't obviously distinct from 'Layout'
	if e:
		e.stopImmediatePropagation()

		if not app_config.is_paid_or_trial:
			table_utils.notify_user()
			return

		options = {
			'props': {
				'parent_label': window_label,
				'header_data': json.dumps({'header_data': header_data}),
				},
			'height': 600,
			'width': 800,
		}

		column_window_title = f'{window_title} - Define'
		column_window_label = f'{window_label}_column_editor'
		url = '/windows/column_dialog.html'

		await table_utils.open_window(column_window_title, column_window_label, url, options)


async def open_import_editor(e):
	if e:
		e.stopImmediatePropagation()
		options = {
			'props': {
			'parent_label': window_label,
			'header_data': json.dumps({'header_data': header_data}),
			},
			'height': 600,
			'width': 800,
		}
		import_window_title = f'{window_title} - Import Columns'
		import_window_label = f'{window_label}_import'
		url = '/windows/import_dialog.html'
		await table_utils.open_window(import_window_title, import_window_label, url, options)


#..................................end................................................................


async def delete_selected_row():
	# globals not modified here: table
	# right now this only applies to file_data with a cache_db ==> TBD: implement for db_data

	# 3 tasks:
	# - delete from SQLite cache_db
	# - add a record to edits_db
	# - delete from tabulator

	if not app_config.is_paid_or_trial:
		table_utils.notify_user()
		return

	if not table:
		return False

	selected_rows = table.getSelectedRows()
	if not selected_rows:
		return False

	selected_row = selected_rows[0]  # there should only be 1 selected but just in case

	row_data = dict(selected_row.getData())
	db_row_id = row_data[table_utils.file_row]

	if not db_row_id:
		return False

	# remove internal fields from what we show the user
	for field in table_utils.built_in_fields:
		if field in row_data:
			del row_data[field]

	timestamp = deeply_utils.now_string()
	edits_data = [
		str(db_row_id),
		'ENTIRE ROW',
		json.dumps(row_data),
		'ROW DELETED',
		timestamp,
		edits_db.path_relative,
		]

	res = await cache_db.delete_row(db_row_id)  # delete from SQLite cache
	if res:
		await edits_db.insert_data(edits_data)
		selected_row.delete()  # delete from tabulator
		return True
	else:
		# something went wrong
		return False


# -----
# setup handlers: called by file_data, db_data, db_tables, edits, history

def setup_window_event_handlers(window, create_table_CALLBACK=None, get_table_CALLBACK=None):

	# lambda function for the callbacks
	column_order_DEBOUNCE = table_utils.debounce(
		lambda tauri_message: update_column_order(tauri_message, table, create_table_CALLBACK, get_table_CALLBACK),
		1000
	)

	column_visibility_DEBOUNCE = table_utils.debounce(update_column_visibility, 100)
	queue_column_width_DEBOUNCE = table_utils.debounce(queue_column_width, 0)
	filter_checkbox_DEBOUNCE = table_utils.debounce(filter_from_filter_dialog_checkbox, 2000)
	filter_DEBOUNCE = table_utils.debounce(filter_from_filter_dialog, 1000)
	show_hidden_DEBOUNCE = table_utils.debounce(update_show_hidden, 1000)
	sort_DEBOUNCE = table_utils.debounce(sort_from_sort_dialog, 1000)

	# listen for tauri messages that other code sends via tauri.invoke / emit
	window.listen('sort_from_editor', sort_DEBOUNCE)
	window.listen('update_column_order', column_order_DEBOUNCE)
	window.listen('update_column_visibility', column_visibility_DEBOUNCE)
	window.listen('queue_column_width', queue_column_width_DEBOUNCE)  # renamed
	window.listen('update_main_table_checkbox_search_filters', filter_checkbox_DEBOUNCE)
	window.listen('update_main_table_search_filters', filter_DEBOUNCE)
	window.listen('update_show_hidden', show_hidden_DEBOUNCE)
