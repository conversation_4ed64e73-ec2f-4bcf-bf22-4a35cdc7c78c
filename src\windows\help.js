/**
 * help.js -- ok to leave as js for now
 */

import * as makedeeply from '/common/makedeeply.js';

async function init() {
	// Get feature flags
	/**
	 * @type {import("/common/types.js").AppConfig}
	 */
	const app_config = await window.__TAURI__.core.invoke('get_app_config');
	// console.log('App config => ', appConfig);

	if (app_config.trial_info) {
		makedeeply.setDaysRemaining();
	}

	const buyButton = document.querySelector('.buy');
	const licenseInput = document.querySelector('.license-input');
	const activateBtn = document.querySelector('.activate');

	const revokeLicenseButton = document.querySelector('.revoke');
	const serialText = document.querySelector('.serial');

	if (appConfig.license) {
		serialText.textContent = `${appConfig.license.license_key}`;
	}

	buyButton.addEventListener('click', () => {
		window.__TAURI__.core.invoke('open_buy_page');
	});

	revokeLicenseButton.addEventListener('click', () => {
		window.__TAURI__.core.invoke('revoke_license');
	});

	activateBtn.addEventListener('click', async () => {
		if (licenseInput.value.length !== 36) {
			document.querySelector('.error').textContent = 'Error: license key must be exactly 36 characters.';
			return;
		}
		try {
			window.__TAURI__.core.invoke('activate_license', { license_key: licenseInput.value });
		} catch (e) {
			console.log('help.js ERROR activating license', e);
		}
	});
}

init();
