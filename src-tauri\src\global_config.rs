use anyhow::Result;
use serde::{Deserialize, Serialize};
use tauri::Manager;

use crate::{
    lemon_squeezy,
    license::{self, License},
    trial::{self, TrialInfo},
};

/// App plan
/// TRIAL: 30 days (actually 31); same features as PAID
/// PAID: Paid via Lemon Squeezy
/// FREE: After 30 days without payment
#[derive(Debug, Serialize, Deserialize, Copy, Clone, PartialEq)]
#[serde(rename_all = "UPPERCASE")]
pub enum Plan {
    Paid,
    Trial,
    Free,
}

impl Plan {
    fn create(_app: &tauri::AppHandle, trial_info: Option<TrialInfo>) -> Self {
        // create PLAN instance
        // 1. Check if we have serial in database
        // 	-> if we have license: Validate
        //		-> if valid: [PAID PLAN]
        // 2. Check if we have TRIAL date in registry / filesystem
        // 	-> if no date: [TRIAL]
        // 3. Check if trial expired
        //	-> if expired: [FREE]
        //  -> if not expired: [TRIAL]

        // allow forcing plan by passing ENV variable (only in dev mode code added)
        #[cfg(debug_assertions)]
        {
            if let Ok(mut env_plan) = std::env::var("PLAN") {
                env_plan = env_plan.to_uppercase();
                match env_plan.as_str() {
                    "FREE" => {
                        return Plan::Free;
                    }
                    "TRIAL" => {
                        return Plan::Trial;
                    }
                    "PAID" => {
                        return Plan::Paid;
                    }
                    _ => {}
                }
            }
        }
        // if trial still active return it
        if let Some(info) = trial_info {
            if info.trial_still_active() {
                return Plan::Trial;
            }
        }
        Plan::Free
    }
}

/// Global appConfig, used in UI / Rust
#[derive(Debug, Serialize, Deserialize, Clone)]
// #[serde(rename_all = "snake_case")] // already snake_case but FYI
pub struct AppConfig {
    pub app_data_dir: String,
    pub connection_string: String,  // e.g. 'sqlite:datadeeply.sqlite'
    pub flags: FeatureFlags, // defined below
    pub is_paid_or_trial: bool,  // derived from 'plan'
    pub license: Option<License>,
    pub plan: Plan, // PAID, TRIAL, FREE
    pub platform: String, // macos, windows, linux
    pub platform_class: String, // macOS, windowsOS, linuxOS
    pub product_id: String, // e.g. datadeeply; used in connection_string and suffix for some files
    pub product_name: String, // e.g. Data Deeply
    pub trial_info: Option<TrialInfo>,
}

pub fn get_os() -> &'static str {
    // returns macos, windows, linux
    std::env::consts::OS
}

fn get_platform_class(platform: &str) -> String {
    match platform {
        "macos" => "macOS".to_string(),
        "windows" => "windowsOS".to_string(),
        "linux"   => "linuxOS".to_string(),
        other     => other.to_string(),
    }
}

pub fn get_app_data_dir(app_handle: &tauri::AppHandle) -> String {
    app_handle
        .path()
        .app_data_dir()
        .expect("unable to resolve app_data_dir")
        .to_string_lossy()
        .into_owned()
}

impl AppConfig {
    pub fn try_create(app: &tauri::AppHandle) -> Result<Self> {
        let app_data_dir = get_app_data_dir(app);
        let plan: Plan;
		let platform = get_os().to_string();
		let platform_class = get_platform_class(&platform);

		// these have to be done in order, NOT alphabetical
		let product_name = app.config().product_name.clone().unwrap();
		let product_id = product_name.to_lowercase().replace(" ", "");
		let connection_string = format!("sqlite:{}.sqlite", product_id);

        let mut trial_info: Option<TrialInfo> = None;

        if let Ok(license) = license::load(app) {
            if let Ok(mut client) = lemon_squeezy::LemonSqueezy::try_create() {
                let result = tauri::async_runtime::block_on(
                    client.validate(&license.license_key, &license.instance_id),
                );
                match result {
                    Ok(_) => {
                        plan = Plan::Paid;
                        log::info!("Plan is PAID!");
                        return Ok(AppConfig {
							app_data_dir,
							connection_string: connection_string.clone(),
							flags: FeatureFlags::default(),
							is_paid_or_trial: true,  // always true for paid
							license: Some(license),
                            plan,
							platform,
							platform_class,
							product_id: product_id.clone(),
							product_name: product_name.clone(),
                            trial_info: None,
                        });
                    }
                    Err(e) => {
                        log::error!("{}", e);
                    }
                }
            }
        }
        match trial::get_info(app) {
            Ok(info_option) => {
                match info_option {
                    Some(info) => {
                        plan = Plan::create(app, Some(info.clone()));
                        trial_info = Some(info);
                    }
                    None => {
                        // time to activate
                        match trial::activate(app) {
                            Ok(info) => {
                                plan = Plan::create(app, Some(info.clone()));
                                trial_info = Some(info);
                            }
                            Err(e) => {
                                log::error!("cant activate trial {}", e);
                                plan = Plan::create(app, None);
                            }
                        }
                    }
                }
            }
            Err(e) => {
                log::error!("cant get trial info {}", e);
                plan = Plan::create(app, None);
            }
        }
        Ok(AppConfig {
			app_data_dir,
			connection_string,
			flags: FeatureFlags::default(),
			is_paid_or_trial: plan != Plan::Free,
			license: None,
            plan,
			platform,
			platform_class,
			product_id,
			product_name,
            trial_info,
        })
    }
}

/// Feature Flags based on plan
/// Used in UI (Brython) / Rust
#[derive(Debug, Serialize, Deserialize, Copy, Clone)]
pub struct FeatureFlags {
	pub can_handle_html: bool,  // probably v1.1
	pub can_handle_json: bool,  // probably v1.1

	pub can_import_files: bool,  // maybe v1.1
    pub can_open_sqlite: bool,
	pub can_select_multiple_rows: bool,  // implies CAN_HIDE_ROWS

	pub can_view_edits_table: bool,
	pub can_view_settings: bool,
}

impl Default for FeatureFlags {
    fn default() -> Self {
        Self {
			can_handle_html: false,
			can_handle_json: false,

			can_import_files: false,
			can_open_sqlite: true, // temp for debugging
			can_select_multiple_rows: false, // implies CAN_HIDE_ROWS since we will turn on together

			can_view_edits_table: true, // temp for debugging
			can_view_settings: false,
        }
    }
}
