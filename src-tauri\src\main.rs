// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use clap::Parser;
mod cmd;
mod config;
mod customize;
mod database;
mod date;
mod edit1;  // Edit Deeply TBD: what's the easiest way to maintain this difference?
mod global_config;
mod http_api;
mod lemon_squeezy;
mod license;
mod setup;
mod trial;
mod utils;
mod window;
use cmd::{common, file, payment, settings};
use edit1::editor;  // Edit Deeply TBD: what's the easiest way to maintain this difference?

use tauri_plugin_window_state::{self, StateFlags};

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
pub struct Args {
    /// Optional path to database
    #[arg(short, long)]
    db_path: Option<String>,
}

fn main() {
    let args = Args::parse();
    env_logger::try_init().unwrap();

    let window_state_plugin = tauri_plugin_window_state::Builder::default()
        .with_denylist(config::WINDOW_STATE_DENYLIST)
        .with_filename(config::WINDOW_STATE_FILENAME)
        .with_state_flags(!StateFlags::DECORATIONS)
        .build();

	tauri::Builder::default()
		.plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_process::init())
        .plugin(tauri_plugin_store::Builder::new().build())
        // ^^^ TBD: what's the easiest way to maintain this difference?
		.setup(|app| setup::setup(app, args))
		.plugin(window_state_plugin)
		.plugin(tauri_plugin_sql::Builder::default().build())
		.plugin(tauri_plugin_dialog::init())
		.plugin(tauri_plugin_fs::init())
		.invoke_handler(tauri::generate_handler![
			settings::open_assets_folder,
			settings::reload_app,
			cmd::window::create_document_window,
			cmd::window::create_known_window,
			cmd::window::create_window,
			common::get_api_port,
			common::get_app_config,
			common::get_product_name,
			common::get_product_id,
			common::close_window,
			common::is_mac_os,
			common::get_os,
			common::test,
			common::get_home_dir,
			payment::open_buy_page,
			payment::revoke_license,
			payment::get_checkout_url,
			file::column_order,
			file::column_visibility,
			file::column_width_updated,
			file::queue_column_width,  // renamed
			file::columns_changed,
			file::create_folder,
			// file::emit_to_window,
			file::exists,
			file::get_active_files_path,
			file::get_active_tables,
			file::get_file_mod_date,
			file::get_history,
			file::get_open_windows,
			file::main_table_column_moved,
			file::merge_data,
			file::open_file,
			file::open_new_row_window_file,
			file::open_new_row_window,
			file::open_row_details,
			file::open_table,
			file::pivot_row_to_edit,
			file::read_text_file,
			file::rename_file,
			file::save_file,
			file::sort_from_main_table,
			file::sort_main_table,
			file::sqlite_exists_with_data,
			file::sqlite_select,
			file::update_layout_columns,
			file::update_main_table_search_checkboxes,
			file::update_main_table_search,
			file::update_main_table_show_hidden,
			file::update_search_column_order,
			file::update_search_columns,
			file::update_search_editor_checkbox,
			file::update_search_editor_data,
			file::update_search_hidden,
			file::update_sort_column_order,
			file::update_table_columns,
			file::update_table,
			cmd::database::get_database_url,
			// Edit Deeply TBD: what's the easiest way to maintain this difference?
			editor::editor_save_file,
            editor::open_files,
            editor::open_data_files,
            editor::save_as_file,
            edit1::open_finder,
            edit1::open_tabulater,
            edit1::open_matchignore,
            edit1::check_files_status,
            edit1::get_file_folder,
            edit1::search_in_files,
            edit1::search_in_folders,
            edit1::get_files_in_folders,
            edit1::replace_value,
            edit1::replace_values,
            edit1::open_matchignore,
            edit1::get_review_data,
            edit1::filter_valid_paths,
            edit1::run_new_editor,
            edit1::set_active_window,
            edit1::get_window_title_by_path,
			edit1::autosave_file,
			edit1::update_iclose_flag,
			edit1::delete_autosave,
			edit1::get_file_autosave,
		])
		.run(tauri::generate_context!())
		.expect("error while running tauri application");
}
