// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use clap::Parser;
mod cmd;
mod config;
mod customize;
mod database;
mod date;
mod global_config;
mod http_api;
mod lemon_squeezy;
mod license;
mod setup;
mod trial;
mod utils;
mod window;
use cmd::{common, file, payment, settings};
use tauri_plugin_window_state::{self, StateFlags};

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
pub struct Args {
	/// Optional path to database
	#[arg(short, long)]
	db_path: Option<String>,
}

fn main() {
	let args = Args::parse();
	env_logger::try_init().unwrap();

	let window_state_plugin = tauri_plugin_window_state::Builder::default()
		.with_denylist(config::WINDOW_STATE_DENYLIST)
		.with_filename(config::WINDOW_STATE_FILENAME)
		.with_state_flags(!StateFlags::DECORATIONS)
		.build();

	tauri::Builder::default()
		.setup(|app| setup::setup(app, args))
		.plugin(window_state_plugin)
		.plugin(tauri_plugin_sql::Builder::default().build())
		.plugin(tauri_plugin_dialog::init())
		.plugin(tauri_plugin_fs::init())
		.invoke_handler(tauri::generate_handler![
			settings::open_assets_folder,
			settings::reload_app,
			cmd::window::create_document_window,
			cmd::window::create_known_window,
			cmd::window::create_window,
			common::get_api_port,
			common::get_app_config,
			common::get_product_name,
			common::get_product_id,
			common::close_window,
			common::is_mac_os,
			common::get_os,
			common::test,
			common::get_home_dir,
			payment::open_buy_page,
			payment::revoke_license,
			payment::get_checkout_url,
			file::column_order,
			file::column_visibility,
			file::column_width_updated,
			file::queue_column_width,  // renamed
			file::columns_changed,
			file::create_folder,
			// file::emit_to_window,
			file::exists,
			file::get_active_files_path,
			file::get_active_tables,
			file::get_file_mod_date,
			file::get_history,
			file::get_open_windows,
			file::main_table_column_moved,
			file::merge_data,
			file::open_file,
			file::open_new_row_window_file,
			file::open_new_row_window,
			file::open_row_details,
			file::open_table,
			file::pivot_row_to_edit,
			file::read_text_file,
			file::rename_file,
			file::save_file,
			file::sort_from_main_table,
			file::sort_main_table,
			file::sqlite_exists_with_data,
			file::sqlite_select,
			file::update_layout_columns,
			file::update_main_table_search_checkboxes,
			file::update_main_table_search,
			file::update_main_table_show_hidden,
			file::update_search_column_order,
			file::update_search_columns,
			file::update_search_editor_checkbox,
			file::update_search_editor_data,
			file::update_search_hidden,
			file::update_sort_column_order,
			file::update_table_columns,
			file::update_table,
			cmd::database::get_database_url,
		])
		.run(tauri::generate_context!())
		.expect("error while running tauri application");
}
