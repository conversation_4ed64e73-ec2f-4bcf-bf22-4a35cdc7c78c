use serde::{Deserialize, Serialize};
use std::io::Read;
use std::path::{Path, PathBuf};
use std::{env, fs::File};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct MyFile {
    pub path: String,
    pub ext: String,
    pub content: String,
}
impl MyFile {
    pub fn read_file_data(path: PathBuf) -> Result<MyFile, String> {
        let path_str = path.to_string_lossy().to_string();
        let extension = path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_string();

        let mut file = File::open(&path).map_err(|e| format!("Failed to open file: {:?}", e))?;
        let mut content = String::new();
        file.read_to_string(&mut content)
            .map_err(|e| format!("Failed to read file: {:?}", e))?;

        Ok(MyFile {
            path: path_str,
            ext: extension,
            content,
        })
    }
}

pub fn home_dir() -> PathBuf {
    #[cfg(windows)]
    {
        env::var("USERPROFILE").unwrap_or_default().into()
    }

    #[cfg(unix)]
    {
        env::var("HOME").unwrap_or_default().into()
    }
}

#[tauri::command]
pub fn get_file_folder(path: String) -> String {
    let mut xpath = path.replace(home_dir().to_str().unwrap(), "~");
    if let Some(first_char) = xpath.chars().next() {
        if first_char != '~' {
            if first_char.to_string() != std::path::MAIN_SEPARATOR_STR {
                xpath = format!("{}{}", std::path::MAIN_SEPARATOR_STR, xpath);
            }
            xpath = format!("{}{}", "~", xpath);
        }
    }
    let path_buf = PathBuf::from(xpath.clone());
    let folder = path_buf.parent().unwrap_or(Path::new(""));

    folder.display().to_string() + std::path::MAIN_SEPARATOR_STR
}
