"""
deeply_utils.py - likely useful across multiple projects
"""

from browser import window
from collections.abc import Iterable
from datetime import datetime, timedelta, timezone
import decimal
import locale
import math
import os
import re
import sys
import time


DATE_FORMAT = '%Y-%m-%d %H:%M:%S %z'  # local with offset

# -----
# date utils


def has_timezone_offset(timestamp_string):
	"""
	Checks if a date string ends with a timezone offset like -0400 or -04:00
	Returns True if it does, False otherwise
	"""

	# Regex pattern for timezone offsets at the end of a string
	tz_pattern = r'[-+]\d{2}(?::?\d{2})?$'

	return bool(re.search(tz_pattern, timestamp_string.strip()))


def parse_to_utc(timestamp_string):
	"""
	Work around an apparent bug in Brython (Courtesy of Claude 3.7 Sonnet)
	MY REPORT: https://github.com/brython-dev/brython/issues/2577

	Parse a datetime string with timezone offset and convert to UTC
	Format example: '2025-05-16 07:15:11 -0400'
	"""
	# Split the string into datetime part and timezone part
	parts = timestamp_string.rsplit(' ', 1)

	if len(parts) != 2:
		# No timezone info, assume UTC
		return datetime.strptime(timestamp_string, '%Y-%m-%d %H:%M:%S')

	dt_part, tz_part = parts

	# Parse the datetime part
	dt = datetime.strptime(dt_part, '%Y-%m-%d %H:%M:%S')

	# Handle timezone offset
	if tz_part and (tz_part.startswith('+') or tz_part.startswith('-')):
		# Remove colon if present (handle both '-0400' and '-04:00' formats)
		tz_part = tz_part.replace(':', '')

		# Calculate offset
		sign = -1 if tz_part[0] == '-' else 1
		hours = int(tz_part[1:3])
		minutes = int(tz_part[3:5]) if len(tz_part) >= 5 else 0

		# Calculate total offset minutes
		offset_minutes = sign * (hours * 60 + minutes)

		# Apply the offset to convert to UTC
		# Subtract because we're converting to UTC
		dt = dt - timedelta(minutes=offset_minutes)

	return dt


def date_from_string(timestamp_string):
	timestamp_string = timestamp_string.strip()

	if not timestamp_string:
		return datetime.min

	if has_timezone_offset(timestamp_string):
		return parse_to_utc(timestamp_string)
	# else: fall thru to check multiple formats

	if timestamp_string.endswith('Z'):  # handle trailing “Z” for UTC
		timestamp_string = timestamp_string[:-1] + '+0000'

	format_strings = [
		DATE_FORMAT,  # 2025-05-16: fails in Brython but keep for the future
		'%Y-%m-%dT%H:%M:%S%z',   # parses ISO-8601 UTC, e.g. 2025-05-15T17:55:32Z or +00:00
		'%Y-%m-%dT%H:%M:%S.%f%z',
		'%Y-%m-%d %H:%M:%S',
		'%m/%d/%Y %I:%M:%S %p',
		'%m/%d/%Y %H:%M:%S',
	]

	for format_string in format_strings:
		try:
			return datetime.strptime(timestamp_string, format_string)
		except ValueError as e:
			# print('TEMP ERROR: "%s" not parsed by "%s"; error %s' % (timestamp_string, format_string, e))
			continue

	# if it gets here
	raise ValueError(f'Unrecognized date format in "{timestamp_string}"')


def date_to_string(when=None):
	if when is None:
		when = datetime.now().astimezone()
	elif when.tzinfo is None:
		when = when.replace(tzinfo=timezone.utc).astimezone()

	return when.strftime(DATE_FORMAT)


def now_string(as_filestamp=False):
	date_string = date_to_string()  # now() if no param

	if as_filestamp:
		return date_string.replace(':', ';')
	else:
		return date_string


# failed due to Brython bug; they fixed it but I don't update Brython right now
# def now_string():
# 	date_format = '%Y-%m-%d %H:%M:%S %z'  # local time for readability; %z for offset so the absolute time is clear
# 	return datetime.now().strftime(date_format)


def to_date_with_offset(value):
	dt_naive = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
	offset_seconds = -time.timezone
	if time.daylight and time.localtime().tm_isdst:
		offset_seconds = -time.altzone

	local_offset = timezone(timedelta(seconds=offset_seconds))
	dt_with_offset = dt_naive.replace(tzinfo=local_offset)

	formatted = dt_with_offset.strftime("%Y-%m-%d %H:%M:%S %z")

	return formatted


# -----
# general utils

def get_value(obj, prefix=''):
	'''Convert object to string representation -- not easy for js objects ==> print directly'''

	if str(type(obj)).startswith('<Javascript') or str(obj).startswith('<Javascript'):
		try:
			window.console.log(prefix + '[JS]', obj)
		except Exception as e:
			print(prefix + str(obj))

		return ''

	elif isinstance(obj, str):
		return f'"{obj}"'

	else:
		return str(obj)


def smart_print(obj, indent=0):
	'''
	- Brython's print can print some things but not all
	- window.console.log can print some things but not all
	- useful to expand some objects to see details (though leaving short lists on 1 line would be better)
	'''
	prefix = '\t' * indent

	if isinstance(obj, (list, tuple)):
		# Always try to print lists on one line if they contain simple values
		try:
			values = ', '.join(get_value(item, prefix) for item in obj)
			print(f'{prefix}{type(obj).__name__}[{len(obj)}]: [{values}]')
		except:
			# If that fails, fall back to multi-line
			print(f'{prefix}{type(obj).__name__}[{len(obj)}]:')
			for i, item in enumerate(obj):
				smart_print(item, indent + 1)

	elif isinstance(obj, dict):
		print(f'{prefix}dict[{len(obj)}]:')
		for key, value in obj.items():
			# Keep key: value on same line
			print(f'{prefix}\t{key}: {get_value(value, prefix)}')

	else:
		print(f'{prefix}{obj}')


def pp(*args):
	"""
	pp = pretty print -- not actually pretty but a short name is useful

	A wrapper around print() that can be easily turned on/off via the DEBUG flag.

	Rationale: the Python logging module has different semantics (a single string then any number of params) ==> can't do a blind replace all of print() to log()
		(caveat: the logging module is faster when not debugging: it only formats the string as needed)

	Args:
		*args: Positional arguments passed to print()
	"""

	DEBUG = True  # hack: hardcoded for now

	if DEBUG:
		# Brython's print won't deconstruct JS objects ==> use console.log
		# and console.log doesn't handle Python dict with embedded callback
# 		try:
# 			window.console.log('\n_' * 2, *args)
# 		except Exception as e:
# 			print('\n_' * 2, *args)

		indent = 0  # the first item is often a 'header'
		for arg in args:
			smart_print(arg, indent)
			indent = 1  # every subsequent item (if any)


def is_nan(val):
	if math.isnan(val):
		return 0
	else:
		return val

def format_number(number):
	# a locale-sensitive version of add_commas()

	# return locale.format_string('%g', number, grouping=True)  # should work but doesn't
	return '{:,}'.format(number)


def is_mixed_case(s):
	# this intentionally ignores non-letters; they don't affect the issue either way
	# i.e. this checks 'does this have at least 1 lower and at least 1 upper'
	# NOT 'is this only alphabetic with at least 1 of each'

	has_lower = False
	has_upper = False

	for c in s:
		if c.islower():
			has_lower = True
		elif c.isupper():
			has_upper = True

		if has_lower and has_upper:
			return True  # return as soon as clear

	# if it gets here
	return False


def remove_plural_suffix(word: str) -> str:
	if word.endswith('ies'):
		return word[:-3] + 'y'
	elif word.endswith('es') and word[-3] in 'sxz' or word[-4:-2] in ['ch', 'sh']:
		return word[:-2]
	elif word.endswith('s') and not word.endswith('ss'):
		return word[:-1]
	return word


### might be useful but NOT IN USE
###
# def plural_to_singular(name: str) -> str:
# 	def convert_part(part: str) -> str:
# 		if part.endswith('ies'):
# 			return part[:-3] + 'y'
# 		elif part.endswith('s'):
# 			return part[:-1]
# 		return part
#
# 	if '_' in name:  # Snake case
# 		parts = name.split('_')
# 		singular_parts = [convert_part(part) for part in parts]
# 		return '_'.join(singular_parts)
# 	elif ' ' in name:  # Space-separated case
# 		parts = name.split(' ')
# 		singular_parts = [convert_part(part) for part in parts]
# 		return ' '.join(singular_parts)
# 	else:  # Title case
# 		parts = re.findall(r'[A-Z]?[a-z]+|[A-Z]+(?=[A-Z]|$)', name)
# 		singular_parts = [convert_part(part) for part in parts]
# 		return ''.join(singular_parts)


### might be useful but NOT IN USE
###
# def random_string(length):
# 	characters = string.ascii_letters
# 	random_str = ''.join(random.choice(characters) for _ in range(length))
# 	return random_str


# -----
# misc from simple_utils3.py (MIT License)

def add_commas(int_float_string, pad_to=None, pad_char=' ', decimals=1, prefix='', suffix='', hide_zero=False, halt_on_error=False):
	value = int_float_string  # unless changed below

	if isinstance(int_float_string, str):  # str or unicode
		if ',' in int_float_string:
			# assume the commas are correct
			return prefix + int_float_string + suffix

		# TBD: localize for Europe (comma as period)
		try:
			if '.' in int_float_string:
				value = float(int_float_string)
			else:
				value = int(round(float(int_float_string)))  # round to avoid floor/truncate
		except ValueError:
			if halt_on_error:
				raise Exception('add_commas: could not convert "%s" to float and then int' % int_float_string)  # SimpleError
			else:
				return int_float_string

	if isinstance(value, int):   # Python 3: includes the old 'long'
		if hide_zero and (value == 0):
			return ''

		s = locale.format_string('%d', value, grouping=True)

		if len(s) > 3:
			# not sure why this stopped working
			# s = '{:,}'.format(value)  # requires Python 2.7
			assert ',' in s, 'oops, locale.format did NOT add a comma to "%s"' % s

	elif isinstance(value, (float, decimal.Decimal)):
		if hide_zero and (value == 0.0):
			return ''

		if decimals == 0:
			s = locale.format_string('%0.0f', value, grouping=True)  # grouping=True, i.e. add commas
		elif decimals == 1:
			s = locale.format_string('%0.1f', value, grouping=True)  # grouping=True, i.e. add commas
		elif decimals == 2:
			s = locale.format_string('%0.2f', value, grouping=True)  # grouping=True, i.e. add commas
		else:
			raise Exception('figure out a better solution than my copy/paste hack!')  # SimpleError
	else:
		raise Exception('expect int or float or string, not %s: %s' % (type(int_float_string), int_float_string))  # SimpleError

	if pad_to:
		return prefix + pad(s, pad_to, pad_char) + suffix
	else:
		return prefix + s + suffix


def add_suffix(path, suffix='', extension='', folder=None, prefix='', verbose=True, debug=False):
	# add suffix (before the extension) and/or change the extension and/or an optional prefix
	# for deeply_utils: I removed rename_if_exists=False
	if is_list(path):
		path_char = os.path.sep

		path = path_char.join([item.rstrip(path_char) for item in path])
		if debug:
			print('expanded path from list to string', path)

	if folder:
		path = os.path.join(folder, path)
		if debug:
			print('added folder to path, yielding:', path)

	check_extension(extension)  # SimpleFile

	if prefix or suffix or extension:
		# usual case!
		if suffix is None:
			suffix = ''  # just in case
		if extension is None:
			extension = ''	# just in case
		if prefix is None:
			prefix = ''  # just in case

		folder_path, basename = os.path.split(path)
		core_name, old_extension = os.path.splitext(basename)

		if not extension:
			extension = old_extension

		if folder_path:
			# the usual case, but may want to call add_suffix just for a filename not full path
			folder_path += os.path.sep
		path = ''.join([folder_path, prefix, core_name, suffix, extension])  # was SimpleList

# 	if rename_if_exists:
# 		SimpleFile.rename_if_exists(path, verbose=verbose)

	return path


def check_extension(extension):
	# note that Rust 'ext' does NOT include the leading period ==> I call that file_type
	if extension:
		assert extension.startswith('.'), 'including the leading period!'


def get_extension(filename):   # SimpleFile
	# includes the leading '.'
	# caveat: if called for url, may need additional re.split('[#?&]', extension)  # drop URL params and local references
	return os.path.splitext(os.path.basename(filename))[1]   # drop the basename


def get_file_type(filename):
	# for Rust: extension WITHOUT the leading '.'
	return get_extension(filename).lstrip('.')


def drop_extension(filename, keep_folder=False):   # SimpleFile
	# alternate name: corename ... or rootname ... i.e. even more 'basic' than 'base'
	# by default: ALSO drops the leading folder, if any
	# easier and more self-documenting than calling os.path with [0]

	if keep_folder:
		first_part = filename
	else:
		first_part = os.path.basename(filename)

	return os.path.splitext(first_part)[0]   # drop the extension


def is_list(something):
	# a/k/a is_list_like, is_like_list, looks_like_list

	# CAVEAT: updated to include set, e.g. matching more recent is_list_of_int
	# ... rationale: I generally want to iterate without regard to order; a set is fine for that
	# ... could use is_iterable but that's less natural, e.g. covers dict where keys/values/items is ambiguous

	# in Python 2, Misc.is_list(something) sufficed ... but Python 3's dict_keys gets in the way
	# hack dict_keys type; could use collections.abc.KeysView
	return isinstance(something, (list, tuple, set, type({}.keys()), type({}.values()), type({}.items())))


def looks_like_int(s, allow_comma=True, verbose=False):
	if isinstance(s, int):
		return True

	if s is None:
		return False

	s_str = str(s)
	if not s_str:
		return False

	if isinstance(s, Iterable) and not isinstance(s, (str, bytes)):
		if verbose:
			print(type(s), 'is iterable so NOT an int')
		return False

	if not isinstance(s, str):
		if verbose:
			print('convert', type(s), 'to str before processing')
		s = s_str

	s = s.strip()
	if s.isdigit() and not s.startswith('0'):
		return True

	if allow_comma:
		s = re.sub(r',(?=\d{3})', '', s)

	return bool(re.fullmatch(r'-?[1-9]\d*', s))


def pad(string_or_int, places=4, pad_char=None, pad_left=True, delim='', verbose=False):
	# pad left or right, with various options
	s = SimpleString.to_unicode(string_or_int)
	if len(s) >= places:
		return s

	if not pad_char:
		if type(string_or_int) is int:  # convert to isinstance?
			pad_char = '0'
		else:
			pad_char = ' '

	if type(places) != int:
		if type(pad_char) == int:
			raise SimpleError('you seem to have swapped places with pad_char')
		else:
			raise SimpleError('places %s must be an int, not %s' % (places, type(places)))
	if len(pad_char) > 1 or len(delim) > 0:
		# rjust/ljust only accept a single char, but a string is easy
		# a generator would be more elegant
		# for now just generate a string that's guaranteed to be long enough
		pad_string = (places * pad_char)[0:places - len(s)]

		if pad_left:	# i.e. "justify" right
			new_s = pad_string + delim + s
		else:
			new_s = s + delim + pad_string
	else:
		# usual case: standard Python routine suffices
		if pad_left:	# i.e. "justify" right
			new_s = s.rjust(places, pad_char)
		else:
			new_s = s.ljust(places, pad_char)

	if verbose:
		print(new_s)

	return new_s


def get_length(item):
	if not item:
		# workaround for len(None) throwing an error
		return 0

	try:
		return len(item)
	except Exception as e:
		# bool/int/float don't have 'length'; this should suffice
		return len(str(item))


def length(s, pad_to=None, pad_char=' ', units='', hide_zero=False, add_commas_to_value=False):
	# as a string for join ... updated (later) to set add_commas FALSE and to use my earlier get_length

	if s is None:
		# Python throws an error on len(None); that's rarely what I want
		if hide_zero:
			return ''
		else:
			return 0

	value = get_length(s)   # important for bool/int/float

	if add_commas_to_value:
		return add_commas(value, pad_to=pad_to, pad_char=pad_char, suffix=units, hide_zero=hide_zero)
	else:
		return value


def pretty_print_dict(d, indent=0):
	# ChatGPT
	count = len(d)
	print(' ' * indent + f'dict ({count} items):')
	for key, value in d.items():
		if isinstance(value, dict):
			print(' ' * (indent + 4) + f'{key}:')
			pretty_print(value, indent + 8)
		else:
			print(' ' * (indent + 4) + f'{key}: {value}')


def pretty_print(list_or_dict_or_item, label='', is_recurse=False, print_to_stdout=True, include_count=True, sort_by_count=True, sort_by_value=False,
	blank_line_before=False, places=7, add_commas_to_value=False, exit=False):

	# simple_utils3.py; MIT License

	# usually prints directly; does NOT return a value except in the case of a single object with print_to_stdout set to False
	# sort_by_count is mostly for a dict which contains objects such as lists or other dicts
	# sort_by_value is useful when the dict contains numeric values ... perhaps counts

	if print_to_stdout and not is_recurse:
		# start with a label
		print()
		if label:
			print(label)
		if include_count and (list_or_dict_or_item is not None):
			print(length(list_or_dict_or_item, add_commas_to_value=True), 'items in the', type(list_or_dict_or_item))
		print()

	# -----
	if list_or_dict_or_item is None:
		print('None')
		# fall thru to optional exit

	elif is_list(list_or_dict_or_item):
		for item in list_or_dict_or_item:
			# recurse, e.g. for list of dictionaries
			pretty_print(item, is_recurse=True, print_to_stdout=print_to_stdout, include_count=include_count, sort_by_count=sort_by_count,
				sort_by_value=sort_by_value, blank_line_before=blank_line_before, add_commas_to_value=add_commas_to_value)

	elif isinstance(list_or_dict_or_item, dict):
		pretty_print_dict(list_or_dict_or_item)
		'''
		# code for SimpleDict.pretty_print
		, print_to_stdout=print_to_stdout, include_count=include_count, sort_by_count=sort_by_count,
				sort_by_value=sort_by_value, blank_line_before=blank_line_before, places=places, add_commas_to_value=add_commas_to_value)
		'''

	elif print_to_stdout:
		# string, float, int, etc.
		if add_commas_to_value and looks_like_int(list_or_dict_or_item):
			print(add_commas(list_or_dict_or_item))
		else:
			print(list_or_dict_or_item)

	elif not exit:
		return list_or_dict_or_item

	# -----
	if exit:
		sys.exit('DEBUG: exit after pretty_print %s' % label)


def pluralize(singular, how_many, include_zero=True):
	# different param order: put the word first since that's the focus here
	# CONSIDER: look for a more robust version

	if how_many == 1:
		return singular
	elif how_many == 0 and not include_zero:
		return ''
	elif singular.endswith('y') and singular not in ('boy', 'toy'):  # this is an ugly hack!!!
		return '%sies' % singular[:-1]
	elif singular in ('punctuation', 'whitespace'):  # for describe_differences
		return singular
	elif singular in ['dash']:
		# pluralize with 'es'
		return '%ses' % singular
	else:
		# including zero!
		# simplistic rule for English: pluralize by adding 's'
		return '%ss' % singular


def plural_phrase(how_many, singular, include_zero=True):
	# param order based on the output: f'{how_many} {singular_or_plural}'
	# also adds commas to how_many if >999
	# CONSIDER: look for a more robust version

	if how_many == 0 and not include_zero:
		return ''

	# param order is different
	plural_if_needed = pluralize(singular, how_many, include_zero=include_zero)

	return '%s %s' % (add_commas(how_many), plural_if_needed)
