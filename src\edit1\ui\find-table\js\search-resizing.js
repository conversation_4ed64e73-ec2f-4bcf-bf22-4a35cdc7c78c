import { searchEl } from './elements';

document.addEventListener('DOMContentLoaded', function () {
	const control = document.getElementById('resizer');
	let isDragging = false;
	let startY;
	let startHeight;

	control.addEventListener('mousedown', startDragging);

	function startDragging(e) {
		isDragging = true;
		startY = e.clientY;
		startHeight = parseInt(window.getComputedStyle(searchEl).height);
		//console.log('Start dragging - startHeight:', startHeight);

		document.addEventListener('mousemove', drag);
		document.addEventListener('mouseup', stopDragging);
	}

	function drag(e) {
		if (!isDragging) return;
		const deltaY = e.clientY - startY;
		const newHeight = startHeight + deltaY;
		//console.log('dragging - Height:', newHeight);
		const container = searchEl.querySelector('.input-container');
		if (newHeight >= 50) {
			if (newHeight >= 105) {
				container.classList.remove('row');
			} else {
				container.classList.add('row');
			}
			searchEl.style.height = newHeight + 'px';
		} else {
		}
	}

	function stopDragging() {
		isDragging = false;
		//console.log('Stop dragging - Final Top Section Height:', parseInt(window.getComputedStyle(searchEl).height));

		document.removeEventListener('mousemove', drag);
		document.removeEventListener('mouseup', stopDragging);
	}
});

// findReplaceExpand.addEventListener('change', () => {
// 	if (findReplaceExpand.chacked) {
// 		let newH = parseInt(window.getComputedStyle(searchEl).height) - 125;
// 		searchEl.style.height = newH >= 100 ? newH + 'px' : 'auto';
// 	}
// });
