<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Buy</title>
	</head>
	<body id="buy-page">
		<script src="https://app.lemonsqueezy.com/js/lemon.js" defer></script>

		<!-- 'module' required for await invoke -->
		<script type="module">
			import { invoke } from '@tauri-apps/api';

			window.addEventListener('DOMContentLoaded', () => {
				const buyBtn = document.querySelector('.buy');

				buyBtn.addEventListener('click', async () => {
					try {
						const url = await invoke('get_checkout_url');
						console.log('fetching url', url);
						window.createLemonSqueezy();
						LemonSqueezy.Setup({
							eventHandler(event) { console.log(event); }
						});
						LemonSqueezy.Url.Open(url);
					} catch (e) {
						console.error('failed to get checkout url', e);
					}
				});
			});
		</script>
	</body>
</html>
