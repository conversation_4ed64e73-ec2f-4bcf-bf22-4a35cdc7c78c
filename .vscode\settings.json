{
	"editor.insertSpaces": false,
	"editor.formatOnSave": true,
	"editor.codeActionsOnSave": {
		"source.organizeImports": "explicit",
		"source.fixAll": "explicit"
	},
	"rust-analyzer.check.command": "clippy",
	"rust-analyzer.checkOnSave": true,

	"prettier.ignorePath": ".gitignore",
	"prettier.configPath": ".prettierrc.json",
	"javascript.preferences.importModuleSpecifierEnding": "js",
	"python.analysis.extraPaths": ["./src/common"], // Provide custom autocomplete
	"python.analysis.ignore": [
		"*" // <PERSON><PERSON><PERSON> does not have types
	]
}
