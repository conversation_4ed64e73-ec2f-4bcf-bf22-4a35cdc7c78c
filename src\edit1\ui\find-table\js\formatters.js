import { replaceInstance } from '../find-table';

export var rightTabIcon = function (cell, formatterParams) {
	return "<img src='../../../assets/right-tab-arrow.svg' style='cursor:pointer' alt='right tab' width='24px' height='24px'/>";
};

export var replaceBtnFormat = function (cell, formatterParams) {
	const button = document.createElement('button');
	button.setAttribute('type', 'button');
	button.style.cursor = 'pointer';
	button.classList.add('replace-btn');
	button.classList.add('small');
	button.textContent = 'Replace';
	button.addEventListener('click', async (e) => await replaceInstance(e, cell));
	return button;
};

export var fileNameGroupHeader = function (value, count, data, group) {
	//value - the value all members of this group share
	//count - the number of rows in this group
	//data - an array of all the row data objects in this group
	//group - the group component for the group
	// console.log('Groupe', group, 'first data', data[0]);
	return `<span id="${value}-group-header" class="${data[0].ignored ? 'ignored-group' : ''}"  style="overflow:none">${count} in ${
		data[0].file_name
	} ${data[0].file_folder != 'unsaved' ? `- ${data[0].parent_folder}` : ''}</span> `;
};
