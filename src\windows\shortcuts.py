"""
shortcuts.py - included in every html page (app window) to provide keyboard shortcuts for Windows/Linux
	AFAIK macOS handles them via menubar
	(TBD: test whether something similar can be done for Windows and/or Linux)
"""

from browser import window, aio

import table_utils
import open_file

async def on_shortcut(event):
	shortcut_action = table_utils.get_shortcut_action(event)
	if shortcut_action == 'open':
		await open_file.open_file(from_menu=True)

	# CONSIDER: maybe handle Cmd-N here
	# - db_data.py & file_data.py: new record/row -- see existing on_shortcut
	# - elsewhere: new window / document ?

# CONSIDER: should this be on keyup instead?
window.bind('keydown', lambda e: ((e.stopImmediatePropagation(), aio.run(on_shortcut(e)))))
