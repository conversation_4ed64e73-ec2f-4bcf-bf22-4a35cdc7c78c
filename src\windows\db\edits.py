"""
edits.py - UI for 'edits' window

refactor TBD: update to share code across edits.py, history.py, file_data.py, db_data.py, db_tables.py
"""

from browser import aio, document, window
from collections import OrderedDict
import json

# local to load first
import page_runner
import tauri

# local imports
from file_sqlite import Database
import app_data
import deeply_utils
# import open_file  # for import-btn ==> not in edits or history
import sync_utils
import table_utils

app_config = None  # must be loaded async
current_window = tauri.window.getCurrentWindow()

# -----
table_name = 'edits'  # table in our internal SQLite DB
table_type = 'edits_actions'


platform = {  # Scott TBD: obsolete? or refactor
	'using_windows': '',
	'using_other': '',
}

# -----
# window_label = None  # edits uses sync_utils; history & db_tables use globals
# window_title = None

# -----
def get_table():
	# refactor TBD: this is a callback so that sync_utils gets its own data!
	# ... but I assume some other code needs an actual callback
	return sync_utils.table


async def reload_rows(tabulator, db):
	rows = await get_rows(db)
	tabulator.replaceData(rows)


# FUTURE: add link_out icon to edits & history

# edits & history & db_tables: no cell editor

# edits & history & db_tables: no insert new_row


# -----
async def main():
	global current_window

	global app_config
	app_config = await window.get_app_config()
	table_utils.app_config = app_config  # must be async so cannot be set on import
	sync_utils.app_config = app_config  # must be async so cannot be set on import

	await table_utils.add_flags_to_body()

	aio.run(sync_utils.run_check())  # check_column_width_queue

	# edits & history & db_tables ==> no #import-btn

	# documentation TBD: which windows need this and why?
	await table_utils.set_title()
	tauri.event.listen(f'title_update', lambda _: aio.run(table_utils.set_title()))

	document.querySelector('.spinner').classList.add('active')  # spinner ON

	# ---------------------------------------------
	# refactor TBD: move this to a local get_edits() like get_history()
	if not app_config.connection_string:  ###DEBUG code to delete
		deeply_utils.pp('edits.py: FIX')
	db = await Database.load(app_config.connection_string)
	rows = await get_rows(db)  # for db_data.py and re-used in edits.py though not sure if best
	# no ForeignKey info for edits table
	# ^^^^^^^^^^ end future get_edits() ^^^^^^^^^^

	sync_utils.table_data = rows  # row_objects

	deeply_utils.pp('edits.py with %s rows' % len(rows))

	create_table(sync_utils.table_data)  # lots of action here

	document.querySelector('.spinner').classList.remove('active')  # spinner OFF

	window.tabulator = sync_utils.table

	# current_window is global

	# documentation TBD: compare this 'listen' across similar code
	# - edits: table_{table_name}_update
	# - history: table_history_update -- perhaps should be table_{table_name}_update
	# - db_tables: file_tables_update
	current_window.listen(f'table_{table_name}_update', lambda _: aio.run(reload_rows(sync_utils.table, db)))

	# TBD: should there be an 'add_new' 'listen' for when a new edit is added?
	# refactor TBD: review/compare/document different close-requested behavior
	current_window.listen('tauri://close-requested', lambda _: aio.run(table_utils.close_window()))

	# edits, history & db_tables: no import_data

	# refactor TBD: global window_title vs. sync_utils.window_title
	sync_utils.window_title = await current_window.title()
	sync_utils.window_label = current_window.label

	document['show-hidden-checkbox'].bind('change', lambda e: sync_utils.update_row_visibility(e, sync_utils.table, table_type))
	document['edit-layout'].bind('click', lambda e: aio.run(sync_utils.open_layout_editor(e)))
	document['edit-sort'].bind('click', lambda e: aio.run(sync_utils.open_sort_editor(e)))
	document['edit-search'].bind('click', lambda e: aio.run(sync_utils.open_search_editor(e)))
	document['export-btn'].bind('click', lambda e: aio.run(sync_utils.open_export_editor(e)))
	document['export-btn'].removeAttribute('disabled')

	document['select-all-btn'].bind('click', lambda event: sync_utils.select_all_checkboxes())
	document['select-none-btn'].bind('click', lambda event: sync_utils.deselect_all_checkboxes())
	document['hide-btn'].bind('click', lambda event: sync_utils.main_tabulator_row_toggle_visibility(event, sync_utils.table))

	# edits, history & db_tables:
	# - no 'Define' column editor
	# - no 'insert_new_row'

	# history.py adds table_history_update listener here

# ^^^^^ END: main()

# history & edits: no special on_window_close() behavior

# -----
# edits, history & db_tables:
# - no 'Define' column editor
# - no 'insert_new_row'

#........................end.............................

# Scott TBD July: probably get rid of these too
def update_main_table_when_recreate(evt):
	sync_utils.on_table_data_loaded(evt, sync_utils.table)


def save_loaded_data(evt):
	sync_utils.on_table_data_loaded_init(evt, sync_utils.table)


def execute_save_loaded_data(evt):
	window.setTimeout(lambda: save_loaded_data(evt), 50)


# create table
def create_table(tabulator_data, is_initial=True):
	# assembles params then calls window.Tabulator.new()

	visibility_dict = {}  # true/false lookup table by column name; default TRUE

	if sync_utils.table:
		# this should correspond to is_initial FALSE but perhaps there are exceptions
		# some changes require rebuilding the table; this preserves then later restores checked/hidden
		sync_utils.save_row_states(sync_utils.table)  # preserve checked or hidden rows; not in 1.0 -- refactor TBD: redundant with sync_utils?
		sync_utils.table.destroy()
		sync_utils.table = None

	# 'if' wrapper since edits & history could be empty ==> tabulator_data[0] will fail; currently let Tabulator show placeholder text
	if tabulator_data:
		if is_initial:
			# comparing similar code elsewhere: tabulator_data param vs. sync_utils.table_data
			sync_utils.columns = [key for key in dict(tabulator_data[0]).keys() if key not in table_utils.built_in_fields]

			# is_customer_data=False for (history, edits, actions, db_tables)
			# FUTURE: add with_details_icon=True for edits after creating a READ ONLY view
			sync_utils.columns = table_utils.add_internal_column_names(sync_utils.columns)

			for col in sync_utils.columns:
				sync_utils.search_options_dropdown[col] = 'like'

		else:
			sync_utils.columns = []
			for item in sync_utils.header_data:
				sync_utils.columns.append(item['name'])
				visibility_dict[item['name']] = item['visible']

		visibility_dict[table_utils.file_row] = False # for edits & history

		column_dicts = table_utils.make_columns(sync_utils, visibility_dict)
		table_utils.add_column_details(column_dicts)  # edits, history & db_tables: no editor_callback

		# include_details_icon=True currently only for user data (csv/txt or DB); should add READ ONLY for history & edits
		final_data = table_utils.get_extra_data(tabulator_data, False, hidden_rows=sync_utils.hidden_rows, checked_rows=sync_utils.checked_rows, include_details_icon=False)

	else:
		# rare: no data -- should only be true for edits & history
		column_dicts = []
		final_data = []

	sync_utils.table = window.Tabulator.new(
		'#table',
		{
			'height': 'auto',
			# 'rowHeight': 24, # rowHeight prevents text from wrapping onto multiple rows ==> don't use!
			'pagination': True,
			'paginationSize': table_utils.pagination_default,
			'paginationSizeSelector': table_utils.pagination_options,
			'paginationButtonCount': table_utils.pagination_buttons,
			'data': final_data,
			'columns': column_dicts,
			'movableColumns': True,
			'editorParams': {'elementAttributes': {'spellcheck': False}},
			'debugInvalidOptions': False,
			'debugInvalidComponentFuncs': False,
			'debugInitialization': False,
			'placeholder': 'No Data Available',  # only for history & edits
			# 'selectableRows': selectable_rows,  # for Delete button ==> only csv/txt and (when implemented) SQLite data
		},
	)

	sync_utils.table.on('tableBuilt', lambda: (
		# print('edits.py -- create_table: table built'),
		table_utils.update_shown_entries(None, sync_utils.table, table_type),
		sync_utils.table.on('columnMoved', lambda column, columns:sync_utils.on_table_column_moved(column,columns, sync_utils.table)),
		sync_utils.table.on('columnResized', lambda column: sync_utils.column_width_updated(column)),
		sync_utils.table.on('dataSorted', lambda sorters, rows: sync_utils.on_table_data_sorted(sorters, rows, sync_utils.table)),
		# cellClick varies
		sync_utils.table.on('headerClick', lambda event, column: sync_utils.on_header_click(event, column, sync_utils.table)),
		sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table)),
		))

	# documentation TBD: why here AND after tableBuilt
	sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table))

	if is_initial:
		sync_utils.table.on('dataLoaded', execute_save_loaded_data)
	else:
		sync_utils.table.on('dataLoaded', update_main_table_when_recreate)

		# restore_row_states is for checked or hidden rows; not in 1.0
		window.setTimeout(lambda: sync_utils.restore_row_states(sync_utils.table, sync_utils.table_data), 50)

	window.tabulator = sync_utils.table


#...........................................end...........................................


async def get_rows(db):
	# globals not modified: table_name which is always 'edits' here

	query = f"SELECT * from '{table_name}'"  # performance TBD: can we LIMIT to 100 for initial display then page additional records for search/sort?
	rows = await db.select(query)  # row_objects

	# add icon_link_out -- BUT not yet supported for edits since that requires a READ ONLY view
# 	for i in range(len(rows)):
# 		row_dict = OrderedDict(dict(rows[i]))  # convert from JSObject; move_to_end requires OrderedDict
#
# 		row_dict[table_utils.details_icon_field] = app_data.icon_link_out
# 		row_dict.move_to_end(table_utils.details_icon_field, last=False)  # OrderedDict move_to_end with last=False means move_to_START
#
# 		row = __BRYTHON__.pyobj2jsobj(dict(row_dict))  # Convert back to JSObject
# 		rows[i] = row

	deeply_utils.pp('edits.py - get_rows - table_name, len(rows)', table_name, len(rows))

	return rows  # row_objects

sync_utils.setup_window_event_handlers(current_window, create_table, get_table)  # window object + 2 callbacks

page_runner.register_for_js()  # hardcoded for 'main'
