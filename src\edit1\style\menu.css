.window-header {
	width: 100%;
	height: var(--header-footer-h);
	background-color: var(--menu-color);
	padding: 0 24px 0 4px;
	border-bottom: 1px solid var(--menu-border-color);
	font-size: var(--menu-text);
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.window-header .r-box {
	display: flex;
	gap: 8px;
	align-items: center;
	/* right: 4px; */
	/* top: 0; */
}

.window-header .l-box {
	display: flex;
	gap: 8px;
	align-items: center;
}

.window-header .l-box button{
	transition: all 0.3s ease;
}

.window-header .l-box button:hover{
	background-color: #dedede;
}

.window-header .r-box button#toggle-theme {
	width: 16px;
	height: 16px;
	padding: 0;
	/* font-size: var(--menu-text); */
	font-size: 14px;
	border: none;
	padding-top: 1px;
	background-color: transparent;
	cursor: pointer;
}

.window-header .l-box .line-wrap-btn {
	/* width: 16px;
	height: 16px; */
	padding: 0;
	/* font-size: var(--menu-text); */
	font-size: 14px;
	border: none;
	padding-top: 1px;
	background-color: transparent;
	cursor: pointer;
}

.window-header .l-box .line-wrap-btn.active {
	filter:brightness(0) saturate(100%) invert(25%) sepia(100%) saturate(1234%) hue-rotate(180deg);
}

.window-header .l-box .line-wrap-btn>img {
	width: 24px;
	/* height: 16px; */
	padding: 0;
	/* font-size: var(--menu-text); */
	font-size: 14px;
	border: none;
	padding-top: 1px;
	background-color: transparent;
	cursor: pointer;
}
