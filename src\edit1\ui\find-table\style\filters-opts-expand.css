#filters-opts-expand:checked ~ .filters-opts > .name > .section-state > .down {
	display: block;
}

#filters-opts-expand:not(:checked) ~ .filters-opts > .name > .section-state > .down {
	display: none;
}

#filters-opts-expand:checked ~ .filters-opts > .name > .section-state > .right {
	display: none;
}

#filters-opts-expand:not(:checked) ~ .filters-opts > .name > .section-state > .right {
	display: block;
}

#filters-opts-expand:checked ~ .filters-opts {
	flex-wrap: wrap;
}

#filters-opts-expand:not(:checked) ~ .filters-opts > .filters >.inputs-area {
	display: none;
}


#filters-opts-expand:checked ~ .filters-opts > .filters >.inputs-area {
	display: flex;
}



#search .opts2 .filters-opts{
	width: 100%;
	display: flex;
}

#search .opts2 .filters-opts .filters{
	display: flex;
	flex-direction: column;
	width: 70%;
	gap: 8px;
}

#search .opts2 .filters-opts .filters .inputs-area{
	gap: 8px;
	width: calc(100% - 8px);
}

#search .opts2 .filters-opts .filters .inputs-area .input-box{
	display: flex;
	flex-direction: column;
	width: 50%;
	gap: 4px;
}

#search .opts2 .filters-opts .filters .inputs-area .input-box>textarea{
	width: 100%;
	min-height: 50px;
	max-height: 150px;
	resize: vertical;
}

#search .opts2 .filters-opts .filters .checkbox-area{
	display: flex;
	gap: 8px;
	width: 100%;
}

#search .opts2 .filters-opts .filters .checkbox-area .checkbox{
	display: flex;
	justify-content: center;
	align-items: center;
}
