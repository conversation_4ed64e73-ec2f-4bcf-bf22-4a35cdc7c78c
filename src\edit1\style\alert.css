:root {
	--alert-close-color: #f22;
	/* ERROR */
	--alert-err-bg: #fdd;
	--alert-err-msg: #a11;
	--alert-err-icon: #a11;
	/* SUCCESS */
	--alert-success-bg: #dfd;
	--alert-success-msg: #1a1;
	--alert-success-icon: #1a1;
	--alert-success-close: #080;
	/* INFO */
	--alert-info-bg: #ddf;
	--alert-info-msg: #11a;
	--alert-info-icon: #11a;
	--alert-info-close: #66f;
}

.dark-theme {
	--alert-close-color: #fdd;
	/* ERROR */
	--alert-err-bg: #300;
	--alert-err-msg: #f66;
	--alert-err-icon: #a11;
	/* SUCCESS */
	--alert-success-bg: #040;
	--alert-success-msg: #6f6;
	--alert-success-icon: #1a1;
	--alert-success-close: #0c0;
	/* INFO */
	--alert-info-bg: #005;
	--alert-info-msg: #ccf;
	--alert-info-icon: #66a;
}

.alert {
	position: fixed;
	right: 16px;
	bottom: 16px;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: auto;
	height: 50px;
	min-width: 250px;
}

.alert .alert-body {
	width: 100%;
	height: calc(100% - 4px);
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 16px;
	gap: 8px;
}

.alert .alert-body .body-left {
	width: auto;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 8px;
}

.alert .left-b {
	position: absolute;
	width: 4px;
	height: 100%;
	left: 0;
	border-top-right-radius: 8px;
	border-bottom-right-radius: 8px;
}

.alert .progress-box {
	width: 100%;
	height: 4px;
	background-color: transparent;
}

.alert .progress-box .progress {
	width: 100%;
	height: 4px;
}

.alert .icon {
	font-size: 1.4rem;
}

.alert .close {
	cursor: pointer;
}

.alert .message {
	font-size: 1rem;
}
/* ERROR STYLE */
.alert.error {
	background-color: var(--alert-err-bg);
	color: var(--alert-err-msg);
}

.alert.error .icon {
	color: var(--alert-err-icon);
}

.alert.error .left-b {
	background-color: var(--alert-err-icon);
}

.alert.error .close {
	color: var(--alert-err-close);
}

.alert.error .progress {
	background-color: var(--alert-err-icon);
}
/* ERROR STYLE */
.alert.success {
	background-color: var(--alert-success-bg);
	color: var(--alert-success-msg);
}

.alert.success .icon {
	color: var(--alert-success-icon);
}

.alert.success .left-b {
	background-color: var(--alert-success-icon);
}

.alert.success .close {
	color: var(--alert-success-close);
}

.alert.success .progress {
	background-color: var(--alert-success-icon);
}

/* INFO STYLE */

.alert.info {
	background-color: var(--alert-info-bg);
	color: var(--alert-info-msg);
}

.alert.info .icon {
	color: var(--alert-info-icon);
}

.alert.info .left-b {
	background-color: var(--alert-info-icon);
}

.alert.info .close {
	color: var(--alert-info-close);
}

.alert.info .progress {
	background-color: var(--alert-info-icon);
}

@keyframes progress-anim {
	0% {
		width: 100%;
	}
	100% {
		width: 0%;
	}
}
