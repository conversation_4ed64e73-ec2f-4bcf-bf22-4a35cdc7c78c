import * as config from './config.js';
/**
 *
 * @param {import("selenium-webdriver").ThenableWebDriver} driver
 */
export async function findWindow(driver, label) {
	await driver.wait(async () => {
		console.log('searching main window...');
		const windows = await driver.getAllWindowHandles();
		for (const window of windows) {
			await driver.switchTo().window(window);
			const windowLabel = await driver.executeScript('return window.__TAURI__.window.getCurrentWindow().label');
			if (windowLabel == label) {
				return true;
			}
		}
	}, config.waitforInitTimeout);
}

/**
 *
 * @param {number} ms
 * @returns
 */
export async function sleep(ms) {
	return new Promise((resolve) => setTimeout(resolve, ms));
}
