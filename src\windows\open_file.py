"""
open_file.py
"""

from browser import aio, alert, document, window
import javascript
import json
import tauri

# local imports
import table_utils

app_config = None  # must be loaded async

header_data = []  # documentation TBD

PROPS = None
NAME = None
try:
	PROPS = dict(window['__PROPS__'])
	NAME = PROPS.get('name', PROPS.get('table'))
	# print('open_file.py -- NAME', NAME)
except Exception as e:
	print('ERROR in open_file.py; could not get PROPS', e)
	pass


async def open_file(from_menu=False):
	# called directly or via menu_utils.py
	# global app_config  # required for 'if not app_config'

	if not app_config:
		print('\n?' * 3, 'TBD: what is best way to ensure it is already loaded?')

	# post the 'open' dialog to get source folder & filename
	file = await window.__TAURI__.dialog.open({
		'multiple': False,  # only select 1 file
		'directory': False,  # file not folder
		'name': 'File',
		# Allow any extension
		# 'filters': [{ 'name': 'File', 'extensions': extensions }],
	})

	# print('open_file', file)
	if not file:  # SEE ALSO: if file is not javascript.NULL:
		return

	# CONSIDER
	'''
	from pathlib import Path

	if file:
		ext = Path(file).suffix.lower().lstrip('.')
		if ext in ('html', 'json'):
	'''

	# refactor TBD: call table_utils.is_allowed_to_open_file_with_ext
	if any(file.endswith(i) for i in ['sqlite', 'db']) and not app_config.flags.can_open_sqlite:
		alert('SQLite files are not yet supported')
		return

	if any(file.endswith(i) for i in ['json', 'jsonl']) and not app_config.flags.can_handle_json:
		alert('JSON files are not yet supported')
		return

	elif any(file.endswith(i) for i in ['html', 'htm']) and not app_config.flags.can_handle_html:
		alert('HTML files are not yet supported')
		return

	exists = await tauri.invoke('exists', {'path': file})
	if not exists:
		alert('File not found!')
		return

	label = tauri.window.getCurrentWindow().label
	src = window.__TAURI__.core.convertFileSrc(file)

	if header_data and not from_menu:
		# documentation TBD: why check both header-data and 'not from_menu'
		# ... why columns, label & name here but not below?

		# read the data
		await tauri.invoke('open_file', {
			'path': file,
			'src': src,
			'headerColumns': json.dumps(header_data),
			'parentLabel': label,
			'targetName': NAME,
			})
		# print('open_file.py -- header_data', header_data)

	else:
		await tauri.invoke('open_file', {
			'path': file,
			'src': src,
			})
		# print('open_file.py -- no header_data')


def bind_this_button(btn_id):
	# currently called with:
	# - '.open' class (which should probably be an ID) -- for home page
	# - #import-btn ID -- for csv/txt or SQLite data

	file_btn = document.querySelector(btn_id)
	if not file_btn:
		# should never happen!
		print('ERROR: missing button')
		return

	# TBD: if stopImmediatePropagation is important here then why not elsewhere (or vice versa)
	# aio.run seems to be required
	file_btn.bind('click', lambda e:( e.stopImmediatePropagation(), aio.run(open_file())))


async def open_file_from_menu(event):
	# handle emit from menu.rs via the 'listen' below

	# print('open_file_from_menu called with event:', dict(dict(event)['payload']))
	win_label = dict(dict(event)['payload'])['label']
	if win_label != label:
		# print(f'Skipping open_file_from_menu for label {win_label}, current label is {label}')
		return

	await open_file(from_menu=True)

current_window = table_utils.get_current_window()
if current_window:
	label = current_window.label
	current_window.listen('open_file_menu', lambda event: aio.run(open_file_from_menu(event)))  # emit from menu.rs
else:
	print('ERROR: no current window')
