<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<title>Define</title>

		<!-- Brython -->
		<script type="text/javascript" src="/lib/brython/brython.js"></script>
		<script type="text/javascript" src="/lib/brython/brython_stdlib.js"></script>

		<!-- Tabulator -->
		<script type="text/javascript" src="/lib/tabulator/tabulator_custom.js"></script>
		<link rel="stylesheet" href="/lib/tabulator/tabulator_bulma.min.css" />

		<!-- Stylesheets -->
		<link rel="stylesheet" href="/common/makedeeply.css" />
		<link rel="stylesheet" href="editors.css" />

		<!-- Scripts -->
		<script src="/common/tauri.py" type="text/python" id="tauri"></script>
		<script src="/common/app_data.py" type="text/python" id="app_data"></script>
		<script src="/common/table_utils.py" type="text/python" id="table_utils"></script>

		<script src="/windows/open_file.py" type="text/python" id="open_file"></script>
		<script src="/windows/shortcuts.py" type="text/python" id="shortcuts"></script>

		<script src="column_dialog.py" type="text/python" id="column_dialog"></script>
	</head>
<body id="column-dialog" class="editor-page">
	<div id="editor-container">
		<div id="column-table"></div>
		<div id="add-column-section">
			<input id="add-column-name" placeholder="Name" spellcheck="false">
			<input id="add-column-label" placeholder="Label" spellcheck="false">
<!-- column title is in column_dialog.py -->
<!--
			<select id="add-column-type">
				<option>(unknown)</option>
				<option>text</option>
				<option>integer</option>
				<option>real</option>
				<option>date</option>
				<option>datetime</option>
				<option>json</option>
				<option>binary</option>
			</select>
 -->
			<button id="add-column-add-btn" disabled="">Add</button>
		</div>
		<div id="ok-cancel-btn-group">
			<button id="column-editor-cancel-btn" class="ok-cancel">Cancel</button>
			<button id="column-editor-confirm-btn" class="ok-cancel" disabled="">Save</button>
		</div>
		<div id="column-editor-dialog" class="dialog">
			<div class="dialog-content">
				<p>Do you want to save changes?</p>
				<div class="dialog-btn-group">
					<button id="column-editor-dialog-discard-btn" class="ok-cancel">Discard</button>
					<button id="column-editor-dialog-save-btn" class="ok-cancel">Save</button>
				</div>
			</div>
		</div>
	</div>

</body>
</html>
