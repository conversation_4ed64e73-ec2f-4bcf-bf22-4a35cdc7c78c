.titlebar {
	height: 24px;
	width: 100%;
	background: var(--titlebar-bg);
	color: var(--primary-text);
	display: flex;
	justify-content: center;
	align-items: center;

	user-select: none; /* Standard */
	-webkit-user-select: none; /* Safari */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* IE 10+ */
}
.titlebar .titlebar-button {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 24px;
	height: 24px;
	margin-right: 4px;
	cursor: pointer;
}

.titlebar .titlebar-button:hover {
	background: var(--hover-bg);
}
.titlebar .fa-xmark {
	color: #ff6060;
}
.titlebar .box {
	width: 10%;
	user-select: none;
}

.titlebar .right-box {
	width: 10%;
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.titlebar .middle-box {
	width: 80%;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 8px;
	cursor: default;
}

.titlebar .left-box {
	width: 10%;
	padding-left: 4px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	font-weight: bold;
	font-size: 14px;
}

.titlebar .left-box .symbole {
	color: #f1a415;
	margin-right: 4px;
	transform: rotate(90deg);
}

#titlebar-title {
	width: 100%;
	font-size: 0.9rem;
}
