
use std::collections::HashMap;

use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug,Clone)]
pub struct AutosaveFile {
	pub window_label:String,
    pub original:Option<String>,
	pub autosave:String,
	pub last_saved:String,
	pub iclose:bool
}

#[derive(Serialize, Deserialize, Debug)]
pub struct AutosaveMap {
	pub files: HashMap<String, AutosaveFile>,
}


#[derive(Serialize, Deserialize, Debug,Clone)]
pub struct AutosaveFileData {
	pub autosave:AutosaveFile,
	pub content:String,
}
