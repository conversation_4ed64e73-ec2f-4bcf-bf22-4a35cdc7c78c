-- create actions table
CREATE TABLE IF NOT EXISTS actions (
	id INTEGER NOT NULL PRIMARY KEY,
	action TEXT NOT NULL,   
	details TEXT, -- post_id or user_id
	count INTEGER,
	date_saved TEXT NOT NULL, -- when we performed the action
	UNIQUE(id)
);

-- create edits table
CREATE TABLE IF NOT EXISTS edits (
	id INTEGER NOT NULL PRIMARY KEY,
	the_table TEXT NOT NULL,
	the_id TEXT NOT NULL,
	the_field TEXT NOT NULL,
	old_value TEXT NOT NULL DEFAULT '',
	new_value TEXT NOT NULL,
	timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS history (
	id INTEGER NOT NULL PRIMARY KEY,
	kind TEXT NOT NULL, -- file or future Postgres connection
	path TEXT NOT NULL, -- path to file
	path_id TEXT NOT NULL, -- file ID from refind crate
	date_opened TEXT, -- Date UTC
	record INTEGER
);
