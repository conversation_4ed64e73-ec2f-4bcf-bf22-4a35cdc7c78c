use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Listener, Manager, Window};

use crate::window::menu;

// #[tauri::command]
// pub async fn open_finder(window: Window, handle: AppHandle) {
// 	// let menu = create_menu(&handle, handle.state(), false).expect("Couldn't create menu for the window!");
// 	let finder = handle.get_webview_window("search"); //.expect("error geting the finder");
// 	match finder {
// 		Some(wind) => {
// 			let w = window.clone(); //handle..expect("error geting the current window");
// 			let _ = wind.show();
// 			let _ = wind.set_focus();

// 			let _ = wind.emit_to("search", "finder:on-open", w.label());
// 		}
// 		None => {
// 			let w = window.clone();
// 			let new_search = tauri::WebviewWindowBuilder
// 				::new(&handle, "search", tauri::WebviewUrl::App("ui/find-dialog/find-dialog.html".into()))
// 				.title("Find")
// 				.inner_size(600.0, 236.0)
// 				.skip_taskbar(true)
// 				.resizable(true)
// 				.maximizable(false)
// 				.minimizable(false)
// 				.min_inner_size(700.0, 236.0)
// 				// .menu(menu)
// 				.build()
// 				.unwrap();
// 			new_search.clone().once("finder:loaded", move |_| {
// 				let _ = new_search.clone().emit("finder:on-open", w.label());
// 			});
// 		}
// 	}
// }

#[tauri::command]
pub async fn open_finder(window: Window, handle: AppHandle) {
    let finder = handle.get_webview_window("search");

    match finder {
        Some(wind) => {
            let w = window.clone();
            let _ = wind.show();
            let _ = wind.set_focus();

            let _ = wind.emit_to("search", "finder:on-open", w.label());
        }
        None => {
            let w = window.clone();
            let handle_clone = handle.clone();

            // Use `tauri::async_runtime::spawn` to create the window asynchronously
            tauri::async_runtime::spawn(async move {
                let new_search = tauri::WebviewWindowBuilder::new(
                    &handle_clone,
                    "search",
                    tauri::WebviewUrl::App("src/edit1/ui/find-dialog/find-dialog.html".into()),
                )
                .title("Find")
                .inner_size(600.0, 236.0)
                .skip_taskbar(true)
                .resizable(true)
                .maximizable(false)
                .minimizable(false)
                .min_inner_size(700.0, 236.0)
                // .menu(menu) // Uncomment if you have a menu to add
                .build()
                .expect("Failed to create search window");

				let search = new_search.clone();
                search.clone().once("finder:loaded", move |_| {
                    let _ = search.emit("finder:on-open", w.label());
                });

				let window_c = new_search.clone();
				let window_c1 = new_search.clone();
				let app_handle_c = handle.clone();
				let app_handle_c1 = handle.clone();

				window.listen("tauri://window-created", move |event| {
					menu::update_titles(&app_handle_c);
					window_c.unlisten(event.id());
				});
				window.listen("tauri://destroyed", move |event| {
					menu::update_titles(&app_handle_c1);
					window_c1.unlisten(event.id());
				});
            });
        }
    }
}
