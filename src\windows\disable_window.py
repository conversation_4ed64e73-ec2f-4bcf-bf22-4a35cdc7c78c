"""
disable_window.py - 'lock' the data window for modal dialogs: Export & Define/columns

# refactor TBD: move this code elsewhere
"""

from browser import aio, document, html, timer
import tauri
import time


async def prevent_click(event, payload):
	event.preventDefault()
	webview = window.__TAURI__.webviewWindow
	windows = await webview.getAllWebviewWindows()

	# documentation TBD: what is 'by'?
	dst_window = next((w for w in windows if dict(w)['label'] == dict(payload)['by']), None)
	if dst_window:
		dst_window.setFocus(True)


def disable_window(event):
	try:
		div = html.DIV(
			style={
			'userSelect': 'none',
			'display': 'block',
			'position': 'absolute',
			'padding': '0',
			'margin': '0',
			'left': '0',
			'top': '0',
			'width': '100%',
			'height': '100%',
			'background': 'rgba(255, 255, 255, 0.5)',  # 50% translucent = washed out
			'zIndex': '1'
			},
			Class='disable-window'
		)

		payload = event.payload
		div.bind('click', lambda event, payload=payload: aio.run(prevent_click(event, payload)))
		document.body.appendChild(div)
	except Exception as e:
		print('ERROR in disable_window:', e)


def enable_window():
	disable_div = document.querySelector('.disable-window')
	if disable_div:
		disable_div.remove()

current_window = tauri.window.getCurrentWindow()
current_window.listen('disable-window', lambda event: disable_window(event))
current_window.listen('enable-window', lambda _: enable_window())


