[package]
name = "${APP_NAME}"
version = "${VERSION}"
description = "Does this appear anywhere?"
authors = ["you"]
license = ""
repository = ""
edition = "2021"
default-run = "${APP_NAME}"

[[bin]]
name = "${APP_NAME}"
path = "src/main.rs"

[build-dependencies]
tauri-build = { version = "2.0.2", features = [] }

[dependencies]
tauri = { version = "2.0.6", features = [
	"protocol-asset",
	"devtools",
	"unstable",
] }
tauri-plugin-window-state = "2.0.2"
tauri-plugin-dialog = "2.0.3"
tauri-plugin-fs = "2.0.3"

tauri-plugin-sql = { features = ["sqlite"], version = "2" }

serde = { version = "1.0", features = ["derive"] }
serde_json = { version = "1.0", features = ["preserve_order"] }
urlencoding = "2.1.3"
reqwest = { version = "0.11.27", features = ["json"] }
env_logger = "0.10.2"
log = "0.4.22"
chrono = "0.4.38"
open = "5.3.0"
base64 = "0.21.7"
url = "2.5.3"
anyhow = "1.0.93"
clap = { version = "4.5.20", features = ["derive"] }
lazy_static = "1.5.0"
libaes = "0.7.0"
axum = "0.7.7"
tokio = "1.41.1"
machineid-rs = "1.2.4"
uuid = { version = "1.11.0", features = ["v4"] }
rand = "0.8.5"
derive_builder = "0.20.2"
handlebars = "5.1.2"
refind = "0.1.2"
eyre = { git = "https://github.com/thewh1teagle/eyre", branch = "feat/report-serialize-backtrace", features = [
	"serialize",
] }
indexmap = "2.6.0"
sqlx = { version = "0.8.2", features = [
		"json",
		"time",
		"runtime-tokio",
		"sqlite",
] }
sha2 = "0.10.8"
hex = "0.4.3"

mime_guess = "2.0.5"

[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.25.0"
objc = "0.2.7"
whoami = "1.5.2"

[target.'cfg(target_os = "windows")'.dependencies]
winreg = "0.52.0"
windows = { version = "0.52.0", features = [
	"Win32_Foundation",
	"Win32_Storage",
	"Storage",
	"Win32_Storage_FileSystem",
] }

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
