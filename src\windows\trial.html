<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Trial Version</title>

		<script type="module" src="/common/makedeeply.js" defer></script>
		<!-- <script src="/windows/trial.py" type="text/python" id="trial"></script> -->
		<script type="module" src="/windows/trial.js" defer></script>

		<link rel="stylesheet" href="/common/makedeeply.css" />
	</head>

	<body id="trial-dialog" class="center dialog">
		<h1 id="header">Trial version</h1>

		<div class="indent-1"><span class="days-remaining">__</span> days remaining</div>

		<div class="stacked purchase_buttons">
			<button class="learn secondary">Learn More</button>
			<button class="buy">Buy Now</button>
		</div>

		<div class="lemon-squeezy">Payments are securely handled by <PERSON>queezy, a reliable merchant of record.</div>
	</body>
</html>
