"""
app_data.py - static app / project constants

(useful to distinguish these from file globals that may be changed at runtime)
"""
# images
image_width = 18  # only used here

icon_link_out = f'<img width="{image_width}px" src="/assets/link-out.svg" />'
icon_open = f'<img width="{image_width}px" src="/assets/grid-gray.svg" />'
icon_restore =  f'<img width="{image_width}px" src="/assets/grid-gray-restore.svg" />'
# ... 'restore' = 'bring to front' or 'unminimize'

# colors; refactor TBD: probably move most or all to a CSS file
light_gray_color_main_table = '#D3D3D3'
white_color = 'white'
pale_tan_color = '#f4e4c9'
gray_color = 'gray'
black_color = '#363636'
light_gray_color = '#C8C8C8'

