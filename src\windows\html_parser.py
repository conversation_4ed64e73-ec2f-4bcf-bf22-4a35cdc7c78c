from html.parser import HTMLParser

class TableHTMLParser(HTMLParser):
	def __init__(self):
		super().__init__()
		self.in_table = False
		self.in_row = False
		self.in_cell = False
		self.headers = []
		self.current_row = []
		self.data = []
		self.current_cell_data = ''
		self.header_row_processed = False

	def handle_starttag(self, tag, attrs):
		if tag == 'table':
			self.in_table = True
		elif tag == 'tr' and self.in_table:
			self.in_row = True
			self.current_row = []
		elif tag in ('th', 'td') and self.in_table:
			self.in_cell = True
			self.current_cell_data = ''

	def handle_endtag(self, tag):
		if tag == 'table':
			self.in_table = False
		elif tag == 'tr' and self.in_row:
			if self.in_row:
				if not self.header_row_processed:
					if self.current_row:
						self.headers = self.current_row
						self.header_row_processed = True
				else:
					if self.current_row and self.headers:
						self.data.append(dict(zip(self.headers, self.current_row)))
				self.in_row = False
		elif tag in ('th', 'td') and self.in_cell:
			if self.header_row_processed:
				self.current_row.append(self.current_cell_data.strip())
			else:
				self.current_row.append(self.current_cell_data.strip())
			self.in_cell = False

	def handle_data(self, data):
		if self.in_cell:
			self.current_cell_data += data
