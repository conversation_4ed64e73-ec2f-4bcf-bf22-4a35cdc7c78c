/* tabulator variables */
:root {
	--tab-bg-color: #fff;
	--tab-nodata-bb: #ddd;
	--tab-header-bb: #999;
	--tab-header-text: #363636;
	--tab-table-text: #111;
	--tab-cell-bb: #dbdbdb;
	--tab-group-bg-color: #def;
	--tab-group-text-color: black;
	--tabulator-find-highlight: #fbfb98aa;
	--tabulator-replace-highlight: #98fb98aa;
	--disabled-row-color: #aaa;
	--refresh-filter: none;
	--resizer-bg: #555;
}

.dark-theme {
	--tab-bg-color: #444;
	--tab-nodata-bb: #222;
	--tab-header-bb: #222;
	--tab-header-text: #efefef;
	--tab-table-text: #efefef;
	--tab-cell-bb: #222;
	--tab-group-bg-color: #678;
	--tab-group-text-color: white;
	--tabulator-find-highlight: #fbfb9860;
	--tabulator-replace-highlight: #98fb9860;
	--disabled-row-color: #888;
	--refresh-filter: invert();
	--resizer-bg: #aaa;
}

* {
	transition: color 0.2s, background 0.3s;
}

body,
html {
	width: 100vw;
	/* height: 100vh; */
	padding: 0;
	margin: 0;
	overflow-y: auto;
	font-size: 1rem;
}

* {
	box-sizing: border-box;
}

body {
	height: auto;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

#table {
	flex: 1 1 0%;
	width: 100%;
	min-height: 100vh;
}

#table .replace-btn:hover {
	filter: invert(100%);
}

#table .match {
	background-color: var(--tabulator-find-highlight);
}

#table .match-replace {
	background-color: var(--tabulator-replace-highlight);
}

.tabulator {
	background-color: var(--tab-bg-color);
}

.tabulator .tabulator-header {
	border-bottom: 1px solid var(--tab-header-bb);
	color: var(--tab-header-text);
}

.tabulator .gray-row {
	color: var(--disabled-row-color);
}

.tabulator .gray-row :matches(.match, .match-replace) {
	background-color: transparent !important;
}

.tabulator .tabulator-header .tabulator-header-filte input[type='search']::-moz-search-decoration {
	-moz-appearance: none;
}

.tabulator .tabulator-tableholder .tabulator-table {
	color: var(--tab-table-text);
}

.tabulator .tabulator-row .tabulator-cell {
	border-bottom: 1px solid var(--tab-cell-bb);
	padding: 0.2em 0.75em;
}

.tabulator-row.tabulator-group {
	background: var(--tab-group-bg-color);
}

/* have to mimic tabulator exactly */
@media (hover: hover) and (pointer: fine) {
	.tabulator-row.tabulator-group:hover {
		background: var(--tab-group-bg-color);
	}
}

.tabulator-row.tabulator-group span {
	color: var(--tab-group-text-color);
}



#search form#finder-form {
	height: fit-content /* fit-content */ /* calc(100% - 105px) */;
	justify-content: flex-start;
	display: flex !important;
	flex-wrap: nowrap;
	flex-direction: row;
}

#search .section-state {
	cursor: pointer;
}

#search form .input-container {
	height: calc(100%-50px);
	width: 100%;
	display: flex !important;
	justify-content: center;
	flex-wrap: nowrap;
	flex-direction: column;
}

#search form .input-container.row {
	flex-direction: row;
	gap: 12px;
}

#search form .input-container.col .box .input-box textarea {
	height: 100%;
}

#search form .input-container.row .box .input-box {
	flex-direction: row;
	gap: 8px;
	width: 100%;
	align-items: center;
	padding-top: 12px;
}

#search form .input-container.row .box .input-box textarea {
	height: 32px !important;
	width: 100%;
}

#search .options-box {
	height: fit-content !important;
	align-items: start;
}

#search .opts2 .opt.with-list{
	display: flex;
	justify-content: start;
	align-items: start;
	flex-direction: column;
}

#search .opts2 .opt.with-list > .check {
	display: flex;
	gap: 4px;
}

#search .opts2 .opt.with-list > .list {
	display: none;
	width: 100%;
	padding-left: 24px;
	gap: 4px;
	max-height: 190px;
	overflow-y: auto;
	margin-top: 8px;
}

#search .opts2 .opt.with-list > .list > .list-item {
	font-size: 12px;
	display: flex;
	align-items: center;
	width: fit-content;
	border: 1px solid var(--pale_tan);
	padding: 1px 8px 1px 1px;
	gap: 8px;
	transition: 0.2s ease-in-out;
}

#search .opts2 .opt.with-list > .list > .list-item > i {
	height: 100%;
	border: 1px solid;
	padding: 8px 8px;
	cursor: pointer;
}

#search .opts2 .opt.with-list > .list > .list-item > i:hover {
	/* transform: scale(1.2); */
	color: var(--deep-red);
}

#search .opts2 .opt.with-list > .list > .list-item > .ignored {
	color: #ee4040;
}

#search .opts2 .target-opts .checkbox-row > .opt.with-list button {
	width: 20px;
	height: 20px;
	color: var(--button_color) !important;
	border: 2px solid var(--button_color) !important;
	background-color: transparent !important;
	font-weight: bold;
}

#search .opts2 .target-opts .checkbox-row > .opt.with-list button > i {
	font-size: 12px;
	margin-top: 2px;
}

#search .opts2 :is(.target-opts,.filters-opts) .name {
	display: flex;
	gap: 4px;
}

#search .opts2 .target-opts .target-btns {
	display: flex;
	align-items: center;
	gap: 4px;
}

#search .opts2 :is(.target-opts,.filters-opts) .target-btns > button {
	padding: 8;
	color: var(--button_color) !important;
	border: 2px solid var(--button_color) !important;
	background-color: transparent !important;
	font-weight: bold;
}

#search .options-box .target-opts .target-btns > button > i {
	margin-top: 2px;
}

#search .options-box .btns-box {
	display: flex;
	align-items: start;
	padding-right: 18px;
}

#search .bottom-opts {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: start;
	color: var(--primary-text);
	position: relative;
}

.opts,
.opts2 {
	padding-left: 8px;
}

.matching-opts,
.target-opts,
.bottom-opts {
	display: flex;
	flex-direction: row;
	align-items: center;
}

#search .hidden {
	display: none !important;
}

#search .replace-in-box{
	margin-top: 4px;
}

#search .replace-set-box {
	position: absolute;
	right: 16px;
}


#resizer {
	width: 100%;
	position: relative;
	bottom: 0px;
	cursor: ns-resize;
	margin-bottom: 6px;
	margin-top: 6px;
	height: 6px;
	background: linear-gradient(to bottom, white 0px, white 2px, var(--resizer-bg) 2px, var(--resizer-bg) 4px, white 4px, white 6px);
}

#resizer img {
	display: block;
	margin-left: auto;
	top: 35%;
	transform: translateY(-35%);
	z-index: 1;
	filter: var(--refresh-filter);
	padding: 1px 0;
	border-radius: 8px;
	user-select: none;
	pointer-events: none;
}

.divider{
	width: calc(100% - 8px);
	height: 1px;
	background-color: #efefef;
	margin: 8px 0;
}

.ignored-group{
	color: red !important;
}
