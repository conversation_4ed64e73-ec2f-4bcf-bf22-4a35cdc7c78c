"""
db_data.py - UI for SQLite records in a single table

Advanced feature: shows additional info for each Foreign Key

refactor TBD: update to share code across edits.py, history.py, file_data.py, db_data.py, db_tables.py
"""

from browser import aio, document, window
from collections import OrderedDict
from typing import List
import json

# local to load first
import page_runner
import tauri

# local imports
from file_sqlite import Database
import app_data
import db_utils
import deeply_utils
import open_file  # for import-btn
import sync_utils
import table_utils

app_config = None  # must be loaded async
current_window = tauri.window.getCurrentWindow()  # TBD: or None then fetch later

# -----
# caveat: there's a risk these aren't ready at import time ==> TBD: move to main()

PROPS = None
try:
	PROPS = dict(window['__PROPS__'])
	INSERT_ID = PROPS['insert_id']
	db_path = PROPS['path']

	table_name = PROPS['table']  # refactor TBD: rename in Rust too
except Exception as e:
	print('ERROR in table_utils: could not get __PROPS__', e)

# -----
table_type = 'db_table'
table_information = {}  # refactor TBD: probably get from key_info (or keep this and remove from there)


platform = {  # TBD: obsolete? or refactor
	'using_windows': '',
	'using_other': '',
}

# -----
key_info = {}  # only in db_data.py; for menu_new_row ... TBD: what about file_data.py?

# -----
def get_table():
	return sync_utils.table  # TBD: table vs. sync_utils.table


async def reload_rows(tabulator, simple_db, table_info):
	rows = await get_rows(simple_db, table_info)
	rows = await prepare_rows_with_fk(simple_db, rows)  # special case for db_data
	tabulator.replaceData(rows)


# TBD: diff against file_data.py & db_tables.py


# -----
async def main():
	global current_window
	global key_info, table_information  # maybe the latter should only live in key_info?

	global app_config
	app_config = await window.get_app_config()
	open_file.app_config = app_config  # must be async so cannot be set on import
	sync_utils.app_config = app_config  # must be async so cannot be set on import
	table_utils.app_config = app_config  # must be async so cannot be set on import

	await table_utils.add_flags_to_body()

	aio.run(sync_utils.run_check())  # check_column_width_queue

	open_file.bind_this_button('#import-btn')  # only file_data.py & db_data.py support Import

	table_utils.check_os(platform)  # sets platform; should be refactored

	# documentation TBD: which windows need this and why?
	await table_utils.set_title()
	tauri.event.listen(f'title_update', lambda _: aio.run(table_utils.set_title()))

	document.querySelector('.spinner').classList.add('active')  # spinner ON

	# ---------------------------------------------
	# refactor TBD: move this to a local get_records() like get_history()
	deeply_utils.pp('db_data.py - db_path', db_path)
	# db = await Database.load(f'sqlite:{db_path}')
	simple_db = db_utils.SimpleDB(db_path)
	table_information = await simple_db.get_table_info(table_name)  # fetch data to set the global
	rows = await get_rows(simple_db, table_information)  # for db_data.py and re-used in edits.py though not sure if best
	rows = await prepare_rows_with_fk(simple_db, rows)  # specific to SQLite
	# ^^^^^^^^^^ end future get_records() ^^^^^^^^^^

	sync_utils.table_data = rows

	create_table(sync_utils.table_data)  # lots of action here

	document.querySelector('.spinner').classList.remove('active')  # spinner ON

	window.tabulator = sync_utils.table

	# for menu_new_row
	key_info['simple_db'] = simple_db
	key_info['tabulator'] = sync_utils.table  # performance/memory: by reference so low overhead
	key_info['table_info'] = table_information

	### vs file_data.py: most of the similar scripts put these listeners here
	# current_window is global
	current_window.listen('add_new', lambda payload: deb(payload))
	current_window.listen(f'table_{table_name}_update', lambda _: aio.run(reload_rows(sync_utils.table, simple_db, table_information)))

	# 'close' implementation varies
	current_window.listen('tauri://close-requested', lambda _: aio.run(table_utils.close_window()))

	# ---
	# import: file_data.py & db_data.py (doesn't apply to history, edits or db_tables)
	current_window.listen('import_data', lambda payload: add_new_data_deb(payload))

	if not table_utils.is_mac():
		# TBD: test on Windows & Linux
		window.bind('keydown', lambda event: (aio.run(on_shortcut(event))))
	# else: macOS handles this via the menu item's keyboard shortcut
	# ^^^^^ import ^^^^^

	# refactor TBD: window_title vs. sync_utils.window_title
	sync_utils.window_title = await current_window.title()
	sync_utils.window_label = current_window.label

	document['show-hidden-checkbox'].bind('change', lambda e: sync_utils.update_row_visibility(e, sync_utils.table, table_type))
	document['edit-layout'].bind('click', lambda e: aio.run(sync_utils.open_layout_editor(e)))
	document['edit-sort'].bind('click', lambda e: aio.run(sync_utils.open_sort_editor(e)))
	document['edit-search'].bind('click', lambda e: aio.run(sync_utils.open_search_editor(e)))
	document['export-btn'].bind('click', lambda e: aio.run(sync_utils.open_export_editor(e)))
	document['export-btn'].removeAttribute('disabled')  # TBD: compare across similar files

	document['select-all-btn'].bind('click', lambda event: sync_utils.select_all_checkboxes())
	document['select-none-btn'].bind('click', lambda event: sync_utils.deselect_all_checkboxes())
	document['hide-btn'].bind('click', lambda event: sync_utils.main_tabulator_row_toggle_visibility(event, sync_utils.table))

	# csv/txt and DB ==> file_data.py & db_data.py
	document['edit-column'].bind('click', lambda e: aio.run(sync_utils.open_column_editor(e)))  # 'Define'
	document['insert'].bind('click', lambda event: aio.run(insert_new_row(key_info['simple_db'], key_info['tabulator'], key_info['table_info'])))

	# TBD: add delete-btn; see file_data.py

	# history.py adds table_history_update listener here -- or maybe above?

# ^^^^^ END: main()

# SQLite TBD: implement for SQLite data i.e. in db_data.py (ideally refactored to call the same function)
# see file_data.py
# def custom_editor(cell, onRendered, success, cancel, editorParams):

# -----
# sync between DEFINE (column editor) and data table
# (only applies to the csv/txt and (when implemented) DB data tables i.e. file_data.py & db_data.py)

definitions_changed_DEBOUNCE = table_utils.debounce(lambda payload: aio.run(sync_utils.definitions_changed(payload, sync_utils.table, table_type, create_table)),1000)

current_window.listen('change_table_columns', lambda payload: definitions_changed_DEBOUNCE(payload))

# new row/record
# (only applies to the csv/txt and (when implemented) DB data tables i.e. file_data.py & db_data.py)
# menu_new_row_DEBOUNCE = table_utils.debounce(lambda payload: menu_new_row(payload), 2000)
# current_window.listen('add_new', lambda payload: menu_new_row_DEBOUNCE(payload))

#........................end.............................

# Scott TBD July: probably get rid of these too
def update_main_table_when_recreate(evt):
	sync_utils.on_table_data_loaded(evt, sync_utils.table)


def save_loaded_data(evt):
	sync_utils.on_table_data_loaded_init(evt, sync_utils.table)


def execute_save_loaded_data(evt):
	window.setTimeout(lambda: save_loaded_data(evt), 50)


# create table
def create_table(tabulator_data, is_initial=True):

	deeply_utils.pp('db_data.py - create_table - is_initial', is_initial)

	visibility_dict = {}  # true/false lookup table by column name; default TRUE -- TBD: rename to visibility_dict

	if sync_utils.table:
		# this should correspond to is_initial FALSE but perhaps there are exceptions
		# some changes require rebuilding the table; this preserves then later restores checked/hidden
		sync_utils.save_row_states(sync_utils.table)  # preserve checked or hidden rows; not in 1.0 -- refactor TBD: redundant with sync_utils?
		sync_utils.table.destroy()
		sync_utils.table = None

	if is_initial:
		# header_data column dicts is (likely?!?) empty ==> build from scratch

		# comparing similar code elsewhere: tabulator_data param vs. tabulator_data
		sync_utils.columns = [key for key in dict(sync_utils.table_data[0]).keys() if key not in table_utils.built_in_fields]

		# is_customer_data=False for (history, edits, actions, db_tables)
		sync_utils.columns = table_utils.add_internal_column_names(sync_utils.columns, is_customer_data=True, with_details_icon=True)

		for col in sync_utils.columns:
			sync_utils.search_options_dropdown[col] = 'like'

	else:
		# already have header_data ==> build from there
		sync_utils.columns = []
		for item in sync_utils.header_data:
			sync_utils.columns.append(item['name'])
			visibility_dict[item['name']] = item['visible']

	# FYI: column_dicts is essentially new_header_data
	column_dicts = table_utils.make_columns(sync_utils, visibility_dict)
	table_utils.add_column_details(column_dicts)  # SQLite TBD: make editable, i.e. add editor_CALLBACK=custom_editor

	# final_data = tabulator_data
	# include_details_icon=True currently only for user data (csv/txt or DB); should add READ ONLY for history & edits
	final_data = table_utils.get_extra_data(tabulator_data, False, hidden_rows=sync_utils.hidden_rows, checked_rows=sync_utils.checked_rows, include_details_icon=True)

	# for Delete button ==> only csv/txt and (when implemented) SQLite data
# 	if app_config.is_paid_or_trial:
# 		selectable_rows = 1  # or True for any number, False for 0
# 	else:
# 		selectable_rows = False  # might add 1 in future for cmd-space to open READ ONLY detail view

	sync_utils.table = window.Tabulator.new(
		'#table',
		{
			'height': 'auto',
			# 'rowHeight': 24, # rowHeight prevents text from wrapping onto multiple rows ==> don't use!
			'pagination': True,
			'paginationSize': table_utils.pagination_default,
			'paginationSizeSelector': table_utils.pagination_options,
			'paginationButtonCount': table_utils.pagination_buttons,
			'data': final_data,
			'columns': column_dicts,
			'movableColumns': True,
			'editorParams': {'elementAttributes': {'spellcheck': False}},
			'debugInvalidOptions': False,
			'debugInvalidComponentFuncs': False,
			'debugInitialization': False,
			# placeholder only needed for history & edits
			# 'selectableRows': selectable_rows,  # for Delete button ==> only csv/txt and (when implemented) SQLite data
		},
	)

	sync_utils.table.on('tableBuilt', lambda: (
		# print('db_data.py -- create_table: table built'),
		table_utils.update_shown_entries(None, sync_utils.table, table_type),

		sync_utils.table.on('columnMoved', lambda column, columns: sync_utils.on_table_column_moved(column,columns, sync_utils.table)),
		sync_utils.table.on('columnResized', lambda column: sync_utils.column_width_updated(column)),
		sync_utils.table.on('dataSorted', lambda sorters, rows: sync_utils.on_table_data_sorted(sorters, rows, sync_utils.table)),
		sync_utils.table.on('headerClick', lambda event, column: sync_utils.on_header_click(event, column, sync_utils.table)),
		sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table)),

		# open details window
		sync_utils.table.on('cellClick', lambda e, cell: aio.run(on_cell_click(e, cell, table_information))),
		))

	# documentation TBD: why here AND after tableBuilt
	sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table))

	if is_initial:
		sync_utils.table.on('dataLoaded', execute_save_loaded_data)
	else:
		sync_utils.table.on('dataLoaded', update_main_table_when_recreate)

		# restore_row_states is for checked or hidden rows; not in 1.0
		window.setTimeout(lambda: sync_utils.restore_row_states(sync_utils.table, sync_utils.table_data), 50)

	window.tabulator = sync_utils.table


#...........................................end...........................................

# edits.py & db_data.py
async def get_rows(simple_db, table_info):
	# globals not modified here: table_name

	blob_fields = set()
	for column_info_obj in table_info:
		column_info_dict = dict(column_info_obj)  # convert from JSObject
		if column_info_dict.get('type') == 'BLOB':
			blob_fields.add(column_info_dict['name'])

	# performance TBD: can we LIMIT to 100 for initial display then page additional records for search/sort?
	rows = await simple_db.get_table_data(table_name)  # row_objects

	# performance TBD: likely slow for large dataset; could it be added on-the-fly for rows shown in the UI?
	# add icon_link_out and mask BLOB fields
	for i in range(len(rows)):
		row_dict = OrderedDict(dict(rows[i]))  # convert from JSObject; move_to_end requires OrderedDict

		row_dict[table_utils.details_icon_field] = app_data.icon_link_out
		row_dict.move_to_end(table_utils.details_icon_field, last=False)  # OrderedDict move_to_end with last=False means move_to_START
		# ^^^ the above is consistent with similar code though in this case the move is done AGAIN later after adding Foreign Key columns and sorting that

		# mask BLOB fields (if any)
		for key in blob_fields:
			row_dict[key] = '(BLOB)'

		row = __BRYTHON__.pyobj2jsobj(dict(row_dict))  # Convert back to JSObject
		rows[i] = row

	return rows

# ===================================================

# csv/txt & DB rows (file_data.py & db_data.py): click to open details window
# TBD: add to edits & history

async def on_cell_click(event, cell, table_info):
	field = cell.getField()
	if not field == table_utils.details_icon_field:  # '(details icon)'
		return

	row_dict = dict(cell.getRow().getData())

	# print('clicked open details icon', field);
	label = tauri.window.getCurrentWindow().label

	try:
		primary_key = get_pk_name(table_info)
		row_id = row_dict[primary_key]
		args = {
			'parentLabel': label,
			'primaryKey': primary_key,
			'rowId': str(row_id),
			'table': table_name,
			'path': db_path,
			}
		await tauri.invoke('open_row_details', args)
	except Exception as e:
		print('ERROR in db_data.py -- on_cell_click -- trying to open details window:', e)


async def menu_new_row(tauri_message):
	# globals not modified here: key_info

	current_window = tauri.window.getCurrentWindow()

	# documentation TBD: why different behavior by platform?
	if table_utils.is_mac():
		label = tauri_message.payload.label
		if label == current_window.label:
			await insert_new_row(key_info['simple_db'], key_info['tabulator'], key_info['table_info'])
	else:
		insert_id = tauri_message.payload.insert_id
		if insert_id == INSERT_ID:
			await insert_new_row(key_info['simple_db'], key_info['tabulator'], key_info['table_info'])

deb = table_utils.debounce(lambda payload: aio.run(menu_new_row(payload)), 2000)  # TBD: can probably simplify

# -----
# specific to db_data.py?

# from import dialog: add new data
async def add_new_data(tauri_message):
	# print('db/db_data.py: add_new_data(tauri_message)', tauri_message)
	data_to_insert = json.loads(dict(tauri_message)['payload'])

	sync_utils.table_data.extend(data_to_insert)  # add rows
	sync_utils.save_row_states(sync_utils.table)  # save checked/hidden to restore ... where/when? -- TBD: is that done elsewhere?

	create_table(sync_utils.table_data, is_initial=False)  # re-build the table in similar fashion

	sync_utils.bind_header_filter_events(sync_utils.table)

add_new_data_deb = table_utils.debounce(lambda payload: aio.run(add_new_data(payload)), 2000)  # TBD: can probably simplify


# -----
async def insert_new_row(simple_db, tabulator, table_info):
	# TBD: align file_data.py & db_data.py
	# refactor TBD: doesn't actually insert ==> rename and doesn't need simple_db param or tabulator param

	if not app_config.is_paid_or_trial:
		table_utils.notify_user()
		return

	# refactor TBD: any advantage of separate variables?
	label = tauri.window.getCurrentWindow().label  # or: current_window may be global ... does this require await?
	primary_key = get_pk_name(table_info)

	args = {
		'parentLabel': label,
		'primaryKey': primary_key,
		'table': table_name,
		'path': db_path,
		}
	await tauri.invoke('open_new_row_window', args)


async def on_shortcut(event):
	# SEE ALSO: on_shortcut in shortcuts.py
	shortcut_action = table_utils.get_shortcut_action(event)

	if shortcut_action == 'open':
		await open_file.open_file(from_menu=True)

	# TBD adapted from file_data.py but this insert_new_row has params ... and menu_new_row requires payload
# 	elif shortcut_action == 'new row' and app_config.is_paid_or_trial:
# 		insert_new_row()

	# TBD: implement 'save' for SQLite ... may have to mark edits as 'temporary' or some such
# 	elif shortcut_action == 'save' and app_config.is_paid_or_trial:
# 		# will NOT look like saving text data: await save_utils.overwrite_file({}, current_window.label)


window.bind('keydown', lambda e: ((e.stopImmediatePropagation(), aio.run(on_shortcut(e)))))


# ===================================================
# refactor TBD: move to db_utils


def get_pk_name(table_info):
	# extract pk name if defined in table_info, otherwise infer

	if not table_info:
		print('ERROR: missing table_info')
		return ''

	# print('edits.py - get_pk_name - table_info', table_info)
	primary_key_object = next((field for field in table_info if field['pk'] == 1), None)

	if not primary_key_object:
		# assume first SERIAL is PK
		primary_key_object = next((field for field in table_info if field['type'].upper() == 'SERIAL'), None)

	if not primary_key_object:
		# assume first INTEGER is PK
		primary_key_object = next((field for field in table_info if field['type'].upper() == 'INTEGER'), None)

	if not primary_key_object:
		print('TBD: handle this case: still no primary_key_object after 3 attempts', table_info)
		return ''

	return primary_key_object['name']


def sort_with_foreign_keys(row: dict):
	sorted_keys = []

	for key in sorted(row.keys()):
		if key.endswith(' - FK'):
			continue
		if f'{key} - FK' in row:
			sorted_keys.append(f'{key} - FK')
		sorted_keys.append(key)

	new_row = {key: row[key] for key in sorted_keys}

	return new_row


async def prepare_rows_with_fk(simple_db, rows: List[dict]) -> List[dict]:
	src_fk_info = await simple_db.get_fk_info(table_name)
	src_fk_info = sorted(src_fk_info, key=lambda i: i['from'])

	# foreign keys + primary key names caching for tables

	for i in range(len(rows)):
		row = rows[i]  # row_obj?
		for fk_info in src_fk_info:
			fk_info = dict(fk_info)

			src_field = fk_info['from']
			src_value = row[src_field]
			dst_table = fk_info['table']
			dst_field = fk_info['to']

			# Get destination table info and store in cache
			dst_fk_info = await simple_db.get_fk_info(dst_table)
			dst_info = await simple_db.get_table_info(dst_table)

			dst_special = []
			# add primary keys
			for dst_row in dst_info:
				dst_row = dict(dst_row)
				if dst_row['pk'] == 1:
					dst_special.append(dst_row['name'])
			# add foreign keys
			for dst_row in dst_fk_info:
				dst_row = dict(dst_row)
				name = dst_row['from']
				dst_special.append(name)

			# Get foreign friendly value

			if src_value:
				dst_rows = await simple_db.get_rows_where(dst_table, dst_field, src_value)
			else:
				dst_rows = []

			if dst_rows:
				sample_row = dict(dst_rows[0])
				fields = sample_row.keys()
				fields = [key for key in fields if key not in dst_special]
				if fields:
					field = fields[0]
					src_value = str(src_value).lower()
					friendly_columns = await db_utils.get_friendly_fk_columns(simple_db, fk_info['table'])
					column_name = f'{src_field} - FK'
					friendly_value = ' '.join(sample_row.get(column, '') for column in friendly_columns)
					row[column_name] = friendly_value

			new_row = sort_with_foreign_keys(dict(row))

			# Keep icon first
			new_row = OrderedDict(new_row)
			new_row.move_to_end(table_utils.details_icon_field, last=False)  # OrderedDict move_to_end with last=False means move_to_START

			rows[i] = __BRYTHON__.pyobj2jsobj(dict(new_row))  # Convert back to JSObject

	return rows  # row_objects

sync_utils.setup_window_event_handlers(current_window, create_table, get_table)  # window object + 2 callbacks

page_runner.register_for_js()  # hardcoded for 'main'
