use chrono::{DateTime, Local, NaiveDateTime, ParseError, Utc};

#[allow(unused)]
/// Format chrono date to sqlite format UTC
pub fn format_date_for_sqlite(date: &chrono::NaiveDateTime) -> String {
    date.format("%Y-%m-%d %H:%MZ").to_string()
}
#[allow(unused)]
/// Format chrono date to sqlite format local time
pub fn format_local_date_for_sqlite(date: &chrono::NaiveDateTime) -> String {
    date.format("%Y-%m-%d %H:%M").to_string()
}
#[allow(unused)]
/// Get date string in sqlite format UTC
pub fn get_date_utc() -> String {
    let current_time = Utc::now();
    format_date_for_sqlite(&current_time.naive_utc())
}

#[allow(unused)]
/// Get date string in sqlite format local time
pub fn get_date_local() -> String {
    let current_time = Local::now();
    format_local_date_for_sqlite(&current_time.naive_local())
}

#[allow(unused)]
/// Parse sqlite date from "%Y-%m-%d %H:%MZ" into chrono datetime
pub fn parse_sqlite_date(date: &str) -> Result<DateTime<Utc>, ParseError> {
    // Attempt to parse the date string into a NaiveDateTime
    let naive_datetime = NaiveDateTime::parse_from_str(date, "%Y-%m-%d %H:%MZ")?;

    // Convert the NaiveDateTime to DateTime<Utc>
    let datetime_utc = DateTime::from_naive_utc_and_offset(naive_datetime, Utc);

    Ok(datetime_utc)
}
