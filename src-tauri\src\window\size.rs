use crate::config::WINDOW_SIZE_MAP;

/// Get window size from config or fallback to default
impl WINDOW_SIZE_MAP {
    pub fn get_width(&self, mut window_name: &str) -> f64 {
        // handle dynamic windows
        if window_name.starts_with("database_explorer_details") {
            window_name = "database_explorer_details";
        }

        self.get(window_name).map_or(1440.0, |&size| size.0)
    }

    pub fn get_height(&self, mut window_name: &str) -> f64 {
        if window_name.starts_with("database_explorer_details") {
            window_name = "database_explorer_details";
        }
        self.get(window_name).map_or(900.0, |&size| size.1)
    }
}
