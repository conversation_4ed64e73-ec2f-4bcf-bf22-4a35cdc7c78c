use crate::{config, utils};
use anyhow::Result;
use base64::{engine::general_purpose, Engine as _};
use chrono::{DateTime, Duration, Utc};
use libaes::Cipher;
use machineid_rs::{Encryption, HWIDComponent, IdBuilder};
use serde::{Deserialize, Serialize};
pub mod popup;

#[cfg(target_os = "macos")]
mod macos;
#[cfg(target_os = "macos")]
pub use macos::{activate, get_info, update_popup_date};

#[cfg(target_os = "windows")]
mod windows;

#[cfg(target_os = "windows")]
pub use windows::{activate, get_info, update_popup_date};

// Windows
// %localappdata%\<random filename with AES encrypted date>
// %appdata%\<random filename with AES encrypted date>
// HKEY_CURRENT_USER\Software\{Product Name}\{<random key>: <AES encrypted date>}

// MacOS
// /Users/<USER>/Library/Application Support/<random filename with AES encrypted date>
// /Users/<USER>/Library/Preferences/<random filename with AES encrypted date>

// if one of the file missing, trial invalid
// if all files missing, trial is started

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")] // use camelCase in JS but I prefer snake_case if possible
pub struct TrialInfo {
    active_date: String,
    last_popup_date: Option<String>,
}

impl TrialInfo {
    pub fn time_since_popup(&self) -> Result<Option<Duration>> {
        // return true if 24 hours passed since last popup
        let now: DateTime<Utc> = Utc::now();
        if let Some(last) = self.last_popup_date.clone() {
            let date = utils::parse_sqlite_date(&last)?;
            let duration = now.signed_duration_since(date);
            return Ok(Some(duration));
        }
        Ok(None)
    }

    pub fn time_since_activate(&self) -> Result<Duration> {
        // return how many trial days passed
        let now: DateTime<Utc> = Utc::now();
        let date = utils::parse_sqlite_date(&self.active_date)?;
        let duration = now.signed_duration_since(date);
        Ok(duration)
    }

    pub fn trial_still_active(&self) -> bool {
        let duration_since_active = self.time_since_activate();
        if let Ok(duration) = duration_since_active {
            if duration.num_days() < 31 {
                // trial valid
                return true;
            }
        }
        false
    }
}

/// Returns unique hardware ID
/// Unique per app identifier (in tauri.conf.json)
fn get_app_hwid(app: &tauri::AppHandle) -> Result<String> {
    let hwid = IdBuilder::new(Encryption::MD5)
        .add_component(HWIDComponent::CPUCores)
        .add_component(HWIDComponent::SystemID)
        .build(&app.package_info().name)?;
    Ok(hwid)
}

fn encrypt(data: &str) -> String {
    let cipher = Cipher::new_128(config::TRIAL_DATE_ENC_KEY);
    let encrypted = cipher.cbc_encrypt(config::TRIAL_DATE_ENC_IV, data.as_bytes());
    general_purpose::STANDARD.encode(encrypted)
}

fn decrypt(data: &str) -> Result<String> {
    let cipher = Cipher::new_128(config::TRIAL_DATE_ENC_KEY);
    let decoded = general_purpose::STANDARD.decode(data)?;
    let decrypted = cipher.cbc_decrypt(config::TRIAL_DATE_ENC_IV, &decoded);
    let plain = std::str::from_utf8(&decrypted)?;
    log::debug!("decrypted trial into {}", plain);
    Ok(plain.to_owned())
}
