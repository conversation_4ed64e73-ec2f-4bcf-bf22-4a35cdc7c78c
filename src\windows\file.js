function not_like_filter(search_value, cell_value, params) {
	// console.log('not like', 'search_value:', "${search_value}", search_value.length, 'chars');
	// console.log('not like', 'cell_value:', "${cell_value}", cell_value.length, 'chars');

	// special case: 'not like (empty string)' to match any non-empty cell
	if (search_value == '' && String(cell_value) != '') {
		return true;
	}
	return !String(cell_value).toLowerCase().includes(search_value.toLowerCase());
}

function any_filter(search_value, cell_value, params) {
	// mimic Tabulator keywords filter
	if (search_value === '') {
		return false;
	}

	let cell_value_lower = String(cell_value).toLowerCase();

	let keywords = search_value.split(' ');
	for (let i = 0; i < keywords.length; i++) {
		if (cell_value_lower.includes(keywords[i].toLowerCase())) {
			return true;
		}
	}

	// if it gets here
	return false;
}

function not_any_filter(search_value, cell_value, params) {
	// was omit_keywords_filter
	if (search_value === '') {
		return true;
	}

	let cell_value_lower = String(cell_value).toLowerCase();

	let keywords = search_value.split(' ');
	for (let i = 0; i < keywords.length; i++) {
		if (cell_value_lower.includes(keywords[i].toLowerCase())) {
			return false;
		}
	}

	// if it gets here
	return true;
}

// -----
function regex_filter(search_value, cell_value, params) {
	// NOT case sensitive
	if (search_value === '') {
		return true;
	}
	let regex = new RegExp(search_value, 'i')
	let results = String(cell_value).search(regex);
	if (results != -1) {
		return true;
	}
	return false;
}

function case_regex_filter(search_value, cell_value, params) {
	// case sensitive
	if (search_value === '') {
		return true;
	}
	let regex = new RegExp(search_value)
	let results = String(cell_value).search(regex);
	if (results != -1) {
		return true;
	}
	return false;
}

function not_regex_filter(search_value, cell_value, params) {
	// NOT the pattern and NOT case sensitive
	if (search_value === '') {
		return true;
	}
	let regex = new RegExp(search_value, 'i')
	let results = String(cell_value).search(regex);
	if (results === -1) {
		return true;
	}
	return false;
}

function not_case_regex_filter(search_value, cell_value, params) {
	// cs = case sensitive of NOT the pattern
	if (search_value === '') {
		return true;
	}
	let regex = new RegExp(search_value)
	let results = String(cell_value).search(regex);
	if (results === -1) {
		return true;
	}
	return false;
}

// -----
function checkbox_formatter(cell, formatterParams) {
	let checkbox = document.createElement('input');
	checkbox.type = 'checkbox';
	checkbox.checked = cell.getRow().getData()['(checked)'];
	checkbox.onchange = (evt) => {
		cell.getRow().update({ '(checked)': checkbox.checked });
		formatterParams['elementAttributes']['onchange']();
	};
	return checkbox;
}

// -----
Tabulator.extendModule('filter', 'filters', {
	not_like: not_like_filter,

	any: any_filter,  // different name for Tabulator's 'keywords' filter, but seems to need its own script
	not_any: not_any_filter,

	regex: regex_filter,
	not_regex: not_regex_filter,

	case_regex: case_regex_filter,
	not_case_regex: not_case_regex_filter,
});

Tabulator.extendModule('format', 'formatters', {
	checkbox: checkbox_formatter,
});
console.log("Adding modules")
