/* row-details-page & import-dialog */

.tabs {
	width: 100%;
}

.navigate_table {
	margin-left: auto;
	display: flex;
}

td, th {
	border-bottom: 1px solid #dddddd;
	text-align: left;
	padding-left: 8px;
	padding-right: 8px;
	padding-top: 3px;
	padding-bottom: 3px;
	vertical-align: top;
}

#import-dialog td {
	border: none;
}

.generated label.suffix-label  {
	padding-left: 0.5rem;
}

#row-details-page .fieldname {
	width: 10rem;
}

.fieldname {
	font-weight: bold;
}

.import_fieldname {
	font-family: var(--fixed_family);
}

fieldset {
	border: none;
	margin: 0;
	padding: 0;
}

/*
select {
	font-size: 1.2em;
}
 */
