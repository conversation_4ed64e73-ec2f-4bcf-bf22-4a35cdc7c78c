use tauri::{App<PERSON><PERSON><PERSON>, Window};

use crate::edit1::get_label;

#[tauri::command]
pub async fn open_matchignore(handle: AppHandle, _window: Window, label: String) {
    let matchignore_label = format!("{label}matchignore{}", get_label());
    let _matchignore = tauri::WebviewWindowBuilder::new(
        &handle,
        &matchignore_label,
        tauri::WebviewUrl::App("src/edit1/ui/match-ignore-table/match-ignore-table.html".into()),
    )
    .title("Review")
    .inner_size(750.0, 400.0)
    .disable_drag_drop_handler()
    .maximizable(true)
    .minimizable(false)
    .focused(true)
    .center()
    .build()
    .unwrap();
}
