{"name": "edit_deeply", "private": true, "type": "module", "scripts": {"tauri": "tauri", "vite": "vite", "preview": "vite preview", "dev": "vite dev", "build": "vite build"}, "devDependencies": {"@tauri-apps/cli": "^2.0.4", "vite": "^6.0.3", "vite-plugin-static-copy": "^3.0.0"}, "dependencies": {"@codemirror/lang-css": "^6.2.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-markdown": "^6.2.5", "@codemirror/lang-python": "^6.1.6", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sql": "^6.6.4", "@codemirror/lang-xml": "^6.1.0", "@codemirror/legacy-modes": "^6.4.0", "@lezer/highlight": "^1.2.0", "@tauri-apps/api": "^2.0.3", "@tauri-apps/plugin-dialog": "^2.2.0", "@tauri-apps/plugin-os": "^2.2.1", "@tauri-apps/plugin-process": "^2.2.0", "@tauri-apps/plugin-store": "^2.1.0", "codemirror": "^6.0.1", "ignore": "^5.3.2", "path": "^0.12.7", "prettier": "^3.3.2", "serve-static": "^2.2.0", "sharp": "^0.34.2", "tabulator-tables": "^6.2.1", "tauri": "^0.15.0", "thememirror": "^2.0.1"}}