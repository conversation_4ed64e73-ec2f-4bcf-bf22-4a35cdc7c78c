"""
file.py - a companion to file_data.py
	small overlap with code in db_tables.py: update history on close

refactor TBD: update along with file_data.py
refactor TBD: move this code elsewhere
"""

from browser import aio, document, timer, window
import tauri


async def on_window_close():
	currentWindow = tauri.window.getCurrentWindow()
	await tauri.event.emit('table_history_update')  # only file.py (file_data.py) & db_tables.py?
	currentWindow.destroy()


currentWindow = tauri.window.getCurrentWindow()
currentWindow.listen('tauri://close-requested', lambda *_args: aio.run(on_window_close()))
