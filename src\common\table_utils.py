"""
table_utils.py - not just for tables

refactor TBD: move static variables to app_data.py, including lists of fields

refactor TBD: MAYBE create app_utils.py (sorts first) or static_utils.py and move much of this code there
	probably ONLY move fully static scripts i.e. if require app_config then leave here
- create_label
- set_title
- (etc.)

LEAVE HERE: table code
- make_columns and the several functions it calls
- add_internal_column_names
- (maybe others)
"""

from browser import aio, document, timer, window
from collections import OrderedDict
import javascript
import json
import re
import tauri

# local imports
import app_data
import deeply_utils

app_config = None  # must be loaded async -- it's set by caller

# -----
default_width = 46  # documentation TBD: why this value? and why a default for hidden fields?
handle_width = 36  # the 3-bar drag icon in sort & layout dialogs
icon_column_width = 30  # icon is 18px; this provides enough left/right whitespace
search_sort_row_height = 30  # was 36
layout_define_row_height = 30

# filtered names; see fieldname_filter below

# here are several hardcoded / arbitrary categories as ad hoc way to improve formatting for a few specific cases
# ... should add more over time
# ... though of course this will sometimes make things works
blob_fields = ['picture', 'jsonb', 'blob']
not_editable = ['created', 'updated']  # hardcoded / arbitrary
textarea_editable = [
	'column_names',  # field in our SQLite db_tables
	'data',
	'description',
	'external_json',
	'html',
	'json',
	'my_notes',  # tapx1
	'note',
	'notes',
	'raw_data',
	'text',
	]
wide_editable = [
	'homepage',
	'link',
	'my_summary',  # tapx1
	'subtitle',
	'title',
	'url',
]
# ^^^^^ END: hardcoded / arbitrary ^^^^^

hidden_columns = ['date_opened', 'realpath', 'webview_label'] # 'kind'

details_icon_field = '(details icon)'  # icon button
new_window_icon_field = 'new window'  # refactor TBD: '(new window)'
restore_window_icon_field = 'restore window'  # bring to front or un-minimize; refactor TBD: '(restore window)'
row_checked = '(checked)'
row_hidden = '(hidden)'
row_visibility = 'row_visibility'  # see notes elsewhere; may not be useful if it's always 'not row_hidden'
row_selector = '(row_selector)'  # active row for Delete and future cmd-space to open details, Duplicate

# internal fields added to SQLite cache; parens added for display
file_row = '_file_row'  # nth row of original file (plus ID for rows added by user); AND internal ID for hiding rows
mod_date = '_mod_date'
mod_note = '_mod_note'

internal_db_fields = [
	file_row,
	mod_date,
	mod_note,
	]

pagination_options = [10, 20, 25, 50, 75, 100, True]  # true for an 'all' option
pagination_default = 20
pagination_buttons = 9  # max number of buttons in the footer

checkbox_fields = [
	row_checked,
	row_hidden,
	]

window_icon_fields = [
	new_window_icon_field,
	restore_window_icon_field,
	]

icon_columns = [
	details_icon_field,
	] + window_icon_fields

built_in_fields = [  # internal / hidden fields
	file_row,
	row_visibility,  # see notes elsewhere
	row_selector,
	mod_date,
	mod_note,
	] + checkbox_fields + icon_columns  # including new_window, restore_window

exclude = icon_columns + [row_visibility]  # icons (and empty row_visibility column)

PROPS = None
try:
	PROPS = dict(window['__PROPS__'])
except Exception as e:
	print('ERROR in table_utils: could not get __PROPS__', e)


# ----- good idea but never successfully called Tauri; got undefined undefined
# def emit(target_window_label, event_name, payload_dict):
# 	"""Fire-and-forget emit to another window - listener handles the event"""
#
# 	deeply_utils.pp('emit', target_window_label, event_name, payload_dict)
# 	assert isinstance(payload_dict, dict), payload_dict
#
# 	print('call get_open_windows')
# 	tauri.invoke('get_open_windows').then(
# 		lambda windows: print([f'{w}' for w in windows])
# 	)

# 	def handle_error(err):
# 		window.console.error("Emit error:", err)
# 		deeply_utils.pp("Emit error details:", str(err))
#
# 	# with .catch to handle the promise
# 	tauri.invoke('emit_to_window', {
# 		'target_window_label': target_window_label,
# 		'event_name': event_name,
# 		'payload': json.dumps(payload_dict),
# 	}).catch(handle_error)

# 	tauri.invoke('emit_to_window', {
# 		'target_window_label': 'history',
# 		'event_name': 'test_event',
# 		'payload': '{"test": "data"}',
# 	}).then(
# 		lambda _: deeply_utils.pp("Emit successful")
# 	).catch(
# 		lambda err: deeply_utils.pp(f"Emit failed: {err}")
# 	)
#
# 	json_args = json.dumps({
# 		'target_window_label': target_window_label,
# 		'event_name': event_name,
# 		'payload': payload_dict  # Don't double-encode
# 	})
#
# 	tauri.invoke('emit_to_window', {'json_args': json_args}).catch(
# 		lambda err: deeply_utils.pp(f"emit_to_window error: {err}")
# 	)
#
#
# def emit_args(event_name, args):
# 	# hack to minimize code changes: process existing args param
#
# 	target_window_label = args.pop('label')
#
# 	assert len(args) == 1, args
# 	key, value = next(iter(args.items()))
#
# 	assert isinstance(value, str), value
#
# 	deeply_utils.pp('emit_args, event_name, args after POP', event_name, args)
#
# 	return tauri.invoke('emit_to_window', {
# 		'target_window_label': target_window_label,
# 		'event_name': event_name,
# 		'payload': json.dumps(args),
# 	})


# -----
# might be useful at some point but not currently in use
# def run_tauri_invoke(command, args=None):
# 	"""Synchronously invoke a Tauri command and return the result"""
# 	async def _invoke():
# 		return await tauri.invoke(command, args)
#
# 	return aio.run(_invoke())


async def invoke_tauri(what, args, source='', debug=False):
	# handle errors

	if debug:
		try:
			print('Checking if tauri is available...')
			print('… tauri object:', tauri)
			print('… tauri.invoke:', tauri.invoke)
			print('… type(tauri):', type(tauri))
		except Exception as e:
			print(f'ERROR: tauri not available: {e}')

		if source:
			print('… invoke_tauri FROM %s WITH %s and ARGS:' % (source, what))
		else:
			print('… invoke_tauri WITH %s and ARGS:' % what)

		deeply_utils.pp(args)  # caveat: this could be huge, e.g. if SQL INSERT

	try:
		result = await tauri.invoke(what, args)
		if debug:
			print('… RESULT:', result)  # warning: could be large!
		return result
	except Exception as e:
		if not debug:
			# i.e. did NOT print details above so important to print here as context for the error
			print('invoke_tauri -- %s:' % source, what)
		print('ERROR in invoke_tauri:', e)
		# not sure the best return value


# convenience function
# refactor TBD: probably either call it everywhere or get rid of this and revert to direct tauri call
def get_current_window():
	return window['__TAURI__'].window.getCurrentWindow()


async def sure_subfolder(child):
	# make sure the subfolder exists; return the full path
	# 'child' is 'cache' or 'backup'

	# refactor TBD: should ensure at every launch that /cache/ and /backup/ exist then get rid of this


	# (call Rust since not all Python os features are implemented in Brython)
	# the Rust code creates intermediate paths as needed, though that shouldn't be required
	status = await tauri.invoke('create_folder', {'folderPath': path})

	if status:
		return path
	else:
		window.alert('Could not create folder.')
		return None


def fieldname_filter(value):
	# turn 'friendly' label into internal name
	filtered_name = value.replace(' ', '_').lower()

	with_underscore = '_' + filtered_name

	if with_underscore in built_in_fields:  # though internal_db_fields would suffice for now
		return with_underscore
	else:
		return filtered_name


def get_extra_data(initial_data, is_customer_data=False, hidden_rows=[], checked_rows=[], include_details_icon=False):
	# add data for internal columns/fields: check, visible, hidden
	# ... and link_out icon if appropriate

	# globals not modified here: app_config, row_visibility

	final_data = []
	index = 1  # 1-based row number for hidden_rows

	for data_item in initial_data:
		hidden = (index) in hidden_rows
		checked = (index) in checked_rows
		data_dict = OrderedDict(dict(data_item))  # documentation TBD: why isn't data_item already an OrderedDict?

		if include_details_icon:  # TBD: add to history and others after implementing READ ONLY version
			data_dict[details_icon_field] = app_data.icon_link_out  # to details window

		data_dict[row_checked] = checked
		data_dict[row_visibility] = not hidden  # see notes elsewhere; may not be useful if it's always 'not row_hidden'
		data_dict[row_hidden] = hidden

		if app_config.is_paid_or_trial:
			data_dict[row_selector] = ''

		if not is_customer_data:
			# file_row for (history, edits, actions, db_tables) is added to support hiding rows
			data_dict[file_row] = index
			index += 1

		final_data.append(dict(data_dict))

	return final_data


def on_cell_click(event, cell):
	# for now: clicking on the entire 'details icon' cell activates it (vs. just on the icon)
	# tradeoff: easier if that's the intent, but then can't use empty part of the cell to select the entire row (for delete or post 1.0 hide)

	field = cell.getField()
	filename = PROPS['name']
	# cell.getRow().toggleSelect();
	if field == details_icon_field:
		# try:
		row_index = str(cell.getRow().getCell(file_row).getValue())  # internal primary key
		table_name = filename if filename else tauri.window.getCurrentWindow().label
		args = {
			'rowIndex': row_index,
			'tableName': table_name,
			'path': PROPS['path'],
			}
# 		except Exception as e:
# 			print('Error in on_cell_click:', dict(e))

		# print('open details clicked', table_name, row_index)
		aio.run(invoke_tauri('pivot_row_to_edit', args, 'on_cell_click'))


def make_changed_message(changes_count, CREATING_NEW_ROW=False):
	# handle new row & singular/plural changes
	if CREATING_NEW_ROW:
		return 'Save new row?'

	if changes_count == 1:
		return '1 value changed'
	else:
		return f'{changes_count} values changed'


async def close_window():
	# SEE ALSO: close_modal_window(main_window_label) for modal dialogs: Export & Define
	await tauri.event.emit('file_tables_update')  # documentation TBD: summarize
	current_window = tauri.window.getCurrentWindow()
	current_window.destroy()


def update_shown_entries(n_rows=None, table=None, table_type=None):
	# i.e. update_n_of_m
	suffixes = {
		'csv_tsv': 'rows', # including json & html
		'db_table': 'records',
		'db_tables': 'tables',
		'edits_actions': 'records',
		'history': 'files',
		}
	element = document.querySelector('#shown-records')

	label = suffixes.get(table_type, 'items')  # default should never happen!

	total = len(table.getData())
	if total == 1:
		label = label[:-1]  # drop trailing 's'

	if n_rows is None or n_rows == 0 or n_rows == total:
		element.textContent = '%s %s' % (deeply_utils.format_number(total), label)
	else:
		element.textContent = '%s of %s %s' % (deeply_utils.format_number(n_rows), deeply_utils.format_number(total), label)


def debounce(func, delay):
	timeout_id = None

	def debounced_function(*args, **kwargs):
		nonlocal timeout_id
		if timeout_id is not None:
			timer.clear_timeout(timeout_id)
		timeout_id = timer.set_timeout(lambda: func(*args, **kwargs), delay)

	return debounced_function


def check_os(platform):
	# refactor TBD: this whole platform/using approach should be reviewed
	# e.g. Export dialog should default based on previous value with a fallback to platform (likely only at first launch)
	if window.navigator.platform.startswith('Win'):
		platform['using_windows'] = 'checked'
	else:
		platform['using_other'] = 'checked'


def is_mac():
	# refactor TBD: replace this with better data in updated app_config

	# # Check if the platform is macOS
	# print('table_utils.py -- is_mac -- window.navigator.platform.lower():', window.navigator.platform.lower())
	return window.navigator.platform.lower().startswith('mac')


def get_modifier():
	if is_mac():
		return 'Command'
	else:
		return 'Control'


# see menu.rs - accelerator("CommandOrControl+N") for macOS
# but doesn't work on windows/linux ==> handle here
# TBD: for Save & Open, should this be called in every window?
def get_shortcut_action(event):
	# cmd-N for New record/row; was cmd-I
	shortcut = False
	# print('check_modifier', event.key, event.ctrlKey)
	if event.metaKey or event.ctrlKey:
		if is_mac():
			deeply_utils.pp('THEORY: MacOS handles via menubar not this?!?')
# 			if event.metaKey and event.key.lower() == 'n':
# 				shortcut = 'new row'
		else:
			if event.ctrlKey and event.key.lower() == 'n':
				shortcut = 'new row'
			elif event.ctrlKey and event.key.lower() == 's':
				shortcut = 'save'
			# TBD: Save As
			elif event.ctrlKey and event.key.lower() == 'o':
				shortcut = 'open'
	# print('shortcut hit?', shortcut)
	return shortcut


def apply_all_caps_list(label):
	if ' ' in label:
		words = label.rsplit(' ', 1)
		new_words = []

		for word in words:
			if word.lower() in all_caps_list:
				word = word.upper()

			new_words.append(word)

		label = ' '.join(new_words)

	return label


def create_label(name):
	# turn actual column name into a friendly Mixed Case / multi-word label
	if name in internal_db_fields:
		# mimic row_checked and row_hidden: lowercase, spaces, parens
		# i.e. unlike user fields, do NOT convert to Title Case
		return '(%s)' % name.strip('_').replace('_', ' ')

	elif name in built_in_fields:
		return name

	elif name in mixed_case_pairs:
		# hardcoded pairs based on common / likely fieldnames
		return mixed_case_pairs[name]

	elif deeply_utils.is_mixed_case(name):
		label = name.replace('_', ' ')  # important: do NOT apply TitleCase if already mixed case

		return apply_all_caps_list(label)  # but do make some ALL CAPS fixes like ID

	else:
		label = name.replace('_', ' ').title()  # TitleCase i.e. capitalize every word

		return apply_all_caps_list(label)


async def disable_main_windows(main_window_label):
	current_window = tauri.window.getCurrentWindow()
	await tauri.event.emitTo(main_window_label, 'disable-window', {'by': current_window.label})

	## the list seems to be obsolete unless the commented-out code should be uncommented
# 	windows_to_disable = [
# 		main_window_label,
# 		f'{main_window_label}_search_dialog',
# 		f'{main_window_label}_sort_dialog',
# 		f'{main_window_label}_layout_dialog',
# 		]
	#for window_label in windows_to_disable:
		#await tauri.event.emitTo(window_label, 'disable-window')


async def close_modal_window(main_window_label):
	# Export & Define are modal ==> the data window is disabled ==> enable it
	# documentation TBD: what is 'main' here?
	# documentation TBD: the above close_window
	currentWindow = tauri.window.getCurrentWindow()
	await tauri.event.emitTo(main_window_label, 'enable-window')
	await currentWindow.destroy()


async def create_known_window(which):
	deeply_utils.pp('table_utils.py - create_known_window', which)
	# actions, edits, help, history
	# lightweight wrapper to avoid {'which': which} in every call
	await tauri.invoke('create_known_window', {'which': which})


async def open_window(window_title, window_label, url, options):
	# e.g. search/sort/layout dialogs, import, export, 'Define' column editor
	args = {
		'title': window_title,
		'url': url,
		'label': window_label,
		'options': options,
	}

	await invoke_tauri('create_document_window', args, 'open_window')

# --------------------------------------------
# many/most/all? of these will stay in this table_utils.py even after moving lots to app_utils.py

def prepare_icon_column(column, field):
	# render icon
	# called by add_column_details
	column.formatter = 'html'

	# remove filters and sort
	column.headerFilter = False

	if field == restore_window_icon_field:
		column.headerSort = True
	else:
		column.headerSort = False

	# hide column header
	column.title = ''

	# set width
	column.width = icon_column_width
	column.minWidth = icon_column_width  # this one seems to work
	column.maxWidth = icon_column_width


def add_window_restore_icons(row_dict, webview_label):
	"""
	Add new window / restore window icons to table

	for history & db_tables
	row_dict must be OrderedDict for move_to_end (to START)
	"""
	if webview_label:
		row_dict['webview_label'] = webview_label  # documentation TBD: summarize
		row_dict[restore_window_icon_field] = app_data.icon_restore
	else:
		row_dict['webview_label'] = ''  # Scott added 2025-07-11 to get around an error, though may need a different fix
		row_dict[restore_window_icon_field] = ''
	row_dict[new_window_icon_field] = app_data.icon_open

	# restore window should be first
	row_dict.move_to_end(new_window_icon_field, last=False)  # OrderedDict move_to_end with last=False means move_to_START
	row_dict.move_to_end(restore_window_icon_field, last=False)

	# works in place; nothing to return


def to_date_with_offset(cell, formatterParams, onRendered):
	# callback function for Tabulator; it provides formatterParams & onRendered
	return deeply_utils.to_date_with_offset(cell.getValue())


def add_column_details(column_dicts, editor_CALLBACK=None, is_history=False):
	# handle special cases, e.g. editable, blob, hidden, icons

	# refactor to CONSIDER: should most of this logic go into make_columns and apply when CREATING rather than iterating back thru every column and adding misc stuff? (Of course easy to add editor_CALLBACK=None, is_history=False params to that function.)

	for column_dict in column_dicts:
		filtered_name = fieldname_filter(column_dict['field'])

		# prevent overly-wide fields that require too much scrolling
		# ... TBD: previous value (if any) should take precedence
		if filtered_name in textarea_editable:
			column_dict['width'] = '30rem'
			if 'json' not in filtered_name:
				# for a single line: wrap onto multiple lines to see everything, e.g. our column_names in SQLite db_tables
				# for multi-line text: show all lines
				### BUT don't apply this everywhere since Tabulator often messes up the spacing, e.g. with LOTS of extra space at the bottom
				### that's also true when directly applying this CSS
				"""
				.tabulator-cell {
					white-space: pre-wrap;
				}
				"""
				column_dict['formatter'] = 'textarea'

		elif filtered_name in blob_fields:
			column_dict['width'] = '5rem'

		# these are independent of the above, e.g. 'textarea_editable' will get headerSortTristate
		if filtered_name in hidden_columns:
			column_dict['visible'] = False
			# column_dict['width'] = 46  # TBD: why change width of hidden columns?

		elif column_dict['field'].lower() in ['new window', restore_window_icon_field, details_icon_field]:
			prepare_icon_column(column_dict, column_dict['field'].lower())

		elif column_dict['field'].lower() == '(checked)':
			column_dict['headerFilter'] = True

		else:
			if column_dict['field'] != row_selector:
				column_dict['headerFilter'] = 'input'

			column_dict['headerSortTristate'] = True  # TBD: I think this should apply to icon columns but those are excluded above

			if filtered_name not in built_in_fields:
				# editor_CALLBACK is often None -- but maybe that's required to prevent Tabulator from making it editable by default?
				column_dict['editor'] = editor_CALLBACK if app_config.is_paid_or_trial else None  # make editable

			if is_history and (filtered_name in ['first_opened', 'last_opened']):
				column_dict['formatter'] = to_date_with_offset  # callback function for Tabulator

	# works in place ==> nothing to return


# columns:
def make_icon_column(name):
	# makes the column but does NOT add the icon -- which is correct since 'restore' icon is not always visible
	# print('icon_column', name)
	return {
		'field': name,
		'title': '',
		'formatter': 'html',
		'headerFilter': False,
		'headerSort': False,
		'hozAlign': 'left',
		'minWidth': icon_column_width, # prevent collapse
		# width is set by prepare_icon_column
	}


def make_file_row_column():
	return {
		'field': file_row,
		'title': create_label(file_row),
		'headerFilter': 'input',
		'headerFilterLiveFilter': False,
		'headerSort': True,
		'headerSortTristate': True,
		'visible': False,
		'width': 115,
	}


def make_row_hidden_column():
	return {
		'field': row_hidden,
		'title': row_hidden,
		'headerFilter': False,
		'headerSortTristate': True,
		'visible': False,
		'width': 0,
	}


def make_row_selection_column():
	# small blank column on the right to show color for selected row
	return {
		'field': row_selector,
		'title': '', #tbd create_label(row_selector)
		'headerSort': False,
# 		'width': 12,
		'minWidth': icon_column_width,  # this sets width !?!
# 		'maxWidth': 12,
	}


def make_checkbox_column(update_hide_button_text_CALLBACK):
	return {
		'field': row_checked,
		'title': '',
		'formatter': 'checkbox',
		'editor': 'tickCross',
		'editable': False,
		'formatterParams': {'elementAttributes': {'onchange': update_hide_button_text_CALLBACK}},
		'headerFilter': True,  # TBD: replace with real checkbox
		'headerFilterLiveFilter': False,
		'headerSortTristate': True,
		'width': 65,
	}


def multi_line_formatter(cell, formatterParams, onRendered):
	# I couldn't get Tabulator to show multiple lines for real without messing up row height
	# ==> display a newline symbol
	"""
	¶ — \u00B6
	· — \u00B7
	• — \u2022
	↵ — \u21B5
	↩ — \u21A9
	↲ — \u21B2
	⏎ — \u23CE
	"""
	contents = cell.getValue()
	if contents and isinstance(contents, str):
		contents = contents.replace("\n", "\u23CE ")  # show symbol
	return contents


def make_standard_column(name, header_dict):
	# globals not modified here: app_config, default_width
	# when first creating a tabulator table, header_dict is (likely?!?) empty -- that's fine since this has defaults

	# print('standard_column', name)
	if name in header_dict:
		label = header_dict[name]['label']
	else:
		label = create_label(name)

	# defaults
	formatter = None
	width = 'auto'

	if name == 'foreign keys':  # DB table
		formatter = 'html'  # we insert <BR> to format multiple 'rows' (if any)
	else:
		formatter = multi_line_formatter  # callback

	if 'width' in header_dict.get(name, {}):
		width = header_dict[name]['width']

	return {
		'field': name,
		'title': label,
		'editable': app_config.is_paid_or_trial,
		'editorParams': {'elementAttributes': {'spellcheck': False}},
		'formatter': formatter,
		'headerFilter': 'input',
		'headerFilterLiveFilter': False,
		'headerSortTristate': True,
		'width': width,
		'visible': True,  # actually set elsewhere
	}


# ----
def make_columns(shared_state, visibility_dict={}):
	# call the above based on column type
	# globals not modified here: file_row, row_checked, row_hidden, icon_columns

	# shared_state is sync_utils but import didn't work
	columns = shared_state.columns
	header_data = shared_state.header_data
	callback = shared_state.update_hide_button_text  # a function

	header_dict = {col['name']: col for col in header_data}

	column_dicts = []

	deeply_utils.pp('table_utils.py - make_columns - columns', columns)

	for name in columns:  # TBD: i.e. for name in columns.keys()
		if name == file_row:
			column_dict = make_file_row_column()

		elif name == row_checked:
			column_dict = make_checkbox_column(callback)

		elif name == row_hidden:
			column_dict = make_row_hidden_column()

		elif name in icon_columns:
			column_dict = make_icon_column(name)  # makes the column but does NOT add the icon

		elif name == row_selector:
			column_dict = make_row_selection_column()

		else:
			column_dict = make_standard_column(name, header_dict)

		if visibility_dict:  # i.e. visibility_by_name
			column_dict['visible'] = visibility_dict.get(name, True)  # was False but that's not consistent
		# else: no lookup ==> all cols visible

		column_dicts.append(column_dict)

	return column_dicts


# -----
async def set_title():
	element = document.querySelector('.title')
	if element:
		element.innerText = await window.__TAURI__.window.getCurrentWindow().title()


def add_internal_column_names(column_names, is_customer_data=False, with_details_icon=False, with_window_icons=False):
	# globals not modified here: app_config

	# file_row is added BOTH for customer data and internal tables
	# - for customer data is just the line number
	# - for (history, edits, actions, db_tables) is for hiding rows
	column_names.append(file_row)

	if with_window_icons:
		# pre-pend for history & db_tables
		column_names = [restore_window_icon_field, new_window_icon_field] + column_names

	# applies to customer tables and internal tables
	if app_config.flags.can_select_multiple_rows:  # select to hide rows
		column_names.insert(0, row_checked)
		column_names.append(row_hidden)

	# deeply_utils.pp('table_utils.py - add_internal_column_names - column_names', column_names)

	if not is_customer_data:
		# some features aren't added to history, Edits, Actions ... or the DB 'table of tables'
		return column_names
	# else: fall thru

	if with_details_icon and app_config.is_paid_or_trial:  # TBD: will be allowed in FREE version when details has FREE option to browser w/o editing
		# TBD: but db_tables doesn't have details_icon
		column_names.insert(0, details_icon_field)  # icon

	if app_config.is_paid_or_trial:
		column_names.append(mod_date)
		column_names.append(mod_note)

	if app_config.is_paid_or_trial:  # TBD: will be allowed in FREE version when details has FREE option to browser w/o editing
		# put this last: it's a waste of space at the beginning since rarely used
		column_names.append(row_selector)  # active row for Delete and future cmd-space to open details, Duplicate

	return column_names


#..............................PLAN UTILS........................

# refactor TBD: move?

async def add_flags_to_body():
	# globals not modified here: app_config

	body = document.querySelector('body')

	# init_this_page.js adds PLAN & OS

	if app_config.is_paid_or_trial:

		# not available in v1.0
		if app_config.flags.can_select_multiple_rows:  # implies CAN_HIDE_ROWS
			select_ui = document.querySelectorAll('.flags.can_select_multiple_rows')
			if select_ui:
				for element in select_ui:
					element.classList.remove('flags.can_select_multiple_rows')

		if app_config.flags.can_import_files:
			import_ui = document.querySelector('.flags.can_import_files')
			if import_ui:
				import_ui.classList.remove('flags.can_import_files')

		if app_config.flags.can_view_settings:
			view_ui = document.querySelector('.flags.can_view_settings')
			if view_ui:
				view_ui.classList.remove('flags.can_view_settings')

		if app_config.flags.can_view_edits_table:
			edits_ui = document.querySelector('.flags.can_view_edits_table')
			if edits_ui:
				edits_ui.classList.remove('flags.can_view_edits_table')


def is_allowed_to_open_file_with_ext(ext):
	# globals not modified here: app_config

	if ext in ['sqlite', 'db'] and not app_config.flags.can_open_sqlite:
		return False
	elif ext in ['json', 'jsonl'] and not app_config.flags.can_handle_json:
		return False
	elif ext in ['html', 'htm'] and not app_config.flags.can_handle_html:
		return False
	else:
		return True


def notify_user():
	# globals not modified here: app_config

	if app_config.is_paid_or_trial:
		return
	window.alert('This feature is part of the paid version')


# async def is_last_open_window():
# 	webview = window.__TAURI__.webviewWindow
# 	windows = await webview.getAllWebviewWindows()
#
# 	if len(windows) == 1:
# 		print('is_last_open_window: true')
# 		return True
#
# 	# if it gets here
# 	return False


#.............................END.........................................

# data for friendly column label: special-case capitalization and multi-word phrases with no delim
all_caps_list = [
	'api',
	'css',
	'dob',
	'ean',
	'guid',
	'html',
	'id',
	'ip',
	'json',
	'mac',
	'otp',
	'pin',
	'sku',
	'sql',
	'ssn',
	'sso',
	'url',
	'uuid',
	'xml',
]

mixed_case_pairs = {
	'accesstoken': 'Access Token',
	'accountid': 'Account ID',
	'accountlocked': 'Account Locked',
	'accountname': 'Account Name',
	'accountnumber': 'Account Number',
	'accountstatus': 'Account Status',
	'accounttype': 'Account Type',
	'addressid': 'Address ID',
	'addressline1': 'Address Line1',
	'addressline2': 'Address Line2',
	'addressline3': 'Address Line3',
	'addressline4': 'Address Line4',
	'addressline5': 'Address Line5',
	'addressline6': 'Address Line6',
	'addressname': 'Address City',
	'addressname': 'Address Name',
	'addressstate': 'Address State',
	'addresstype': 'Address Type',
	'addresszip': 'Address Zip',
	'albumid': 'Album ID',
	'albumname': 'Album Name',
	'amountdue': 'Amount Due',
	'api': 'API',
	'apikey': 'APIKey',
	'approvalcode': 'Approval Code',
	'artistid': 'Artist ID',
	'artistname': 'Artist Name',
	'authorizationcode': 'Authorization Code',
	'billingaddress': 'Billing Address',
	'billingaddressid': 'Billing Address ID',
	'billingcity': 'Billing City',
	'billingcountry': 'Billing Country',
	'billingcycle': 'Billing Cycle',
	'billingpostalcode': 'Billing Postal Code',
	'billingstate': 'Billing State',
	'billingstatus': 'Billing Status',
	'billtoaddress': 'Bill To Address',
	'billtoaddressid': 'Bill To Address ID',
	'billtocity': 'Bill To City',
	'billtocountry': 'Bill To Country',
	'billtopostalcode': 'Bill To Postal Code',
	'billtostate': 'Bill To State',
	'birthdate': 'Birth Date',
	'brandname': 'Brand Name',
	'catalogdescription': 'Catalog Description',
	'categorycode': 'Category Code',
	'categoryid': 'Category ID',
	'categoryname': 'Category Name',
	'childid': 'Child ID',
	'clientid': 'Client ID',
	'clientname': 'Client Name',
	'companyname': 'Company Name',
	'contactname': 'Contact Name',
	'contacttitle': 'Contact Title',
	'contractenddate': 'Contract End Date',
	'contractstartdate': 'Contract Start Date',
	'countrycode': 'Country Code',
	'countryregion': 'Country Region',
	'createdat': 'Created At',
	'createdby': 'Created By',
	'createddate': 'Created Date',
	'createdtimestamp': 'Created Timestamp',
	'creationdate': 'Creation Date',
	'creditcard': 'Credit Card',
	'creditcardapprovalcode': 'Credit Card Approval Code',
	'creditscore': 'Credit Score',
	'css': 'CSS',
	'customerdesc': 'Customer Desc',
	'customerid': 'Customer ID',
	'customername': 'Customer Name',
	'customerstatus': 'Customer Status',
	'customertypeid': 'Customer Type ID',
	'databaseversion': 'Database Version',
	'datecreated': 'Date Created',
	'datediscontinued': 'Discontinued Date',
	'datedue': 'Date Due',
	'dateended': 'Date Ended',
	'datemodified': 'Date Modified',
	'dateofbirth': 'Date Of Birth',
	'datestarted': 'Date Started',
	'dealstage': 'Deal Stage',
	'deletedat': 'Deleted At',
	'deletedby': 'Deleted By',
	'deletedtimestamp': 'Deleted Timestamp',
	'devicetype': 'Device Type',
	'discontinueddate': 'Discontinued Date',
	'discountvalue': 'Discount Value',
	'dob': 'DOB',
	'duedate': 'Due Date',
	'ean': 'EAN',
	'emailaddress': 'Email Address',
	'emailaddress1': 'Email Address1',
	'emailaddress2': 'Email Address2',
	'emailsubscriptionstatus': 'Email Subscription Status',
	'emailverified': 'Email Verified',
	'employeeid': 'Employee ID',
	'employeename': 'Employee Name',
	'employeesalary': 'Employee Salary',
	'enddate': 'End Date',
	'endedat': 'Ended At',
	'errorline': 'Error Line',
	'errorlogid': 'Error Log ID',
	'errormessage': 'Error Message',
	'errornumber': 'Error Number',
	'errorprocedure': 'Error Procedure',
	'errorseverity': 'Error Severity',
	'errorstate': 'Error State',
	'errortime': 'Error Time',
	'failedloginattempts': 'Failed Login Attempts',
	'fileuploaddate': 'File Upload Date',
	'fileuuid': 'File UUID',
	'firstname': 'First Name',
	'genreid': 'Genre ID',
	'genrename': 'Genre Name',
	'givenname': 'Given Name',
	'guid': 'GUID',
	'hiredate': 'Hire Date',
	'homepage': 'Home Page',
	'homephone': 'Home Phone',
	'html': 'HTML',
	'id': 'ID',
	'imageurl': 'Image URL',
	'internalid': 'Internal ID',
	'internalname': 'Internal Name',
	'inventorycount': 'Inventory Count',
	'inventorystatus': 'Inventory Status',
	'invoicedate': 'Invoice Date',
	'invoiceid': 'Invoice ID',
	'invoiceitemid': 'Invoice Item ID',
	'invoicelineid': 'Invoice Line ID',
	'invoicelineitemid': 'Invoice Line Item ID',
	'invoicenumber': 'Invoice Number',
	'invoicetotal': 'Invoice Total',
	'ip': 'IP',
	'isactive': 'Is Active',
	'isadminuser': 'Is Admin User',
	'isfeatured': 'Is Featured',
	'isverified': 'Is Verified',
	'itemid': 'Item ID',
	'itemname': 'Item Name',
	'jobtitle': 'Job Title',
	'json': 'JSON',
	'languagepreference': 'Language Preference',
	'lastlogintime': 'Last Login Time',
	'lastname': 'Last Name',
	'lastpurchasedate': 'Last Purchase Date',
	'leadowner': 'Lead Owner',
	'linetotal': 'Line Total',
	'listprice': 'List Price',
	'logintimestamp': 'Login Timestamp',
	'loyaltypoints': 'Loyalty Points',
	'mac': 'MAC',
	'marketingcampaignid': 'Marketing Campaign ID',
	'marketingcampaignname': 'Marketingcampaign Name',
	'marketingoptin': 'Marketing Opt In',
	'mediatype': 'Media Type',
	'mediatypeid': 'Media Type ID',
	'middlename': 'Middle Name',
	'modificationdate': 'Modification Date',
	'modifieddate': 'Modified Date',
	'modifiedtimestamp': 'Modified Timestamp',
	'namestyle': 'Name Style',
	'numberofemployees': 'Number Of Employees',
	'onlineorderflag': 'Online Order Flag',
	'orderdate': 'Order Date',
	'orderid': 'Order ID',
	'ordername': 'Order Name',
	'orderqty': 'Order Qty',
	'orderquantity': 'Order Quantity',
	'orderstatus': 'Order Status',
	'organizationid': 'Organization ID',
	'organizationname': 'Organization Name',
	'otp': 'OTP',
	'parentid': 'Parent ID',
	'parentproductcategoryid': 'Parent Product Category ID',
	'passwordhash': 'Password Hash',
	'passwordresetcode': 'Password Reset Code',
	'passwordsalt': 'Password Salt',
	'paymentgateway': 'Payment Gateway',
	'paymentstatus': 'Payment Status',
	'phonenumber': 'Phone Number',
	'phonenumber1': 'Phone Number1',
	'phonenumber2': 'Phone Number2',
	'photopath': 'Photo Path',
	'pin': 'PIN',
	'planid': 'Plan ID',
	'planname': 'Plan Name',
	'playlistid': 'Playlist ID',
	'playlistname': 'Playlist Name',
	'ponumber': 'PONumber',
	'postalcode': 'Postal Code',
	'preferredcontactmethod': 'Preferred Contact Method',
	'productcategoryid': 'Product Category ID',
	'productcategoryname': 'Productcategory Name',
	'productcode': 'Product Code',
	'productdescriptionid': 'Product Description ID',
	'productid': 'Product ID',
	'productimageurl': 'Product Image URL',
	'productmodelid': 'Product Model ID',
	'productname': 'Product Name',
	'productnumber': 'Product Number',
	'productrating': 'Product Rating',
	'profilepictureurl': 'Profile Picture URL',
	'purchaseordernumber': 'Purchase Order Number',
	'qtyperunit': 'Qty Per Unit',
	'quantityperunit': 'Quantity Per Unit',
	'referralcode': 'Referral Code',
	'referralsource': 'Referral Source',
	'refreshtoken': 'Refresh Token',
	'regiondescription': 'Region Description',
	'regionid': 'Region ID',
	'regionname': 'Region Name',
	'reorderlevel': 'Reorder Level',
	'reportsto': 'Reports To',
	'requireddate': 'Required Date',
	'resettokenexpiry': 'Reset Token Expiry',
	'restockdate': 'Restock Date',
	'returnpolicyid': 'Return Policy ID',
	'returnstatus': 'Return Status',
	'reviewcomment': 'Review Comment',
	'reviewrating': 'Review Rating',
	'revisionnumber': 'Revision Number',
	'roleid': 'Role ID',
	'rolename': 'Role Name',
	'rowguid': 'Row GUID',
	'salesorderdetailid': 'Sales Order Detail ID',
	'salesorderid': 'Sales Order ID',
	'salesordernumber': 'Sales Order Number',
	'salesperson': 'Sales Person',
	'securityanswer': 'Security Answer',
	'securityquestion': 'Security Question',
	'sellenddate': 'Sell End Date',
	'sellstartdate': 'Sell Start Date',
	'seqname': 'Seq Name',
	'sequencename': 'Sequence Name',
	'sessionid': 'Session ID',
	'sessionname': 'Session Name',
	'sessiontoken': 'Session Token',
	'shipaddress': 'Ship Address',
	'shipcity': 'Ship City',
	'shipcountry': 'Ship Country',
	'shipdate': 'Ship Date',
	'shipmethod': 'Ship Method',
	'shipname': 'Ship Name',
	'shippeddate': 'Shipped Date',
	'shipperid': 'Shipper ID',
	'shippername': 'Shipper Name',
	'shippingaddress': 'Shipping Address',
	'shippingcity': 'Shipping City',
	'shippingcountry': 'Shipping Country',
	'shippingdate': 'Shipping Date',
	'shippingid': 'Shipping ID',
	'shippingmethod': 'Shipping Method',
	'shippingname': 'Shipping Name',
	'shippingpostalcode': 'Shipping Postal Code',
	'shippingrate': 'Shipping Rate',
	'shippingregion': 'Shipping Region',
	'shippingvia': 'Shipping Via',
	'shippingzoneid': 'Shipping Zone ID',
	'shippingzonename': 'Shippingzone Name',
	'shippostalcode': 'Ship Postal Code',
	'shipregion': 'Ship Region',
	'shiptoaddress': 'Ship To Address',
	'shiptoaddressid': 'Ship To Address ID',
	'shiptocity': 'Ship To City',
	'shiptocountry': 'Ship To Country',
	'shiptodate': 'Ship To Date',
	'shiptoid': 'Ship To ID',
	'shiptomethod': 'Ship To Method',
	'shiptoname': 'Ship To Name',
	'shiptopostalcode': 'Ship To Postal Code',
	'shiptoregion': 'Ship To Region',
	'shipvia': 'Ship Via',
	'signupdate': 'Signup Date',
	'sku': 'SKU',
	'skunumber': 'SKUNumber',
	'socialsecuritynumber': 'Social Security Number',
	'sql': 'SQL',
	'ssn': 'SSN',
	'sso': 'SSO',
	'standardcost': 'Standard Cost',
	'startdate': 'Start Date',
	'startedat': 'Started At',
	'startedby': 'Started By',
	'stateprovince': 'State Province',
	'stockkeepingunit': 'Stock Keeping Unit',
	'stocklevel': 'Stock Level',
	'subcategoryid': 'Subcategory ID',
	'subcategoryname': 'Subcategory Name',
	'subscriptiondate': 'Subscription Date',
	'subtotal': 'Sub Total',
	'supplierid': 'Supplier ID',
	'suppliername': 'Supplier Name',
	'supportrepid': 'Support Rep ID',
	'supportrepname': 'Support Rep Name',
	'supportticketid': 'Support Ticket ID',
	'systemid': 'System ID',
	'systeminformationid': 'System Information ID',
	'systemname': 'System Name',
	'taxamount': 'Tax Amount',
	'taxamt': 'Tax Amt',
	'taxid': 'Tax ID',
	'taxrate': 'Tax Rate',
	'territorydescription': 'Territory Description',
	'territoryid': 'Territory ID',
	'territoryname': 'Territory Name',
	'thumbnailphoto': 'Thumbnail Photo',
	'thumbnailphotofilename': 'Thumbnail Photo Filename',
	'ticketid': 'Ticket ID',
	'titleofcourtesy': 'Title Of Courtesy',
	'totalamount': 'Total Amount',
	'totalamt': 'Total Amt',
	'totaldue': 'Total Due',
	'trackid': 'Track ID',
	'trackname': 'Track Name',
	'transactiondate': 'Transaction Date',
	'transactionid': 'Transaction ID',
	'twofactorenabled': 'Two Factor Enabled',
	'typeid': 'Type ID',
	'typename': 'Type Name',
	'unitprice': 'Unit Price',
	'unitpricediscount': 'Unit Price Discount',
	'unitsinstock': 'Units In Stock',
	'unitsonorder': 'Units On Order',
	'updatedat': 'Updated At',
	'updatedby': 'Updated By',
	'url': 'URL',
	'userguid': 'User GUID',
	'userid': 'User ID',
	'username': 'User Name',
	'userprofileid': 'User Profile ID',
	'userroleid': 'User Role ID',
	'userrolename': 'User Role Name',
	'usertype': 'User Type',
	'uuid': 'UUID',
	'vendorid': 'Vendor ID',
	'vendorname': 'Vendor Name',
	'versiondate': 'Version Date',
	'warehouselocation': 'Warehouse Location',
	'xml': 'XML',
	'zipcode': 'Zip Code',
}
