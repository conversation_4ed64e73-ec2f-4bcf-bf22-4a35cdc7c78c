use std::path::Path;

use rand::distributions::{Alphanumeric, DistString};

use crate::edit1::get_file_folder;

//generate a unique label for a window
pub fn get_label() -> String {
    Alphanumeric.sample_string(&mut rand::thread_rng(), 16)
}

#[tauri::command]
pub fn get_window_title_by_path(path: String) -> String {
    let file_name = Path::new(&path)
        .file_name()
        .map(|os_str| os_str.to_string_lossy().into_owned())
        .unwrap_or_default();
    let file_folder = get_file_folder(path);
    format!("{name} - {folder}", name = file_name, folder = file_folder)
}
