.window-header .file-options {
	color: var(--primary-text);
	position: relative;
	cursor: pointer;
}

.window-header .file-options label {
	cursor: pointer;
}

.window-header .file-options input[name='file-toggle'] {
	display: none;
}

.window-header .file-options input[name='file-toggle']:checked ~ .options {
	display: block;
}

.window-header .file-options .options {
	z-index: 100;
	position: absolute;
	width: 150px;
	background-color: var(--menu-color);
	border: 1px solid #aaa;
	display: none;
	border-radius: 4px;
}
/* .window-header .file-options .options:hover {
  display: block;
} */

.window-header .file-options .options .option {
	padding: 4px 8px;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	align-items: baseline;
}

.window-header .file-options .options .shortcut {
	font-weight: lighter;
	font-size: 9px;
}

.window-header .file-options .options .option:hover {
	background-color: var(--hover-bg);
}

.window-header .file-options .options .divider {
	width: calc(100% - 16px);
	height: 1px;
	background-color: #777;
	margin: 0 8px;
}
