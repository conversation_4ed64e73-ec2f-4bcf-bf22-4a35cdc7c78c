# Type hints for <PERSON><PERSON><PERSON><PERSON>

from typing import Any, Callable, Dict, Union

class NodeModule:
    style: Any

class ConsoleModule:
    def log(self, *args: Any, **kwargs: Any) -> Any: ...

class ConsoleModule:
    def log(self, *args: Any, **kwargs: Any) -> Any: ...

class AIOModule:
    def run(self, *args: Any, **kwargs: Any) -> Any: ...
    def sleep(self, seconds: int) -> None: ...

class DocumentModule:
    def querySelector(self, selector: str) -> Union[None, NodeModule]: ...

class WindowModule:
    __TAURI__: Any


window: WindowModule
console: ConsoleModule = ConsoleModule()
aio: AIOModule = AIOModule()
document = DocumentModule()
alert: Callable