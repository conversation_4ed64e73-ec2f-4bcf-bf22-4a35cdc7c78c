//! menu.rs - app menubar
//!
//! refactor TBD: create_menu and create_menu_table are nearly identical; called by windows.rs but that doesn't indicate why both are useful

use crate::common;
use crate::cmd::window::{ create_known_window_rust };
use crate::global_config::{ AppConfig, Plan };
use anyhow::Result;
use lazy_static::lazy_static;
use serde_json::json;
use std::collections::HashMap;
use tauri::menu::{ AboutMetadata, IsMenuItem, Menu, MenuId, MenuItem, MenuItemBuilder, PredefinedMenuItem, Submenu, HELP_SUBMENU_ID };
use tauri::{ Emitter, Manager, State, WebviewWindow, Wry };

pub const WINDOW_SUBMENU_ID: &str = "__WINDOW_SUBMENU__";

// Hashmap for window menu items
// Where key is window label and value is menu item ID
lazy_static! {
	pub static ref WINDOW_MENU_ITEMS_MAP: HashMap<String, String> = HashMap::new();
}

fn setup_window_menu(app_handle: &tauri::AppHandle) -> Submenu<Wry> {
	Submenu::with_id_and_items(
		app_handle,
		WINDOW_SUBMENU_ID,
		"Window",
		true,
		&[
			&PredefinedMenuItem::minimize(app_handle, None).unwrap(),
			&PredefinedMenuItem::maximize(app_handle, None).unwrap(),
			&PredefinedMenuItem::separator(app_handle).unwrap(),
		]
	).unwrap()
}

/// Create macOS menu and handle it
pub fn create_menu(app_handle: tauri::AppHandle, from_setup: bool) -> Result<tauri::menu::Menu<Wry>> {
	let window_menu_map: HashMap<String, MenuId> = HashMap::new(); // label: itemID
	app_handle.manage(std::sync::Mutex::new(window_menu_map));

	let pkg_info = app_handle.package_info();
	let config = app_handle.config();

	let app_config: State<'_, AppConfig> = app_handle.state();

	let about_metadata = AboutMetadata {
		name: Some(pkg_info.name.clone()),
		version: Some(pkg_info.version.to_string()),
		copyright: config.bundle.copyright.clone(),
		authors: config.bundle.publisher.clone().map(|p| vec![p]),
		..Default::default()
	};
	let window_menu = setup_window_menu(&app_handle);

	let product_name = common::get_product_name(app_handle.clone());

	let help_item = MenuItem::new(&app_handle, &format!("{} Help", product_name), true, Some("")).unwrap();
	let help_menu = Submenu::with_id_and_items(&app_handle, HELP_SUBMENU_ID, "Help", true, &[&help_item])?;

	// now deeply
	let preferences_item = MenuItem::new(&app_handle, "Preferences…", true, Some("Cmd+,")).unwrap();

	let mut file_menu_items = vec![
		Box::new(MenuItemBuilder::new("Open…").id("open_file").accelerator("CommandOrControl+o").build(&app_handle).unwrap()) as Box<
			dyn IsMenuItem<Wry>
		>
	];

	// file_menu_items.push(

	// );

	#[cfg(target_os = "macos")]
	if app_config.plan != Plan::Free {
		file_menu_items.push(
			Box::new(MenuItemBuilder::new("Save").id("save_file").accelerator("CommandOrControl+s").build(&app_handle).unwrap()) as Box<
				dyn IsMenuItem<Wry>
			>
		);
	}

	file_menu_items.push(Box::new(MenuItemBuilder::new("History").id("history").build(&app_handle).unwrap()) as Box<dyn IsMenuItem<Wry>>);

	file_menu_items.push(Box::new(PredefinedMenuItem::close_window(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>);

	// Convert boxed items to `&dyn IsMenuItem<Wry>`
	let file_refs: Vec<&dyn IsMenuItem<Wry>> = file_menu_items
		.iter()
		.map(|b| b.as_ref())
		.collect();

	let file_submenu = Submenu::with_items(&app_handle, "File", true, &file_refs)?;

	let mut edit_menu_items: Vec<Box<dyn IsMenuItem<Wry>>> = vec![
		Box::new(PredefinedMenuItem::undo(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::redo(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::cut(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::copy(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::paste(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::select_all(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>
	];

	if app_config.plan != Plan::Free {
		edit_menu_items.push(Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()) as Box<dyn IsMenuItem<Wry>>);

		edit_menu_items.push(
			Box::new(
				MenuItemBuilder::new("New Row").id("global_insert").accelerator("CommandOrControl+N").build(&app_handle).unwrap()
			) as Box<dyn IsMenuItem<Wry>>
		);
	}

	let edit_refs: Vec<&dyn IsMenuItem<Wry>> = edit_menu_items
		.iter()
		.map(|b| b.as_ref())
		.collect();
	let edit_submenu = Submenu::with_items(&app_handle, "Edit", true, &edit_refs)?;

	let menu = Menu::with_items(
		&app_handle,
		&[
			&Submenu::with_items(
				&app_handle,
				pkg_info.name.clone(),
				true,
				&[
					&PredefinedMenuItem::about(&app_handle, None, Some(about_metadata.clone())).unwrap(),
					// v1.0: no Settings/Preferences menu item (or leading separator)
					// TBD: for v1.x either add at least 1 setting in FREE version or figure out how to add conditionally (ChatGPT failed!)
					// &PredefinedMenuItem::separator(&app_handle).unwrap(),
					// &preferences_item,
					#[cfg(target_os = "macos")] &PredefinedMenuItem::separator(&app_handle).unwrap(),
					#[cfg(target_os = "macos")] &PredefinedMenuItem::hide(&app_handle, None).unwrap(),
					#[cfg(target_os = "macos")] &PredefinedMenuItem::hide_others(&app_handle, None).unwrap(),
					&PredefinedMenuItem::separator(&app_handle).unwrap(),
					&PredefinedMenuItem::quit(&app_handle, None).unwrap(),
				]
			)?,
			&file_submenu,
			&edit_submenu,
			#[cfg(target_os = "macos")] &(
				// Enter Full Screen
				Submenu::with_items(&app_handle, "View", true, &[&PredefinedMenuItem::fullscreen(&app_handle, None).unwrap()])?
			),
			&window_menu,
			&help_menu,
		]
	)?;

	static ONCE: std::sync::Once = std::sync::Once::new();

	if from_setup {
		menu.set_as_app_menu().unwrap();

		ONCE.call_once(|| {
			app_handle.on_menu_event(move |app_handle, event| {
				let id = event.id();
				let app_config: State<'_, AppConfig> = app_handle.state();

				//file
				if id == preferences_item.id() {
					match app_config.plan {
						Plan::Free => {
							create_known_window_rust(app_handle.clone(), "help");
						}
						_ => {
							create_known_window_rust(app_handle.clone(), "settings");
						}
					}
				}
				if id == help_item.id() {
					create_known_window_rust(app_handle.clone(), "help");
				} else if id.0 == "global_insert" {
					#[cfg(target_os = "macos")]
					{
						let focused_window = app_handle.get_focused_window();

						if focused_window.is_some() {
							let _ = app_handle.emit("add_new", json!({"label": focused_window.unwrap().label()}));
						}
					}
				} else if id.0 == "open_file" {
					if let Some(focused_window) = app_handle.get_focused_window() {
						let label = focused_window.label();
						println!("Emitting to window: {}", label);
						let _ = focused_window.emit("open_file_menu", json!({ "label": label }));
					} else {
						println!("No focused window found.");
					}
				} else if id.0 == "history" {
					if let Some(focused_window) = app_handle.get_focused_window() {
						let label = focused_window.label();
						if label != "history" {
							// TBD: there's only 1 of these for history but 2 for settings and 2-3 for help (including as alt to settings)
							// ... though the redundancy in the others should disappear after create_menu_table is refactored / eliminated
							create_known_window_rust(app_handle.clone(), "history");
						}
					} else {
						println!("No focused window found.");
					}
				} else if id.0.starts_with("insert_") {
					let _ = app_handle.emit("add_new", json!({"insert_id": id.0}));
				} else if id.0 == "save_file" {
					if let Some(focused_window) = app_handle.get_focused_window() {
						let label = focused_window.label();
						println!("Emitting to window: {}", label);
						let _ = focused_window.emit("save_file_overwrite", json!({"label": label}));
					} else {
						println!("No focused window found. Save File action skipped.");
					}
				} else {
					maybe_focus_window(id, app_handle);
				}
			});
		});
	}

	Ok(menu)
}

// TBD: merge this with the above. If any differences are unclear, just ask
pub fn create_menu_table(app_handle: tauri::AppHandle, insert_id: String) -> Result<tauri::menu::Menu<Wry>> {
	let window_menu_map: HashMap<String, MenuId> = HashMap::new(); // label: itemID
	app_handle.manage(std::sync::Mutex::new(window_menu_map));

	let pkg_info = app_handle.package_info();
	let config = app_handle.config();
	let app_config: State<'_, AppConfig> = app_handle.state();

	let about_metadata = AboutMetadata {
		name: Some(pkg_info.name.clone()),
		version: Some(pkg_info.version.to_string()),
		copyright: config.bundle.copyright.clone(),
		authors: config.bundle.publisher.clone().map(|p| vec![p]),
		..Default::default()
	};
	let window_menu = setup_window_menu(&app_handle);

	let product_name = common::get_product_name(app_handle.clone());

	let help_item = MenuItem::new(&app_handle, &format!("{} Help", product_name), true, Some("")).unwrap();
	let help_menu = Submenu::with_id_and_items(&app_handle, HELP_SUBMENU_ID, "Help", true, &[&help_item])?;

	// now deeply
	let preferences_item = MenuItem::new(&app_handle, "Preferences…", true, Some("Cmd+,")).unwrap();

	let mut file_menu_items = vec![];

	file_menu_items.push(
		Box::new(MenuItemBuilder::new("Open…").id("open_file").accelerator("CommandOrControl+o").build(&app_handle).unwrap()) as Box<
			dyn IsMenuItem<Wry>
		>
	);

	if app_config.plan != Plan::Free {
		file_menu_items.push(
			Box::new(MenuItemBuilder::new("Save").id("save_file").accelerator("CommandOrControl+s").build(&app_handle).unwrap()) as Box<
				dyn IsMenuItem<Wry>
			>
		);
	}

	file_menu_items.push(Box::new(MenuItemBuilder::new("History").id("history").build(&app_handle).unwrap()) as Box<dyn IsMenuItem<Wry>>);

	file_menu_items.push(Box::new(PredefinedMenuItem::close_window(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>);

	// Convert boxed items to `&dyn IsMenuItem<Wry>`
	let file_refs: Vec<&dyn IsMenuItem<Wry>> = file_menu_items
		.iter()
		.map(|b| b.as_ref())
		.collect();

	let file_submenu = Submenu::with_items(&app_handle, "File", true, &file_refs)?;

	let mut edit_menu_items = vec![
		Box::new(PredefinedMenuItem::undo(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::redo(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::cut(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::copy(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::paste(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>,
		Box::new(PredefinedMenuItem::select_all(&app_handle, None).unwrap()) as Box<dyn IsMenuItem<Wry>>
	];

	if app_config.plan != Plan::Free {
		edit_menu_items.push(Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()) as Box<dyn IsMenuItem<Wry>>);

		edit_menu_items.push(
			Box::new(MenuItemBuilder::new("New Row").id(insert_id).accelerator("CommandOrControl+N").build(&app_handle).unwrap()) as Box<
				dyn IsMenuItem<Wry>
			>
		);
	}

	let edit_refs: Vec<&dyn IsMenuItem<Wry>> = edit_menu_items
		.iter()
		.map(|b| b.as_ref())
		.collect();
	let edit_submenu = Submenu::with_items(&app_handle, "Edit", true, &edit_refs)?;

	let menu = Menu::with_items(
		&app_handle,
		&[
			&Submenu::with_items(
				&app_handle,
				pkg_info.name.clone(),
				true,
				&[
					&PredefinedMenuItem::about(&app_handle, None, Some(about_metadata.clone())).unwrap(),
					// v1.0: no Settings/Preferences menu item (or leading separator)
					// TBD: for v1.x either add at least 1 setting in FREE version or figure out how to add conditionally (ChatGPT failed!)
					// &PredefinedMenuItem::separator(&app_handle).unwrap(),
					// &preferences_item,
					#[cfg(target_os = "macos")] &PredefinedMenuItem::separator(&app_handle).unwrap(),
					#[cfg(target_os = "macos")] &PredefinedMenuItem::hide(&app_handle, None).unwrap(),
					#[cfg(target_os = "macos")] &PredefinedMenuItem::hide_others(&app_handle, None).unwrap(),
					&PredefinedMenuItem::separator(&app_handle).unwrap(),
					&PredefinedMenuItem::quit(&app_handle, None).unwrap(),
				]
			)?,
			&file_submenu,
			&edit_submenu,
			#[cfg(target_os = "macos")] &(
				// Enter Full Screen
				Submenu::with_items(&app_handle, "View", true, &[&PredefinedMenuItem::fullscreen(&app_handle, None).unwrap()])?
			),
			&window_menu,
			&help_menu,
		]
	)?;

	static ONCE: std::sync::Once = std::sync::Once::new();

	ONCE.call_once(|| {
		app_handle.on_menu_event(move |app_handle, event| {
			let id = event.id();
			let app_config: State<'_, AppConfig> = app_handle.state();

			//file
			if id == preferences_item.id() {
				match app_config.plan {
					Plan::Free => {
						create_known_window_rust(app_handle.clone(), "help");
					}
					_ => {
						create_known_window_rust(app_handle.clone(), "settings");
					}
				}
			}

			if id == help_item.id() {
				create_known_window_rust(app_handle.clone(), "help");
			} else {
				maybe_focus_window(id, app_handle);
			}

			if id.0.starts_with("insert_") {
				let _ = app_handle.emit("add_new", json!({"insert_id": id.0}));
			} else {
				maybe_focus_window(id, app_handle);
			}
		});
	});

	Ok(menu)
}
// ^^^^^^^^^^^^ END: merge this with above ^^^^^^^^^^^^^^

/// Check if current menu item exists in menu window map
/// If it found focus the window
fn maybe_focus_window(id: &MenuId, app_handle: &tauri::AppHandle) {
	// Handle dynamic window menu items
	let menu_map: State<std::sync::Mutex<HashMap<String, MenuId>>> = app_handle.state();
	let menu_map = menu_map.lock().unwrap();
	for (label, item_id) in menu_map.iter() {
		if id == item_id {
			match app_handle.get_window(label) {
				Some(window) => {
					window.unminimize().unwrap();
					window.set_focus().unwrap();
				}
				None => {
					log::error!("Not found window to focus {}", label);
				}
			}
		}
	}
}

pub fn update_titles(app_handle: &tauri::AppHandle) {
	let menu = app_handle.menu().unwrap();
	let window_submenu = menu.get(WINDOW_SUBMENU_ID).unwrap();
	let window_submenu = window_submenu.as_submenu().unwrap();
	let menu_map: State<std::sync::Mutex<HashMap<String, MenuId>>> = app_handle.state();
	let mut menu_map = menu_map.lock().unwrap();

	// Remove window menu and clear menu map
	menu_map.clear();

	let items = window_submenu.items().unwrap();
	for item in items.iter().skip(3) {
		// 'skip(3)' to avoid removing the standard menu Items: Minimize, Zoom, (separator line)
		window_submenu.remove(item).unwrap();
	}
	// Rebuild window items
	let windows = app_handle.webview_windows();

	// Collect windows and their titles into a vector
	let mut window_vec: Vec<(&String, &WebviewWindow)> = windows.iter().collect();

	// Sort the vector by the titles
	window_vec.sort_by(|a, b| a.1.title().unwrap_or_default().cmp(&b.1.title().unwrap_or_default()));

	for (label, window) in window_vec.iter() {
		let title = window.title().unwrap_or("Unknown".into());
		let item: Box<dyn IsMenuItem<tauri::Wry>> = Box::new(MenuItem::new(app_handle, title, true, Some("")).unwrap());
		menu_map.insert(label.to_string(), item.id().clone());
		window_submenu.append(&*item).unwrap();
	}
}
