use tauri::Result;
use lazy_static::lazy_static;
use serde_json::json;
use std::collections::HashMap;
use std::sync::atomic::{AtomicBool, Ordering};
use tauri::async_runtime::spawn;
use tauri::menu::{
    AboutMetadata, CheckMenuItem, IsMenuItem, Menu, MenuId, MenuItem, MenuItemBuilder, PredefinedMenuItem,
    Submenu, HELP_SUBMENU_ID,
};
use tauri::{Emitter, Manager, State, WebviewWindow, Wry};

/// Helper struct to simplify menu item collection and conversion
struct MenuItemCollection {
    items: Vec<Box<dyn IsMenuItem<Wry>>>,
}

impl MenuItemCollection {
    fn new() -> Self {
        Self { items: Vec::new() }
    }

    fn add<T: IsMenuItem<Wry> + 'static>(mut self, item: T) -> Self {
        self.items.push(Box::new(item));
        self
    }

    fn add_boxed(mut self, item: Box<dyn IsMenuItem<Wry>>) -> Self {
        self.items.push(item);
        self
    }

    fn to_refs(&self) -> Vec<&dyn IsMenuItem<Wry>> {
        self.items.iter().map(|item| item.as_ref()).collect()
    }


}

/// Macro to simplify menu item creation
macro_rules! menu_items {
    ($($item:expr),* $(,)?) => {
        {
            let mut collection = MenuItemCollection::new();
            $(
                collection = collection.add_boxed(Box::new($item));
            )*
            collection
        }
    };
}

// Import both sets of dependencies
use crate::cmd::window::{create_known_window_rust};
use crate::global_config::{AppConfig, Plan};
use crate::edit1::{create_new_editor, AppState};
use crate::editor::{open_files, open_data_files};

pub const WINDOW_SUBMENU_ID: &str = "__WINDOW_SUBMENU__";

pub mod menu_ids {
    // File menu
    pub const NEW_DOCUMENT: &str = "new_document";
    pub const OPEN_DOCUMENT: &str = "open_document";
    pub const OPEN_DATA: &str = "open_data";
    pub const NEW: &str = "new";
    pub const OPEN: &str = "open";
    pub const SAVE: &str = "save";
    pub const SAVE_AS: &str = "save_as";

    // Edit menu
    pub const GLOBAL_INSERT: &str = "global_insert";
    pub const INSERT_PREFIX: &str = "insert_";

    // View menu
    pub const SPLIT_VIEW: &str = "split_view";
    pub const DARK_MODE: &str = "dark_mode";

    // Other
    pub const PREFERENCES: &str = "preferences";
}

// Hashmap for window menu items
// Where key is window label and value is menu item ID
lazy_static! {
    pub static ref WINDOW_MENU_ITEMS_MAP: HashMap<String, String> = HashMap::new();
}

// Static flags to ensure event handlers are only registered once
static DOCUMENT_HANDLER_REGISTERED: AtomicBool = AtomicBool::new(false);
static TABLE_HANDLER_REGISTERED: AtomicBool = AtomicBool::new(false);
static EDITOR_HANDLER_REGISTERED: AtomicBool = AtomicBool::new(false);

#[derive(Debug, Clone)]
pub enum MenuType {
    FileData,
    DbData { insert_id: String },
    TextEditor,
}

pub fn get_focused_window(app_handle: &tauri::AppHandle) -> Option<tauri::WebviewWindow> {
    for window in app_handle.webview_windows().values() {
        if let Ok(true) = window.is_focused() {
            return Some(window.clone());
        }
    }
    None
}

/// Helper function to get product name from Tauri config
fn get_product_name(app_handle: &tauri::AppHandle) -> String {
    app_handle.config().product_name.clone().unwrap()
}

/// Helper function to create help text with product name
fn create_help_text(app_handle: &tauri::AppHandle) -> String {
    format!("{} Help", get_product_name(app_handle))
}



/// Helper function to create help menu item
fn create_help_menu_item(app_handle: &tauri::AppHandle) -> Result<MenuItem<Wry>> {
    let help_text = create_help_text(app_handle);
    MenuItem::new(app_handle, &help_text, true, Some(""))
}

/// Helper function to handle window focus with emit
fn handle_window_focus_emit(app_handle: &tauri::AppHandle, event_name: &str) {
    let focused_window = app_handle.get_focused_window();
    if let Some(window) = focused_window {
        let _ = app_handle.emit(event_name, json!({"label": window.label()}));
    }
}

fn setup_window_menu(app_handle: &tauri::AppHandle) -> Result<Submenu<Wry>> {
    Submenu::with_id_and_items(
        app_handle,
        WINDOW_SUBMENU_ID,
        "Window",
        true,
        &[
            &PredefinedMenuItem::minimize(app_handle, None)?,
            &PredefinedMenuItem::maximize(app_handle, None)?,
            &PredefinedMenuItem::separator(app_handle)?,
            &PredefinedMenuItem::close_window(app_handle, None)?,
            &PredefinedMenuItem::separator(app_handle)?,
        ],
    )
}

fn create_help_menu(app_handle: &tauri::AppHandle, _menu_type: &MenuType) -> Result<Submenu<Wry>> {
    let help_item = create_help_menu_item(app_handle)?;
    Submenu::with_id_and_items(app_handle, HELP_SUBMENU_ID, "Help", true, &[&help_item])
}

fn initialize_window_menu_state(app_handle: &tauri::AppHandle) {
    let window_menu_map: HashMap<String, MenuId> = HashMap::new(); // label: itemID
    app_handle.manage(std::sync::Mutex::new(window_menu_map));
}

fn build_main_menu(
    app_handle: &tauri::AppHandle,
    pkg_info: &tauri::PackageInfo,
    file_items: MenuItemCollection,
    edit_items: MenuItemCollection,
    view_items: Option<MenuItemCollection>,
    app_menu_items: MenuItemCollection,
    window_menu: Submenu<Wry>,
    help_menu: Submenu<Wry>,
) -> Result<Menu<Wry>> {
    // Convert items to references using the simplified approach
    let file_items_refs = file_items.to_refs();
    let edit_items_refs = edit_items.to_refs();
    let app_menu_items_refs = app_menu_items.to_refs();
    let view_items_refs = view_items.as_ref().map(|items| items.to_refs());

    // Build the menu - create submenus first to avoid temporary value issues
    let app_submenu = Submenu::with_items(app_handle, pkg_info.name.clone(), true, &app_menu_items_refs)?;
    let file_submenu = Submenu::with_items(app_handle, "File", true, &file_items_refs)?;
    let edit_submenu = Submenu::with_items(app_handle, "Edit", true, &edit_items_refs)?;

    let mut menu_items: Vec<&dyn IsMenuItem<Wry>> = vec![
        &app_submenu,
        &file_submenu,
        &edit_submenu,
    ];

    // Add View menu if it exists (for Editor type)
    let view_submenu = if let Some(view_items_refs) = view_items_refs {
        Some(Submenu::with_items(app_handle, "View", true, &view_items_refs)?)
    } else {
        None
    };

    if let Some(ref view_submenu) = view_submenu {
        menu_items.push(view_submenu);
    }

    // Add Window and Help menus
    menu_items.push(&window_menu);
    menu_items.push(&help_menu);

    Ok(Menu::with_items(app_handle, &menu_items)?)
}

/// Unified menu creation function that handles all menu types
pub fn create_menu(
    app_handle: tauri::AppHandle,
    menu_type: MenuType,
    from_setup: bool,
) -> Result<tauri::menu::Menu<Wry>> {
    // Initialize window menu state
    initialize_window_menu_state(&app_handle);

    let pkg_info = app_handle.package_info();
    let config = app_handle.config();

    let about_metadata = AboutMetadata {
        name: Some(pkg_info.name.clone()),
        version: Some(pkg_info.version.to_string()),
        copyright: config.bundle.copyright.clone(),
        authors: config.bundle.publisher.clone().map(|p| vec![p]),
        ..Default::default()
    };

    // Create base menus
    let window_menu = match setup_window_menu(&app_handle) {
        Ok(menu) => menu,
        Err(e) => {
            log::error!("Failed to setup window menu: {}", e);
            return Err(e);
        }
    };
    let help_menu = match create_help_menu(&app_handle, &menu_type) {
        Ok(menu) => menu,
        Err(e) => {
            log::error!("Failed to create help menu: {}", e);
            return Err(e);
        }
    };

    // Create menu items using the helper function
    let (file_items, edit_items, view_items, app_menu_items) = create_menu_items(&app_handle, &menu_type, about_metadata);

    // Build the complete menu
    let menu = match build_main_menu(
        &app_handle,
        &pkg_info,
        file_items,
        edit_items,
        view_items,
        app_menu_items,
        window_menu,
        help_menu,
    ) {
        Ok(menu) => menu,
        Err(e) => {
            log::error!("Failed to build main menu: {}", e);
            return Err(e);
        }
    };

    if from_setup {
        if let Err(e) = menu.set_as_app_menu() {
            log::error!("Failed to set menu as app menu: {}", e);
            return Err(e);
        }
    }

    // Set up event handlers based on menu type
    setup_menu_event_handlers(&app_handle, &menu_type);

    Ok(menu)
}

/// Common handler for preferences menu item
fn handle_preferences(app_handle: &tauri::AppHandle) {
    let app_config: State<'_, AppConfig> = app_handle.state();

    match app_config.plan {
        Plan::Free => {
            create_known_window_rust(app_handle.clone(), "help");
        }
        _ => {
            create_known_window_rust(app_handle.clone(), "settings");
        }
    }
}

/// Common handler for help menu item
fn handle_help(app_handle: &tauri::AppHandle, _help_item: &MenuItem<Wry>) {
    create_known_window_rust(app_handle.clone(), "help");
}

fn setup_document_event_handler(app_handle: tauri::AppHandle, help_item: MenuItem<Wry>) {
    app_handle.on_menu_event(move |app_handle, event| {
        let id = event.id();

        // Handle new document
        if id.0 == menu_ids::NEW_DOCUMENT {
            let app_handle_clone = app_handle.clone();
            spawn(create_new_editor(app_handle_clone.to_owned(), None));
        }
        // Handle open document
        else if id.0 == menu_ids::OPEN_DOCUMENT {
            let app_handle_clone = app_handle.clone();
            tauri::async_runtime::spawn(async move {
                let _ = open_files(app_handle_clone).await;
            });
        }
        // Handle open data
        else if id.0 == menu_ids::OPEN_DATA {
            let app_handle_clone = app_handle.clone();
            tauri::async_runtime::spawn(async move {
                let _ = open_data_files(app_handle_clone).await;
            });
        }
        // Handle preferences
        else if id.0 == menu_ids::PREFERENCES {
            handle_preferences(app_handle);
        }
        // Handle help
        else if id == help_item.id() {
            handle_help(app_handle, &help_item);
        }
        // Handle global insert
        else if id.0 == menu_ids::GLOBAL_INSERT {
            handle_window_focus_emit(app_handle, "add_new");
        }
        // Handle window focus
        else {
            maybe_focus_window(id, app_handle);
        }
    });
}

fn setup_table_event_handler(app_handle: tauri::AppHandle, help_item: MenuItem<Wry>, _insert_id: String) {
    app_handle.on_menu_event(move |app_handle, event| {
        let id = event.id();

        // Handle preferences
        if id.0 == menu_ids::PREFERENCES {
            handle_preferences(app_handle);
        }
        // Handle help
        else if id == help_item.id() {
            handle_help(app_handle, &help_item);
        }
        // Handle table-specific insert
        else if id.0.starts_with(menu_ids::INSERT_PREFIX) {
            handle_window_focus_emit(app_handle, "add_new");
        }
        // Handle window focus
        else {
            maybe_focus_window(id, app_handle);
        }
    });
}

fn setup_editor_event_handler(app_handle: tauri::AppHandle) {
    app_handle.on_menu_event(move |app_handle, event| {
        let id = event.id();
        println!("Menu event triggered: {:?}", id);

        let state: State<'_, AppState> = app_handle.state();
        let current_window = state.active_window.clone();
        let window_guard = current_window.as_ref().lock().unwrap();

        // Handle menu items
        if id.0 == "new" {
            let app_handle_clone = app_handle.clone();
            spawn(create_new_editor(app_handle_clone.to_owned(), None));
        } else if id.0 == "open" {
            let app_handle_clone = app_handle.clone();
            tauri::async_runtime::spawn(async move {
                let _ = open_files(app_handle_clone).await;
            });
        } else if id.0 == "save" {
            if let Some(w) = &*window_guard {
                let _ = w.emit_to(w.label(), "file:save", "");
            }
        } else if id.0 == "save_as" {
            if let Some(w) = &*window_guard {
                let _ = w.emit_to(w.label(), "file:save-as", "");
            }
        } else if id.0 == "split_view" {
            if let Some(w) = &*window_guard {
                // Note: We'd need to get the actual checked state from the menu item
                let _ = w.emit_to(w.label(), "file:split", json!({"split": true}));
            }
        } else if id.0 == "dark_mode" {
            if let Some(w) = &*window_guard {
                // Note: We'd need to get the actual checked state from the menu item
                let _ = w.emit("theme:change", json!({"dark": true}));
            }
        }
    });
}

/// Check if current menu item exists in menu window map
/// If it found focus the window
fn maybe_focus_window(id: &MenuId, app_handle: &tauri::AppHandle) {
    // Handle dynamic window menu items
    let menu_map: State<std::sync::Mutex<HashMap<String, MenuId>>> = app_handle.state();
    let menu_map = menu_map.lock().unwrap();
    for (label, item_id) in menu_map.iter() {
        if id == item_id {
            match app_handle.get_window(label) {
                Some(window) => {
                    window.unminimize().unwrap();
                    window.set_focus().unwrap();
                }
                None => {
                    log::error!("Not found window to focus {}", label);
                }
            }
        }
    }
}

pub fn update_titles(app_handle: &tauri::AppHandle) {
    let menu = app_handle.menu().unwrap();
    let window_submenu = menu.get(WINDOW_SUBMENU_ID).unwrap();
    let window_submenu = window_submenu.as_submenu().unwrap();
    let menu_map: State<std::sync::Mutex<HashMap<String, MenuId>>> = app_handle.state();
    let mut menu_map = menu_map.lock().unwrap();

    // Remove window menu and clear menu map
    menu_map.clear();

    let items = window_submenu.items().unwrap();
    for item in items.iter().skip(5) {
        window_submenu.remove(item).unwrap();
    }
    // Rebuild window items
    let windows = app_handle.webview_windows();

    // Collect windows and their titles into a vector
    let mut window_vec: Vec<(&String, &WebviewWindow)> = windows.iter().collect();

    // Sort the vector by the titles
    window_vec.sort_by(|a, b| {
        a.1.title()
            .unwrap_or_default()
            .cmp(&b.1.title().unwrap_or_default())
    });

    for (label, window) in window_vec.iter() {
        let title = window.title().unwrap_or("Unknown".into());
        let item: Box<dyn IsMenuItem<tauri::Wry>> =
            Box::new(MenuItem::new(app_handle, title, true, Some("")).unwrap());
        menu_map.insert(label.to_string(), item.id().clone());
        window_submenu.append(&*item).unwrap();
    }
}

// Helper functions for menu creation
fn create_standard_edit_menu(app_handle: &tauri::AppHandle) -> MenuItemCollection {
    menu_items![
        PredefinedMenuItem::undo(app_handle, None).unwrap(),
        PredefinedMenuItem::redo(app_handle, None).unwrap(),
        PredefinedMenuItem::separator(app_handle).unwrap(),
        PredefinedMenuItem::cut(app_handle, None).unwrap(),
        PredefinedMenuItem::copy(app_handle, None).unwrap(),
        PredefinedMenuItem::paste(app_handle, None).unwrap(),
        PredefinedMenuItem::select_all(app_handle, None).unwrap()
    ]
}

fn create_standard_app_menu(
    app_handle: &tauri::AppHandle,
    about_metadata: AboutMetadata,
    include_preferences: bool
) -> MenuItemCollection {
    let mut items = menu_items![
        PredefinedMenuItem::about(app_handle, None, Some(about_metadata)).unwrap()
    ];

    if include_preferences {
        items = items.add(MenuItem::new(app_handle, "Preferences…", true, Some("Cmd+,")).unwrap())
            .add(PredefinedMenuItem::separator(app_handle).unwrap());
    }

    items = items.add(PredefinedMenuItem::hide(app_handle, None).unwrap())
        .add(PredefinedMenuItem::hide_others(app_handle, None).unwrap())
        .add(PredefinedMenuItem::separator(app_handle).unwrap())
        .add(PredefinedMenuItem::quit(app_handle, None).unwrap());

    items
}

fn create_menu_items(
    app_handle: &tauri::AppHandle,
    menu_type: &MenuType,
    about_metadata: AboutMetadata,
) -> (MenuItemCollection, MenuItemCollection, Option<MenuItemCollection>, MenuItemCollection) {
    match menu_type {
        MenuType::FileData | MenuType::DbData { .. } => {
            // File menu
            let new_editor = MenuItemBuilder::new("New")
                .id(menu_ids::NEW_DOCUMENT)
                .accelerator("CmdOrCtrl+N")
                .build(app_handle)
                .unwrap();
            let open_editor = MenuItemBuilder::new("Open")
                .id(menu_ids::OPEN_DOCUMENT)
                .accelerator("CmdOrCtrl+O")
                .build(app_handle)
                .unwrap();
            let open_data = MenuItemBuilder::new("Open Data")
                .id(menu_ids::OPEN_DATA)
                .accelerator("CmdOrCtrl+D")
                .build(app_handle)
                .unwrap();

            let file_items = menu_items![
                new_editor.clone(),
                open_editor.clone(),
                open_data.clone(),
                PredefinedMenuItem::separator(app_handle).unwrap(),
                PredefinedMenuItem::close_window(app_handle, None).unwrap(),
            ];

            // Edit menu with insert
            let mut edit_items = create_standard_edit_menu(app_handle);
            let insert_menu_item = match menu_type {
                MenuType::FileData => {
                    MenuItemBuilder::new("New Row")
                        .id(menu_ids::GLOBAL_INSERT)
                        .accelerator("CmdOrCtrl+N")
                        .build(app_handle)
                        .unwrap()
                }
                MenuType::DbData { insert_id } => {
                    MenuItemBuilder::new("New Row")
                        .id(insert_id.clone())
                        .accelerator("CmdOrCtrl+N")
                        .build(app_handle)
                        .unwrap()
                }
                _ => unreachable!(),
            };
            edit_items = edit_items.add(PredefinedMenuItem::separator(app_handle).unwrap())
                                 .add(insert_menu_item);

            let app_menu_items = create_standard_app_menu(app_handle, about_metadata, true);

            (file_items, edit_items, None, app_menu_items)
        }
        MenuType::TextEditor => {
            // File menu
            let new_item = MenuItemBuilder::new("New")
                .id(menu_ids::NEW)
                .accelerator("CmdOrCtrl+N")
                .build(app_handle)
                .unwrap();
            let open_new_item = MenuItemBuilder::new("Open…")
                .id(menu_ids::OPEN)
                .accelerator("CmdOrCtrl+O")
                .build(app_handle)
                .unwrap();
            let save_item = MenuItemBuilder::new("Save")
                .id(menu_ids::SAVE)
                .accelerator("CmdOrCtrl+S")
                .build(app_handle)
                .unwrap();
            let save_as_item = MenuItemBuilder::new("Save As…")
                .id(menu_ids::SAVE_AS)
                .accelerator("CmdOrCtrl+Shift+S")
                .build(app_handle)
                .unwrap();
            let split_item = CheckMenuItem::with_id(app_handle, menu_ids::SPLIT_VIEW, "Split view", true, false, Some("")).unwrap();
            let dark_item = CheckMenuItem::with_id(app_handle, menu_ids::DARK_MODE, "Dark Mode", true, false, Some("")).unwrap();

            let file_items = menu_items![
                new_item.clone(),
                open_new_item.clone(),
                save_item.clone(),
                save_as_item.clone(),
                PredefinedMenuItem::separator(app_handle).unwrap(),
                split_item.clone(),
            ];

            // Edit menu
            let mut edit_items = create_standard_edit_menu(app_handle);
            edit_items = edit_items.add(PredefinedMenuItem::separator(app_handle).unwrap())
                .add(MenuItemBuilder::new("Insert")
                    .id(menu_ids::GLOBAL_INSERT)
                    .accelerator("CmdOrCtrl+I")
                    .build(app_handle)
                    .unwrap());

            // View menu
            let view_items = menu_items![
                PredefinedMenuItem::fullscreen(app_handle, None).unwrap(),
                split_item.clone(),
                dark_item.clone(),
            ];

            let app_menu_items = create_standard_app_menu(app_handle, about_metadata, false);

            (file_items, edit_items, Some(view_items), app_menu_items)
        }
    }
}

// Legacy function wrappers for backward compatibility
pub fn create_menu_document(
    app_handle: tauri::AppHandle,
    from_setup: bool,
) -> Result<tauri::menu::Menu<Wry>> {
    create_menu(app_handle, MenuType::FileData, from_setup)
}

pub fn create_menu_table(
    app_handle: tauri::AppHandle,
    insert_id: String,
) -> Result<tauri::menu::Menu<Wry>> {
    create_menu(app_handle, MenuType::DbData { insert_id }, false)
}

pub fn create_menu_editor(
    app_handle: tauri::AppHandle,
    from_setup: bool,
) -> Result<tauri::menu::Menu<Wry>> {
    create_menu(app_handle, MenuType::TextEditor, from_setup)
}

fn setup_menu_event_handlers(app_handle: &tauri::AppHandle, menu_type: &MenuType) {
    match menu_type {
        MenuType::FileData => {
            if !DOCUMENT_HANDLER_REGISTERED.load(Ordering::Relaxed) {
                DOCUMENT_HANDLER_REGISTERED.store(true, Ordering::Relaxed);
                let help_item = create_help_menu_item(app_handle).unwrap();
                setup_document_event_handler(app_handle.clone(), help_item);
            }
        }
        MenuType::DbData { insert_id } => {
            if !TABLE_HANDLER_REGISTERED.load(Ordering::Relaxed) {
                TABLE_HANDLER_REGISTERED.store(true, Ordering::Relaxed);
                let help_item = create_help_menu_item(app_handle).unwrap();
                setup_table_event_handler(app_handle.clone(), help_item, insert_id.clone());
            }
        }
        MenuType::TextEditor => {
            if !EDITOR_HANDLER_REGISTERED.load(Ordering::Relaxed) {
                EDITOR_HANDLER_REGISTERED.store(true, Ordering::Relaxed);
                setup_editor_event_handler(app_handle.clone());
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // Note: These tests are basic since full Tauri app testing requires more complex setup
    #[test]
    fn test_menu_type_variants() {
        // Test that MenuType variants can be created
        let _document = MenuType::FileData;
        let _table = MenuType::DbData { insert_id: "test".to_string() };
        let _editor = MenuType::TextEditor;

        // If we reach here, the enum variants are properly defined
        assert!(true);
    }
}
