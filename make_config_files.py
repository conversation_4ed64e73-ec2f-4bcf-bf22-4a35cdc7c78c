#!/usr/bin/env python3
"""
make_config_files.py - turn templates (that I created) into appropriate config files
	used ${VARIABLE_NAME} syntax like shell scripts, but doing the replacement with this Python script

caveats:
- does not account for icons; maybe we should make sure they're not stored in git?
"""

import json
import sys
import os

def main(app_name):
	configs = {
		# APP_NAME & PRODUCT_ID are computed below

		'data1': {
			'PRODUCT_NAME': 'Data Deeply',
			'VERSION': '0.1.0',
			'PORT': 5174,
		},
		'edit2': {
			'PRODUCT_NAME': 'Edit Deeply',
			'VERSION': '0.2.8',
			'PORT': 5173,
		},
		'make1': {
			'PRODUCT_NAME': 'Make Deeply',
			'VERSION': '0.1.0',
			'PORT': 5172,  # not in use; may have been added to get vite/CodeMirror to work with Brython
		},
	}

	for key, config_dict in configs.items():
		# data_deeply, edit_deeply, make_deeply ... documentation TBD: where is this used?
		config_dict['APP_NAME'] = config_dict['PRODUCT_NAME'].lower().replace(' ', '_')

		# com.makedeeply.(datadeeply|editdeeply|makedeeply)
		# & (datadeeply|editdeeply|makedeeply).sqlite
		# SEE ALSO: get_product_id in common.rs
		config_dict['PRODUCT_ID'] = config_dict['PRODUCT_NAME'].lower().replace(' ', '')

	next_command_lookup = {
		'make1': 'npx tauri dev --no-watch',

		'DEFAULT': 'npm run tauri dev',
		}

	if app_name in next_command_lookup:
		next_command = next_command_lookup[app_name]
	else:
		next_command = next_command_lookup['DEFAULT']

	# List of files to generate from .template versions that contain one or more ${VARIABLES}
	filenames = [
		'package.json',
		'src-tauri/Cargo.toml',
		'src-tauri/tauri.conf.json',
		'vite.config.js',
	]

	# =======================================================================================

	print()

	if app_name not in configs:
		print(f'Error: Unknown app "{app_name}". Must be one of', configs.keys())
		sys.exit(1)

	config = configs[app_name]

### PROBABLY DO NOT need these since this script attempts to change everything directly
	# Set environment variables once
# 	for key, value in config.items():
# 		os.environ[key] = str(value)
# 		print(f'Set {key}={value}')

	# Process each file
	for filename in filenames:
		template_filename = filename + '.template'

		if not os.path.exists(template_filename):
			# e.g. make1 doesn't use vite
			print('skipping since no such file: "%s"' % template_filename)
			continue

		# Read template
		with open(template_filename, 'r') as f:
			template_content = f.read()

		# Replace variables that are in standard 'env' format
		for key, value in config.items():
			template_content = template_content.replace(f'${{{key}}}', str(value))

		# Write final file
		print('writing', filename)
		with open(filename, 'w') as f:
			f.write(template_content)

	print()
	print('NEXT: "%s"' % next_command)
	print()

app_name = os.path.basename(os.path.dirname(os.path.abspath(__file__)))  # name of parent folder
main(app_name)


# if __name__ == '__main__':
# 	if len(sys.argv) != 2:
# 		print('Usage: python make_config_files.py <data1|edit2>')
# 		sys.exit(1)
#
# 	main(sys.argv[1])
