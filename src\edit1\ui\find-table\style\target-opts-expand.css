#target-opts-expand:checked ~ .target-opts > .name > .section-state > .down {
	display: block;
}

#target-opts-expand:not(:checked) ~ .target-opts > .name > .section-state > .down {
	display: none;
}

#target-opts-expand:checked ~ .target-opts > .name > .section-state > .right {
	display: none;
}

#target-opts-expand:not(:checked) ~ .target-opts > .name > .section-state > .right {
	display: block;
}

#target-opts-expand:checked ~ .target-opts {
	flex-wrap: wrap;
}

#target-opts-expand:checked ~ .target-opts .checkbox-row {
	align-items: start;
}

#target-opts-expand:not(:checked) ~ .target-opts .checkbox-row {
	align-items: center;
}

#target-opts-expand:checked ~ .target-opts .checkbox-row > .opt.with-list button {
	display: flex;
}

#target-opts-expand:not(:checked) ~ .target-opts .checkbox-row > .opt.with-list button {
	display: none;
}

#target-opts-expand:checked ~ .target-opts > .row-1 {
	flex: 1 1 100%;
}

#target-opts-expand:checked ~ .target-opts > .row-2 {
	flex: 1 1 100%;
	order: 1;
	flex-direction: column;
}

#target-opts-expand:checked ~ .target-opts > .row-2 > .opt.with-list > .list {
	display: flex;
	flex-direction: column;
}
