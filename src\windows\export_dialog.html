<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<title>Export</title>

		<!-- Brython -->
		<script type="text/javascript" src="/lib/brython/brython.js"></script>
		<script type="text/javascript" src="/lib/brython/brython_stdlib.js"></script>

		<!-- Tabulator -->
		<script type="text/javascript" src="/lib/tabulator/tabulator_custom.js"></script>
		<link rel="stylesheet" href="/lib/tabulator/tabulator_bulma.min.css" />

		<!--Extend Tabulator Module-->
		<script type="module" src="/windows/file.js" defer></script>

		<!-- 'deeply' startup code for every page -->
		<script type="text/javascript" src="/common/init_this_page.js"></script>

		<!-- imports -->
		<script src="/common/tauri.py" type="text/python" id="tauri"></script>
		<script src="/common/app_data.py" type="text/python" id="app_data"></script>
		<script src="/common/table_utils.py" type="text/python" id="table_utils"></script>

		<script src="/windows/open_file.py" type="text/python" id="open_file"></script>
		<script src="/windows/shortcuts.py" type="text/python" id="shortcuts"></script>

		<script src="export_dialog.py" type="text/python" id="export_dialog"></script>

		<!-- styles -->
		<link rel="stylesheet" href="/common/makedeeply.css" />
		<link rel="stylesheet" href="/windows/editors.css" />
	</head>
	<body id="export-dialog" class="editor-page">
		<div id="editor-container">
			<div id="save-table" hidden></div>
			<div id="export-editor">
				<div id="export-editor-table">
					<div id="export-editor-content"></div>
					<div id="static-layout-table"></div>
				</div>

				<div id="ok-cancel-btn-group">
					<button id="export-cancel-btn" class="ok-cancel">Cancel</button>
					<button id="export-confirm-btn" class="ok-cancel">Export</button>
				</div>
			</div>
		</div>
	</body>
</html>
