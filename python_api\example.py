from makedeeply import MakeDeeply
import time

api = MakeDeeply()

# when we create window tauri bring whole app to front so ensure it remains in background
app_focus_state = api.is_focused(api.MAIN_WINDOW_LABEL)
if not app_focus_state:
	api.set_focus(api.MAIN_WINDOW_LABEL, False)

# Create window
label = api.create_window('https://zhuohan.li/')
print(f'Created window with label {label}')

# Hide new window
api.set_visible(label, False)
# Remove focus (only after hiding to prevent animation)
# Automatically become visible but minized
api.set_focus(label, False)

# Wait to document to be ready
result = api.wait_for_document(label)
print(f'Ready state result: {result}')

# Get outer HTML
html = api.get_outer_html(label)
print(f'Label {label} has the following HTML: {html[:15]}..{html[-15:]}')

# Wait for redirects
print('Waiting for redirects')
time.sleep(2)
location = api.get_location(label)
print(f'Label {label} has the following location: {location}')

print('Closing in 3 seconds')
time.sleep(3)
api.close_window(label)
