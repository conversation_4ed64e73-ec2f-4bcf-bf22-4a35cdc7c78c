use std::{collections::HashMap, fs::{self, File}};
use std::io::Read;

use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>};

use crate::edit1::AutosaveMap;



pub fn load_autosave_map(app: &AppHandle) -> Result<AutosaveMap, String> {
    let data_dir = app.path().app_data_dir().unwrap();
    let autosave_dir = data_dir.join("autosave");

    if !autosave_dir.exists() {
        fs::create_dir_all(&autosave_dir).map_err(|e| format!("Failed to create autosave dir: {}", e))?;
    }

    let map_path = autosave_dir.join("autosave_map.json");

    if map_path.exists() {
        let mut file = File::open(&map_path).map_err(|e| e.to_string())?;
        let mut data = String::new();
        file.read_to_string(&mut data).map_err(|e| e.to_string())?;
        serde_json::from_str::<AutosaveMap>(&data).map_err(|e| e.to_string())
    } else {
        Ok(AutosaveMap {
            files: HashMap::new(),
        })
    }
}


pub fn save_autosave_map(app: &AppHandle, map: &AutosaveMap) -> Result<(), String> {
    let data_dir = app.path().app_data_dir().unwrap();
    let autosave_dir = data_dir.join("autosave");
    let map_path = autosave_dir.join("autosave_map.json");

    let json_data = serde_json::to_string_pretty(map).map_err(|e| e.to_string())?;
    fs::write(&map_path, json_data).map_err(|e| e.to_string())
}
