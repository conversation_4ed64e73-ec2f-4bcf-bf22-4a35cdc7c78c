use std::{fs::File, io::Write};

use tauri::{async_runtime::spawn, Emitter, Listener};
use tauri_plugin_dialog::DialogExt;

use super::{MyFile, State};

#[tauri::command]
pub async fn open_files(handle: tauri::AppHandle) -> Result<(), String> {
    handle.dialog().file().pick_files(move |file_paths| {
        if let Some(paths) = file_paths {
            for file_path in paths {
                let handle_clone = handle.clone();
                spawn(async move {
                    let my_file =
                        MyFile::read_file_data(file_path.into_path().unwrap()).expect("error");

                    let window =
                        create_new_editor(handle_clone.clone(), Some(my_file.clone().path)).await;
                    let _ = window.set_focus();
                    let w = window.clone();
                    window.listen("window:on-loaded", move |_| {
                        println!("window {:?} loaded!", w.label());
                        w.emit_to(
                            w.label(),
                            "file:on-open",
                            &(State {
                                label: w.label().to_string(),
                                file: my_file.clone(),
                            }),
                        )
                        .unwrap();
                    });
                });
            }
        }
    });
    Ok(())
}

#[tauri::command]
pub async fn open_data_files(handle: tauri::AppHandle) -> Result<(), String> {
    handle.dialog().file()
        .add_filter("Database Files", &["sqlite", "db", "sqlite3"])
        .pick_files(move |file_paths| {
            if let Some(paths) = file_paths {
                for file_path in paths {
                    let handle_clone = handle.clone();
                    let path_buf = file_path.into_path().unwrap();
                    let path_str = path_buf.to_string_lossy().to_string();
                    let src = format!("asset://localhost/{}", path_str);

                    spawn(async move {
                        // Use the existing open_file command to handle database files
                        if let Err(e) = crate::cmd::file::open_file(
                            handle_clone,
                            path_str,
                            src,
                            None,
                            None,
                            None,
                            None,
                        ).await {
                            eprintln!("Error opening database file: {}", e);
                        }
                    });
                }
            }
        });
    Ok(())
}
/***
 * parameters : file path,content and extension
 * description : save the file and send the new object to the window
 */
#[tauri::command]
pub fn editor_save_file(content: String, path: String, ext: String) -> MyFile {
    let mut file = File::create(&path).expect("can't open the file !!!");
    file.write_all(content.as_bytes())
        .expect("can't save the file !!!");
    MyFile { path, content, ext }
}

/***
 * parameters : window label and file content and extension
 * description : open the save as dialog and save the file on the new path
 */
#[tauri::command]
pub fn save_as_file(
    handle: tauri::AppHandle,
    label: String,
    content: String,
    _path: String,
    ext: String,
) {
    handle.dialog().file().save_file(move |path_buf| {
        if let Some(file_path) = path_buf {
            let path = file_path.into_path().unwrap();
            let file_path_str = path.to_string_lossy().to_string();
            let _file = editor_save_file(content.clone(), file_path_str.clone(), ext.clone());
            let file = MyFile::read_file_data(path).expect("error");
            handle.emit_to(&label, "file:on-saved-as", file).unwrap()
        }
    });
}

pub mod manage;

pub use manage::*;
