use crate::common;
use sqlx::{
	sqlite::{SqliteConnectOptions, SqlitePool},
	Pool, Sqlite,
};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};

pub fn database_url(app: AppHandle) -> anyhow::Result<String> {
	let product_id = common::get_product_id(app.clone());

	let config_dir = app.path().app_config_dir()
		.map_err(|e| anyhow::anyhow!("Failed to get app config dir: {}", e))?;

	// Ensure the config directory exists
	std::fs::create_dir_all(&config_dir)
		.map_err(|e| anyhow::anyhow!("Failed to create config directory '{}': {}", config_dir.display(), e))?;

	let db_path = config_dir.join(format!("{}.sqlite", product_id));

	let db_url = db_path.to_str()
		.ok_or_else(|| anyhow::anyhow!("Database path is not valid UTF-8: '{}'", db_path.display()))?;

	println!("Database URL: {}", db_url);
	Ok(db_url.to_string())
}

pub async fn connect(url: &str) -> anyhow::Result<Pool<Sqlite>> {
	println!("Attempting to connect to database: {}", url);

	let options = SqliteConnectOptions::new()
		.filename(url)
		.create_if_missing(true);

	let pool = SqlitePool::connect_with(options).await
		.map_err(|e| anyhow::anyhow!("Failed to connect to database '{}': {}", url, e))?;

	// run migrations
	sqlx::migrate!().run(&pool).await
		.map_err(|e| anyhow::anyhow!("Failed to run migrations on '{}': {}", url, e))?;

	println!("Successfully connected to database: {}", url);
	Ok(pool)
}

pub async fn add_pool_state(app: AppHandle) {
	let url = match database_url(app.clone()) {
		Ok(url) => url,
		Err(e) => {
			eprintln!("Error getting database URL: {}", e);
			return;
		}
	};

	let pool = match connect(&url).await {
		Ok(pool) => pool,
		Err(e) => {
			eprintln!("Error connecting to database: {}", e);
			return;
		}
	};

	app.manage(pool);
	println!("Database pool added to app state");
}
