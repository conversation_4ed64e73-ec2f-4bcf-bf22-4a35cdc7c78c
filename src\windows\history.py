"""
history.py - UI for File History window

refactor TBD: update to share code across edits.py, history.py, file_data.py, db_data.py, db_tables.py
"""

from browser import aio, document, window
from collections import OrderedDict
import json

# local to load first
import page_runner
import tauri

# local imports
# from file_sqlite import Database  # FYI vs similar code: history data is fetched via Rust
import deeply_utils
# import open_file  # for import-btn ==> not in edits, history or db_tables
import sync_utils
import table_utils

app_config = None  # must be loaded async
current_window = tauri.window.getCurrentWindow()

# -----
table_name = 'history'  # though actual get_rows is in Rust
table_type = 'history'


platform = {  # Scott TBD: obsolete? or refactor
	'using_windows': '',
	'using_other': '',
}

# -----
window_label = None  # edits uses sync_utils; history uses globals
window_title = None

# -----
def get_table():
	return sync_utils.table


async def reload_rows(tabulator):
	rows = await get_rows()  # row_objects
	tabulator.replaceData(rows)


# FUTURE: add link_out icon to edits & history

# edits & history: no cell editor
# edits & history: no insert new_row


# -----
async def main():
	global current_window

	global app_config
	app_config = await window.get_app_config()
	table_utils.app_config = app_config  # must be async so cannot be set on import
	sync_utils.app_config = app_config  # must be async so cannot be set on import

	await table_utils.add_flags_to_body()

	aio.run(sync_utils.run_check())  # check_column_width_queue

	# edits & history ==> no #import-btn

	document.querySelector('.spinner').classList.add('active')  # spinner ON

	sync_utils.table_data = await get_rows()  # row_objects

	create_table(sync_utils.table_data)

	document.querySelector('.spinner').classList.remove('active')  # spinner OFF

	window.tabulator = sync_utils.table

	# current_window is global
	# documentation TBD: compare this 'listen' across similar code

	# edits & history & db_tables: user cannot add/insert new row/record
	# edits & history & db_tables: no import_data

	# vs other scripts: each has a variation
	tauri.event.listen(f'table_{table_name}_update', lambda _: aio.run(reload_rows(sync_utils.table)))

	# refactor TBD: review/compare/document different close-requested behavior
	current_window.listen('tauri://close-requested', lambda *_args: aio.run(tauri.window.getCurrentWindow().destroy()))

	# edits & history: no import_data

	# refactor TBD: global window_title vs. sync_utils.window_title
	window_title = await current_window.title()
	window_label = current_window.label

	document['show-hidden-checkbox'].bind('change', lambda e: sync_utils.update_row_visibility(e, sync_utils.table, table_type))
	document['edit-layout'].bind('click', lambda e: aio.run(sync_utils.open_layout_editor(e)))
	document['edit-sort'].bind('click', lambda e: aio.run(sync_utils.open_sort_editor(e)))
	document['edit-search'].bind('click', lambda e: aio.run(sync_utils.open_search_editor(e)))
	document['export-btn'].bind('click', lambda e: aio.run(sync_utils.open_export_editor(e)))
	document['export-btn'].removeAttribute('disabled')

	document['select-all-btn'].bind('click', lambda event: sync_utils.select_all_checkboxes())
	document['select-none-btn'].bind('click', lambda event: sync_utils.deselect_all_checkboxes())
	document['hide-btn'].bind('click', lambda event: sync_utils.main_tabulator_row_toggle_visibility(event, sync_utils.table))

	# edits & history tables:
	# - no 'Define' column editor
	# - no 'insert_new_row'

# ^^^^^ END: main()

# history & edits: no special on_window_close() behavior

# -----
# edits, history & db_tables:
# - no 'Define' column editor
# - no 'insert_new_row'

#........................end.............................

# Scott TBD July: probably get rid of these too
def update_main_table_when_recreate(evt):
	sync_utils.on_table_data_loaded(evt, sync_utils.table)


def save_loaded_data(evt):
	sync_utils.on_table_data_loaded_init(evt, sync_utils.table)


def execute_save_loaded_data(evt):
	window.setTimeout(lambda: save_loaded_data(evt), 50)


# create table
def create_table(tabulator_data, is_initial=True):
	# assembles params then calls window.Tabulator.new()

	visibility_dict = {}  # true/false lookup table by column name; default TRUE

	if sync_utils.table:
		# this should correspond to is_initial FALSE but perhaps there are exceptions
		# some changes require rebuilding the table; this preserves then later restores checked/hidden
		sync_utils.save_row_states(sync_utils.table)  # preserve checked or hidden rows; not in 1.0 -- refactor TBD: redundant with sync_utils?
		sync_utils.table.destroy()
		sync_utils.table = None

	# these two may be unique to history.py ==> documentation TBD
	# ... both would normally get created below EXCEPT this has the 'if tabulator_data' wrapper
	final_data = []
	tabulator_columns = []

	# 'if' wrapper since edits & history could be empty ==> tabulator_data[0] will fail; currently let Tabulator show placeholder text
	if tabulator_data:
		if is_initial:
			# comparing similar code elsewhere: tabulator_data param vs. sync_utils.table_data
			sync_utils.columns = [key for key in dict(tabulator_data[0]).keys() if key not in table_utils.built_in_fields]

			# even though details window might be slightly useful for history, would look ugly with window icons ==> probably don't add even after creating a read-only details page
			sync_utils.columns = table_utils.add_internal_column_names(sync_utils.columns, with_window_icons=True)
			deeply_utils.pp('history.py - create_table - sync_utils.columns', sync_utils.columns)

			for col in sync_utils.columns:
				sync_utils.search_options_dropdown[col] = 'like'

		else:
			sync_utils.columns = []
			deeply_utils.pp('history.py - create_table with is_initial FALSE - header_data', sync_utils.header_data)
			for item in sync_utils.header_data:
				sync_utils.columns.append(item['name'])
				visibility_dict[item['name']] = item['visible']

		visibility_dict[table_utils.file_row] = False # for edits & history
		visibility_dict['path_id'] = False  # hack for history table

		column_dicts = table_utils.make_columns(sync_utils, visibility_dict)

		table_utils.add_column_details(column_dicts, is_history=True)  # edits & history: no editor_callback

		# include_details_icon=True currently only for user data (csv/txt or DB); should add READ ONLY for history 	# include_details_icon edits
		final_data = table_utils.get_extra_data(tabulator_data, False, hidden_rows=sync_utils.hidden_rows, checked_rows=sync_utils.checked_rows, include_details_icon=False)

	else:
		# rare: no data -- should only be true for edits & history
		column_dicts = []
		final_data = []

	sync_utils.table = window.Tabulator.new(
		'#table',
		{
			'height': 'auto',
			# 'rowHeight': 24, # rowHeight prevents text from wrapping onto multiple rows ==> don't use!
			'pagination': True,
			'paginationSize': table_utils.pagination_default,
			'paginationSizeSelector': table_utils.pagination_options,
			'paginationButtonCount': table_utils.pagination_buttons,
			'data': final_data,
			'columns': column_dicts,
			'movableColumns': True,
			'editorParams': {'elementAttributes': {'spellcheck': False}},
			'debugInvalidOptions': False,
			'debugInvalidComponentFuncs': False,
			'debugInitialization': False,
			'placeholder': 'No Data Available',  # only for history & edits
			# 'selectableRows': selectable_rows,  # for Delete button ==> only csv/txt and (when implemented) SQLite data
		},
	)

	sync_utils.table.on('tableBuilt', lambda: (
		# print('history.py -- create_table: table built'),
		table_utils.update_shown_entries(None, sync_utils.table, table_type),
		sync_utils.table.on('columnMoved', lambda column, columns: sync_utils.on_table_column_moved(column,columns, sync_utils.table)),
		sync_utils.table.on('columnResized', lambda column: sync_utils.column_width_updated(column)),
		sync_utils.table.on('dataSorted', lambda sorters, rows: sync_utils.on_table_data_sorted(sorters, rows, sync_utils.table)),
		sync_utils.table.on('headerClick', lambda event, column: sync_utils.on_header_click(event, column, sync_utils.table)),
		sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table)),

		# history & db_tables have window icons handled by on_cell_click
		sync_utils.table.on('cellClick', lambda e, cell: aio.run(on_cell_click(e, cell))),
		))

	# documentation TBD: why here AND after tableBuilt
	sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table))

	if is_initial:
		sync_utils.table.on('dataLoaded', execute_save_loaded_data)
	else:
		sync_utils.table.on('dataLoaded', update_main_table_when_recreate)

		# restore_row_states is for checked or hidden rows; not in 1.0
		window.setTimeout(lambda: sync_utils.restore_row_states(sync_utils.table, sync_utils.table_data), 50)

	window.tabulator = sync_utils.table


#...........................................end...........................................

# window icons: history & db_tables

async def on_cell_click(event, cell):
	field = cell.getField()
	if field not in table_utils.window_icon_fields:
		# history & db_tables only support clicks in these 2 cells of any row
		return

	# for now: treats click on the entire cell as if a click on the icon; may want to change that

	row_dict = dict(cell.getRow().getData())
	row_webview_label = row_dict.get('webview_label')

	if field == table_utils.restore_window_icon_field:
		webview = window.__TAURI__.webviewWindow
		windows = await webview.getAllWebviewWindows()
		# TBD: find better way to get the earliest
		for webviewWindow in windows:
			if dict(webviewWindow)['label'] == row_webview_label:
				webviewWindow.unminimize()
				webviewWindow.setFocus()
				break

	elif field == table_utils.new_window_icon_field:
		# Open file
		ext: str = row_dict.get('ext')
		path: str = row_dict.get('realpath')
		path_id: str = row_dict.get('path_id')
		src = tauri.convertFileSrc(path)  # TBD: document

		exists = await tauri.invoke('exists', {'path': path, 'pathId': path_id})
		if not exists:
			# a file stored in history may be gone
			window.alert('File Not Found!')

		if not table_utils.is_allowed_to_open_file_with_ext(ext):
			window.alert(f'{ext.upper()} files are not supported in this version')

		# usual case
		await table_utils.invoke_tauri('open_file', {'path': path, 'src': src, 'pathId': path_id}, 'history.py -- on_cell_click')

		# reload with (most of the time) icon change and new sort order
		await reload_rows(sync_utils.table)


# -----
# specific to history.py -- though other data tables also go to/from OrderedDict to add custom column(s) to each row

async def get_rows():
	# for history (unlike other data), most of the work is done in Rust
	rows = await tauri.invoke('get_history')  # history implemented in Rust instead of Python

	# history & db_tables need active_items for 'restore' icon (bring to front)
	active_items = {}

	try:
		active_items = await tauri.invoke('get_active_files_path')
		active_items = dict(active_items)  # convert from JSObject
	except Exception as e:
		print('history.py - get_rows: no active_items (ok if no data files are open)', e)
		# still need to add the 'open data' icon

	for i in range(len(rows)):
		row_dict = OrderedDict(dict(rows[i]))  # convert from JSObject; other code requires OrderedDict

		table_utils.add_window_restore_icons(row_dict, active_items.get(row_dict['realpath']))

		row = __BRYTHON__.pyobj2jsobj(dict(row_dict))  # Convert back to JSObject
		rows[i] = row

	return rows  # i.e. row_objects

sync_utils.setup_window_event_handlers(current_window, create_table, get_table)  # window object + 2 callbacks

page_runner.register_for_js()  # hardcoded for 'main'
