<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<title>History</title>

		<!-- Brython -->
		<script type="text/javascript" src="/lib/brython/brython.js"></script>
		<script type="text/javascript" src="/lib/brython/brython_stdlib.js"></script>

		<!-- Tabulator -->
		<script src="/lib/tabulator/tabulator.min.js"></script>
		<link rel="stylesheet" href="/lib/tabulator/tabulator_bulma.min.css" />

		<!--Extend Tabulator Module-->
		<script type="module" src="/windows/file.js" defer></script>

		<!-- 'deeply' startup code for every page -->
		<script type="text/javascript" src="/common/init_this_page.js"></script>
		<script src="/windows/page_runner.py" type="text/python" id="page_runner"></script>

		<!-- imports -->
		<script src="/common/tauri.py" type="text/python" id="tauri"></script>

		<script src="/common/app_data.py" type="text/python" id="app_data"></script>
		<script src="/common/table_utils.py" type="text/python" id="table_utils"></script>
		<script src="/windows/disable_window.py" type="text/python" id="disable_window"></script>
		<script src="/windows/sync_utils.py" type="text/python" id="sync_utils"></script>

		<script src="/windows/open_file.py" type="text/python" id="open_file"></script>

		<!-- styles -->
		<link rel="stylesheet" href="/common/makedeeply.css" />
		<link rel="stylesheet" href="/windows/editors.css" />
		<link rel="stylesheet" href="/windows/file_data.css" />
		<link rel="stylesheet" href="/windows/history.css" />
	</head>
	<body id="history-page" class="data-page">
		<div id="header-btn-container">
			<div>
				<button id="edit-search">Search</button>
				<button id="edit-sort">Sort</button>
				<button id="edit-layout">Layout</button>
			</div>
			<div>
				<button class="if_false" id="insert" style="visibility: collapse;">New</button>
			</div>
			<div>
				<button id="export-btn" disabled>Export</button>
				<button class="if_false" id="import-btn" style="visibility: collapse;">Import</button>
			</div>
			<div>
				<button id="delete-btn" disabled style="visibility: collapse;">Delete</button>
			</div>
			<div id="edit-rows">
				<button class="if_paid_or_trial can_select_multiple_rows" id="hide-btn" disabled>Hide</button>
				<div class="if_paid_or_trial can_select_multiple_rows">
					<input type="checkbox" id="show-hidden-checkbox" />
					<label for="show-hidden-checkbox" class="show-hidden-label" id="show-hidden-label">Show hidden</label>
				</div>
				<button class="if_paid_or_trial can_select_multiple_rows" id="select-all-btn">Select All</button>
				<button class="if_paid_or_trial can_select_multiple_rows" id="select-none-btn">Select None</button>
			</div>
			<div><!-- visual placeholder only: no Define button in Actions, Edits, History, db_tables -->
				<button class="if_false" id="edit-column" style="visibility: collapse;">Define</button>
			</div>
		</div>

		<div id="header-info-row">
			<span id="shown-records"></span>
		</div>

		<div id="table"></div>

		<script src="history.py" type="text/python"></script>

		<div class="spinner-container">
			<div class="spinner"></div>
		</div>
	</body>
</html>
