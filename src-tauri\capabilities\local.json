{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "local", "description": "enables the default permissions", "windows": ["main", "file_*", "history", "db_*", "table_*", "*", "search"], "plugins": {"fs": {"scope": ["**"]}}, "permissions": ["core:path:default", "core:event:default", "core:event:allow-emit", "core:window:default", "core:webview:allow-webview-close", "core:webview:allow-set-webview-focus", "core:app:default", "core:resources:default", "core:menu:default", "core:tray:default", "core:window:allow-set-maximizable", "core:window:allow-is-focused", "core:window:allow-set-minimizable", "core:window:allow-unmaximize", "core:window:allow-set-size", "core:window:allow-set-min-size", "core:window:allow-start-dragging", "core:window:allow-toggle-maximize", "core:window:allow-close", "core:window:allow-hide", "core:window:allow-show", "core:window:allow-set-visible-on-all-workspaces", "core:window:allow-is-visible", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-destroy", "core:window:allow-unminimize", "core:window:allow-set-focus", "core:window:allow-set-title", "sql:allow-load", "sql:allow-select", "sql:allow-execute", "sql:allow-close", "dialog:allow-open", "dialog:allow-save", "dialog:allow-message", "dialog:allow-confirm", "dialog:allow-ask", "fs:allow-app-read", "fs:allow-app-read-recursive", "store:default", "store:allow-load", "core:webview:default", "core:webview:allow-get-all-webviews"], "context": "local", "remote": {"urls": ["asset://*"]}}