"""
trial.py - for the dialog

CAVEAT: did NOT work ==> stick with js version
	I created this per an earlier change to help_{product_id}
	but later simplfied that so it's easy to call from anywhere: Brython or js
"""

from browser import document, window
from common.makedeeply import setDaysRemaining  # makedeeply.js

# initialize days remaining display
setDaysRemaining()

# bind buy button click
document.select('.buy')[0].bind('click', lambda e: window.__TAURI__.core.invoke('open_buy_page'))

async def init():
	print('\n**' * 3, 'trial.py -- init')
	help_btn = document.select('.learn')[0]

	help_btn.bind('click', lambda _: aio.run(table_utils.create_known_window('help')))


aio.run(init())
