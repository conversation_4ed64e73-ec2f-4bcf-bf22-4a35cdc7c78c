use crate::config;
use anyhow::{bail, Result};
use serde::{Deserialize, Serialize};
use tauri::Manager;

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")] // use camelCase in JS ?!? -- I prefer snake_case but make camelCase is required for Lemon Squeezy code? Or maybe only for previous implementation and it can be changed?
pub struct License {
	pub license_key: String,
	pub instance_id: String,
}

/// Save license in app config in JSON file
pub fn save(app: &tauri::AppHandle, license_key: String, instance_id: String) -> Result<()> {
	let config_dir = app.path().app_config_dir()?;
	let license_path = config_dir.join(config::LICENSE_PATH);
	let license: License = License {
		license_key,
		instance_id,
	};
	std::fs::write(license_path, serde_json::to_string_pretty(&license)?)?;
	Ok(())
}

/// Load license from app config
pub fn load(app: &tauri::AppHandle) -> Result<License> {
	let config_dir = app.path().app_config_dir()?;
	let license_path = config_dir.join(config::LICENSE_PATH);
	if license_path.exists() {
		let content = std::fs::read_to_string(license_path)?;
		let license: License = serde_json::from_str(&content)?;
		return Ok(license);
	}
	bail!("No license at {}", license_path.display())
}

/// Delete license JSON file in app config
pub fn delete(app: &tauri::AppHandle) -> Result<()> {
	let config_dir = app.path().app_config_dir()?;
	let license_path = config_dir.join(config::LICENSE_PATH);
	if license_path.exists() {
		std::fs::remove_file(license_path)?;
		return Ok(());
	}
	bail!("No license at {}", license_path.display())
}
