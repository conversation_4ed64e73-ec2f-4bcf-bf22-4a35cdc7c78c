<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<title>Record Details - DB record</title>

		<!-- Brython -->
		<script type="text/javascript" src="/lib/brython/brython.js"></script>
		<script type="text/javascript" src="/lib/brython/brython_stdlib.js"></script>

		<!-- 'deeply' startup code for every page -->
		<script type="text/javascript" src="/common/init_this_page.js"></script>
		<script src="/windows/page_runner.py" type="text/python" id="page_runner"></script>

		<!-- imports -->
		<script src="/common/tauri.py" type="text/python" id="tauri"></script>

		<script src="/common/app_data.py" type="text/python" id="app_data"></script>
		<script src="/common/db_utils.py" type="text/python" id="db_utils"></script>
		<script src="/common/file_sqlite.py" type="text/python" id="file_sqlite"></script>
		<script src="/common/table_utils.py" type="text/python" id="table_utils"></script>

		<script src="/windows/open_file.py" type="text/python" id="open_file"></script>
		<script src="/windows/shortcuts.py" type="text/python" id="shortcuts"></script>

		<!-- styles -->
		<link rel="stylesheet" href="/common/makedeeply.css" />
		<link rel="stylesheet" href="/windows/editors.css" />
		<link rel="stylesheet" href="/windows/db/table.css" />
		<link rel="stylesheet" href="/windows/db/details.css" />
	</head>
	<body id="record-details-page" class="details-page">
		<div id="header-btn-container">
			<div>
				<button id="save"></button>
			</div>

			<div class="navigate_table">
				<div class="titlebar-button" id="navbar-prev">
					<img src="/assets/chevron-back.svg" alt="back" />
				</div>
				<div class="titlebar-button" id="navbar-next">
					<img src="/assets/chevron-forward.svg" alt="forward" />
				</div>
			</div>

			<!--
			<div class="half-spacer"></div>

			<div class="titlebar-button" id="header-help">
				<img src="/assets/help.svg" title="help" role="button" />
			</div>
 			-->
		</div>

		<table id="table" class="data-table"></table>

		<script src="record_details.py" type="text/python"></script>
	</body>
</html>
