"""
search_dialog.py
"""

from browser import document, window, aio
import javascript
import json
import tauri

# local imports
import deeply_utils
import shortcuts  # runs the bind commands on import
import table_utils

search_editor_table = None
header_data = window['__PROPS__'].header_data
columns = window['__PROPS__'].columns
current_filters = window['__PROPS__'].current_filters
show_hidden = window['__PROPS__'].show_hidden
current_search_option_values = window['__PROPS__'].search_options_dropdown

search_operators = [
	# code below creates an '_' version of these for file.js, e.g. not_like, not_any, case_regex, not_case_regex
	# also '≤' to '<='
	# and '≥' to '>='
	'like', 'not like',
	'=', '!=',
	'any', 'not any',
	'starts', 'ends',
	'≤', '<', '>', '≥',
	'regex', 'not regex',
	'case regex', 'not case regex',
]

current_window = tauri.window.getCurrentWindow()
label = current_window.label[:current_window.label.rindex('_search_editor')]


def search_text_formatter(cell, *args):
	# deeply_utils.pp('search_dialog.py - search_text_formatter')

	field_name = cell.getRow().getData()['field']

	if field_name in table_utils.checkbox_fields:
		return None
	# else: fall thru to format

	input_field = document.createElement('input')
	input_field.type = 'text'
	input_field.setAttribute('spellcheck', 'false')
	input_field.value = cell.getValue()

	# TBD: is this required? the above already gets input_field.value
	for f in current_filters:
		if f['field'] == field_name:
			input_field.value = f['value']
			break

	dropdown = cell.getRow().getCell('combined').getElement().querySelector('select')

	# -----
	def on_blur(event):
		operator = dropdown.value
		input_value = input_field.value.strip()
		update_main_table_filter(field_name, input_value, operator)
	# -----

	input_field.bind('blur', on_blur)

	return input_field


def create_search_editor():
	# deeply_utils.pp('search_dialog.py - create_search_editor')
	global search_editor_table

	if search_editor_table:
		search_editor_table.destroy()
		search_editor_table = None

	search_columns = [
		{
			'field': 'label',
			'title': 'label',
			'headerSort': False,
			'width': 150,
		},
		{
			'field': 'field',
			'title': 'Label',
			'headerSort': False,
			'width': 150,
			'visible': False,
		},
		{
			'field': 'combined',
			'title': 'Operator',
			'formatter': search_editor_combined_column_formatter,
			'headerSort': False,
			'width': 140,
		},
		{
			'field': 'value',
			'title': 'Value',
			'formatter': search_text_formatter,
			'headerSort': False,
			'width': 200,
		}
	]

	# documentation TBD: why Unknown? what's the context?
	# (the 'exclude' fields are icons and internal row_visibility)
	filter_data = [
		{	'label': dict(col).get('label', 'Unknown Field'),
			'field': dict(col).get('name', 'Unknown Field'),
			'type': dict(col).get('filterType', 'like'),
			'value': dict(col).get('filterValue', '')
		} for col in filter(lambda x: x['name'] not in table_utils.exclude, header_data)
	]

	search_editor_config = {
		'height': 'auto',
		# 'rowHeight': 36,
		'data': filter_data,
		'layout': 'fitColumns',
		'columns': search_columns,
		'headerFilter': 'input',
		'editorParams': {'elementAttributes': {'spellcheck': False}},
	}

	search_editor_table = window.Tabulator.new(
		'#search-table',
		search_editor_config,
	)


def search_editor_combined_column_formatter(cell, *args):
	# globals not modified here: search_operators

	combined_container = document.createElement('div')
	combined_container.style.display = 'flex'
	combined_container.style.alignItems = 'center'
	combined_container.style.justifyContent = 'space-between'
	combined_container.style.width = '100%'

	field_name = cell.getRow().getData()['field']
	# print('["combined" is misnomer]: search_editor_combined_column_formatter -- callback when creating each popup; for field_name', field_name)

	if field_name in table_utils.checkbox_fields:
		checkbox = document.createElement('input')
		checkbox.type = 'checkbox'
		checkbox.style.marginRight = '5px'

		if field_name == table_utils.row_checked:
			checkbox.bind('change', on_checked_checkbox_change)
		else:
			checkbox.checked = show_hidden
			checkbox.bind('change', on_hidden_checkbox_change)

		combined_container.appendChild(checkbox)

	else:
		sort_order_dropdown = document.createElement('select')
		sort_order_dropdown.style.padding = '0 5px 0 5px'

		for option_text in search_operators:
			option = document.createElement('option')

			if option_text == '≤':
				option.value = '<='
			elif option_text == '≥':
				option.value = '>='
			else:
				option.value = option_text.replace(' ', '_')  # create _ version for file.js

			option.text = option_text
			sort_order_dropdown.appendChild(option)

		sort_order_dropdown.value = dict(current_search_option_values).get(field_name)

		# -----
		def on_dropdown_change(event):
			event.stopImmediatePropagation()
			field_name = cell.getRow().getData()['field']
			operator = sort_order_dropdown.value
			# print('search_editor_combined_column_formatter: field_name, operator', field_name, operator)

			update_main_table_filter(field_name, None, operator, True)
		# -----

		sort_order_dropdown.bind('change', on_dropdown_change)
		combined_container.appendChild(sort_order_dropdown)

	return combined_container


def on_checked_checkbox_change(event):
	checked_checkbox_filter_status = False
	if event.target.checked:
		checked_checkbox_filter_status = True
	update_main_table_checkbox_filters(True, False, checked_checkbox_filter_status, False)


def on_hidden_checkbox_change(event):
	hidden_checkbox_filter_status = False
	if event.target.checked:
		hidden_checkbox_filter_status = True

	#update_main_table_checkbox_filters(False, True, False, hidden_checkbox_filter_status)
	args = {'label': label, 'showHide': str(hidden_checkbox_filter_status)}
	tauri.invoke('update_main_table_show_hidden', args)  # emit update_show_hidden


def checked_checkbox_filter(data, filter_params, *args):
	data_value = data[table_utils.row_checked]
	value_to_match = filter_params['value']
	if not value_to_match:
		return True
	return data_value == value_to_match


seen = window.WeakSet.new()  # documentation TBD

def get_circular_replacer(key, value):
	# print('get_circular_replacer')
	if isinstance(value, dict) and value is not None:
		if seen.js_has(value):
			return None
		seen.js_add(value)
	return value


def update_main_table_filter(field=None, value=None, operator=None, only_operator_changes=False):
	# deeply_utils.pp('search_dialog.py - update_main_table_filter - locals()', locals())
	global current_filters
	cur_filters = current_filters


	if only_operator_changes:
		for fltr in cur_filters:
			if fltr['field'] == field:
				fltr['type'] = operator
				break
	else:
		cur_filters = [f for f in cur_filters if f['field'] != field]
		if value is not None and value != '' and value != javascript.UNDEFINED:
			cur_filters.append({
				'field': field,
				'type': operator,
				'value': value,
				'params': {'regexFlags': 'i'},  # NOT case sensitive; probably safe to include always
				})

	current_filters = cur_filters

	args = {
		'filters': javascript.JSON.stringify(
			{
				'current_filters': current_filters,
				'op_change': {field: operator}
			},
			get_circular_replacer  # CALLBACK
			),
		'label': label,
		}
	seen = window.WeakSet.new()  # documentation TBD: e.g not declared global so seems like a no-op

	tauri.invoke('update_main_table_search', args)  # emit update_main_table_search_filters


def update_search_editor_data(tauri_message):
	print('update_search_editor_data -- invoked indirectly when user changes search header in parent window')
	global current_filters
	p_load = json.loads(dict(tauri_message)['payload'])
	field = p_load['field']
	value = p_load['value']
	operator = p_load['operator']

	if search_editor_table:
		found = False
		rows = search_editor_table.getRows()
		for row in rows:
			if row.getData()['field'] == field:
				input_element = row.getElement().querySelector("input[type='text']")
				if input_element:
					input_element.value = value

				select_element = row.getElement().querySelector('select')
				if select_element:
					select_element.value = operator

				break

		for filter in current_filters:
			if filter['field'] == field:
				found = True
				filter['type'] = operator
				filter['value'] = value
				break

		if not found:
			current_filters.append({
				'field': field,
				'type': operator,
				'value': value,
				})


def update_main_table_checkbox_filters(checked, hidden, checked_checkbox_checked, hidden_checkbox_checked):
	args = {
		'filters': json.dumps({
			'checked': checked,
			'checked_checkbox_filter_status': checked_checkbox_checked,
			'hidden': hidden,
			'hidden_checkbox_filter_status': hidden_checkbox_checked,
			}),
		'label': label,
	}
	tauri.invoke('update_main_table_search_checkboxes', args)  # emit update_main_table_checkbox_search_filters


def update_search_editor_order(new_order):
	if not search_editor_table:
		return

	rows = search_editor_table.getRows()
	current_operators = {}
	for row in rows:
		field_name = row.getData()['field']
		if row.getElement().querySelector('select'):
			operator = row.getElement().querySelector('select').value
			current_operators[field_name] = operator
		else:
			current_operators[field_name] = row.getElement().querySelector('input[type="checkbox"]').checked

	search_data = search_editor_table.getData()
	field_to_data = {data['field']: data for data in search_data}
	updated_search_data = [
		field_to_data[field] for field in new_order if field in field_to_data
	]

	search_editor_table.setData(updated_search_data)
	search_editor_table.redraw(True)

	rows = search_editor_table.getRows()
	for row in rows:
		field_name = row.getData()['field']
		if row.getElement().querySelector('select'):
			row.getElement().querySelector('select').value = current_operators[field_name]
		else:
			row.getElement().querySelector('input[type="checkbox"]').checked = current_operators[field_name]


def update_search_columns(tauri_message):
	# deeply_utils.pp('update_search_columns')
	global header_data, columns, search_editor_table
	p_load = json.loads(dict(tauri_message)['payload'])

	header_data = p_load['header_data']
	columns = p_load['columns']
	# print('update_search_columns: p_load', p_load)
	if search_editor_table:
		search_editor_table.destroy()
		create_search_editor()


def update_checkbox(tauri_message):
	checked = json.loads(dict(tauri_message)['payload'])['checked_value']
	rows = search_editor_table.getRows()

	for row in rows:
		checkbox = row.getElement().querySelector('input[type="checkbox"]')
		field = row.getElement().querySelector(f'div[tabulator-field="field"]').innerHTML == table_utils.row_checked
		if checkbox and field:
			checkbox.checked = checked
			break


def update_hidden_checkbox(tauri_message):
	show_or_hide = False if dict(tauri_message)['payload'] == 'False' else True
	rows = search_editor_table.getRows()

	for row in rows:
		checkbox = row.getElement().querySelector('input[type="checkbox"]')
		field = row.getElement().querySelector(f'div[tabulator-field="field"]').innerHTML == table_utils.row_hidden
		if checkbox and field:
			checkbox.checked = show_or_hide
			break

# -----
current_window.listen('tauri://close-requested', lambda *_args: aio.run(tauri.window.getCurrentWindow().destroy()))

current_window.listen('update_editor_field', lambda payload: sync_deb_to_editor(payload))  # calls update_search_editor_data
current_window.listen('change_column_order', lambda payload: sync_deb_to_col_order(payload))
current_window.listen('change_search_columns', lambda payload: sync_deb_to_col_change(payload))
current_window.listen('update_checked_checkbox', lambda payload: sync_deb_to_checkbox_change(payload))
current_window.listen('update_hidden_checkbox',lambda payload: sync_deb_to_hidden_checkbox_change(payload))

sync_deb_to_editor = table_utils.debounce(lambda payload: update_search_editor_data(payload), 1000)
sync_deb_to_col_order = table_utils.debounce(lambda payload: update_search_editor_order(json.loads(dict(payload)['payload'])['new_order']), 1000)
sync_deb_to_col_change = table_utils.debounce(lambda payload: update_search_columns(payload), 1000)
sync_deb_to_checkbox_change = table_utils.debounce(lambda payload: update_checkbox(payload), 1000)
sync_deb_to_hidden_checkbox_change = table_utils.debounce(lambda payload: update_hidden_checkbox(payload), 1000)

create_search_editor()  # no app_config so this should suffice
