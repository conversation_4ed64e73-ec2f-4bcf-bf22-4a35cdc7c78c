"""
file_data.py - UI for regular data files: csv, tsv, json, html

	SEE ALSO: file.py for a few utils; should refactor

refactor TBD: update to share code across edits.py, history.py, file_data.py, db_data.py, db_tables.py
"""

from browser import aio, document, window
from collections import OrderedDict
import json

# local to load first
import page_runner
import tauri

# local imports
from file_sqlite import ActionsSQLite, DataSQLite, EditsSQLite
import app_data
import db_utils
import deeply_utils
import open_file  # for import-btn
import process_files
import save_utils
import sync_utils
import table_utils

app_config = None  # must be loaded async
current_window = tauri.window.getCurrentWindow()  # TBD: or None then fetch later

# -----
# caveat: there's a risk these aren't ready at import time ==> TBD: move to main()

PROPS = None
try:
	PROPS = dict(window['__PROPS__'])
except Exception as e:
	print('ERROR in file_data: could not get __PROPS__', e)

INSERT_ID = PROPS['insert_id']
FILE_PATH = PROPS['path']
file_name = PROPS['name']
file_type = PROPS['ext']  # from Rust: no leading dot (unlike Python)

current_checksum  = window.__PROPS__['current_checksum']
previous_checksum = window.__PROPS__['prev_checksum']

# -----
last_ignored_changes_date = None

# the relevant subset of 'edits' data; 'preview' is something of a misnomer
preview_tabulator = None
preview_data = []

# -----
# table_name of the cache DB will be the filename (quoted when used in a query)
table_type = 'csv_tsv'  # or 'data' (including html & json) i.e. not db


# -----
window_label = None  # edits uses sync_utils; history, file_data & db_tables use globals
window_title = None

# -----
sync_utils.table = None  # TBD: review & compare
def get_table():
	return sync_utils.table  # TBD: table vs. sync_utils.table


# no reload: Tabulator is the source of truth while the file is open


# -----
async def main():
	global current_window

	global app_config
	app_config = await window.get_app_config()
	open_file.app_config = app_config  # must be async so cannot be set on import
	sync_utils.app_config = app_config  # must be async so cannot be set on import
	table_utils.app_config = app_config  # must be async so cannot be set on import

	await table_utils.add_flags_to_body()

	aio.run(sync_utils.run_check())  # check_column_width_queue

	open_file.bind_this_button('#import-btn')  # only file_data.py & db_data.py support Import

	table_utils.check_os(sync_utils.platform)  # sets platform; should be refactored

	# documentation TBD: which windows need this and why?
	sync_utils.window_title = await current_window.title()
	sync_utils.window_label = current_window.label

	# -------------------
	# was in main_table_helper.py -- not sure this is the best approach

	set_edits_actions_dbs()  # refactor TBD: ugly hack; should be able to load as needed

	# ^^^^^^^^^^^^^^^^^^^

	# unlike similar code: don't yet know whether it's possible to read the actual data; may have to wait on user to decide old vs new
	sync_utils.table_data = await get_rows()

	if sync_utils.table_data:
		# deeply_utils.pp('sync_utils.table_data', sync_utils.table_data)
		create_table(sync_utils.table_data)  # lots of action here
	# else: proceed with creating the window -- the above will add overlay for reviewing edits

	# documentation TBD: should there be a spinner ON before and OFF after?

	# 'close' implementation varies
	current_window.listen('tauri://close-requested', lambda _: aio.run(table_utils.close_window()))

	# ---
	# import: file_data.py & db_data.py
	current_window.listen('import_data', lambda payload: add_new_data_deb(payload))

	if not table_utils.is_mac():
		# TBD: test on Windows & Linux
		window.bind('keydown', lambda event: (aio.run(on_shortcut(event))))
	# else: macOS handles this via the menu item's keyboard shortcut
	# ^^^^^ import ^^^^^

	# ---
	# unique to file_data.py?
	tauri.event.listen('update_table', lambda _ignore: aio.run(update_table()))

	if app_config.is_paid_or_trial:
		# handle File > Save
		current_window.listen('save_file_overwrite', lambda payload: aio.run(save_utils.overwrite_file(payload, get_cached_data)))
	# ^^^^^ for db_data.py

	document['show-hidden-checkbox'].bind('change', lambda e: sync_utils.update_row_visibility(e, sync_utils.table, table_type))
	document['edit-layout'].bind('click', lambda e: aio.run(sync_utils.open_layout_editor(e)))
	document['edit-sort'].bind('click', lambda e: aio.run(sync_utils.open_sort_editor(e)))
	document['edit-search'].bind('click', lambda e: aio.run(sync_utils.open_search_editor(e)))
	document['export-btn'].bind('click', lambda e: (e.stopImmediatePropagation(), aio.run(sync_utils.open_export_editor(e))))

	document['select-all-btn'].bind('click', lambda event: sync_utils.select_all_checkboxes())
	document['select-none-btn'].bind('click', lambda event: sync_utils.deselect_all_checkboxes())
	document['hide-btn'].bind('click', lambda event: sync_utils.main_tabulator_row_toggle_visibility(event, sync_utils.table))

	# csv/txt and db ==> file_data.py & db_data.py
	document['edit-column'].bind('click', lambda e: (e.stopImmediatePropagation(), aio.run(sync_utils.open_column_editor(e))))
	document['insert'].bind('click', lambda e: (e.stopImmediatePropagation(), insert_new_row()))

	document['delete-btn'].bind('click', lambda e: (e.stopImmediatePropagation(), aio.run(sync_utils.delete_selected_row())))

	# history.py adds table_history_update listener here -- or maybe above?

# ^^^^^ END: main()


# -----
# sync between DEFINE (column editor) and data table
# (only applies to the csv/txt and (when implemented) DB data tables i.e. file_data.py & db_data.py)

definitions_changed_DEBOUNCE = table_utils.debounce(lambda payload: aio.run(sync_utils.definitions_changed(payload, sync_utils.table, table_type, sync_utils.cache_db, create_table, get_cached_data)), 1000)

current_window.listen('change_table_columns', lambda payload: definitions_changed_DEBOUNCE(payload))

#........................end.............................

# SQLite TBD: implement for SQLite data i.e. in db_data.py (ideally refactored to call the same function)
def custom_editor(cell, on_rendered_CALLBACK, success_CALLBACK, cancel_CALLBACK, editorParams):
	# callback function for tabulator
	# change the cell to a textarea for the user to edit

	# onRendered, success & cancel are callbacks that it provides
	textarea = document.createElement('textarea')
	textarea.style.width = '100%'
	textarea.style.boxSizing = 'border-box'  # so that scrollbar will fit

	textarea.value = cell.getValue() or ''  # handle NULL

	on_rendered_CALLBACK(lambda: textarea.focus())

	# -----
	def handle_change():
		original = cell.getValue() or ''  # handle NULL

		if textarea.value != original:
			# cell_edited(old_value, new_value, row_id, field)
			aio.run(cell_edited(original, textarea.value, cell.getRow().getData()[table_utils.file_row], cell.getField()))

			success_CALLBACK(textarea.value)
		else:
			cancel_CALLBACK()
	# -----

	textarea.addEventListener('change', lambda e: handle_change())
	textarea.addEventListener('blur', lambda e: handle_change())

	return textarea


#........................end.............................


# Scott TBD July: probably get rid of these too
def update_main_table_when_recreate(evt):
	sync_utils.on_table_data_loaded(evt, sync_utils.table)


def save_loaded_data(evt):
	sync_utils.on_table_data_loaded_init(evt, sync_utils.table)


def execute_save_loaded_data(evt):
	window.setTimeout(lambda: save_loaded_data(evt), 50)


# create table
def create_table(tabulator_data, is_initial=True):
	# assembles params then calls window.Tabulator.new()

	deeply_utils.pp('file_data.py - create_table - len(tabulator_data)', len(tabulator_data))

	sync_utils.create_table_done = False  # a 'lock' for column definitions; TBD: add to db_data.py when relevant
	if not tabulator_data:
		raise Exception('no data')

	visibility_dict = {}  # true/false lookup table by column name; default TRUE

	if sync_utils.table:
		# this should correspond to is_initial FALSE but perhaps there are exceptions
		# some changes require rebuilding the table; this preserves then later restores checked/hidden
		sync_utils.save_row_states(sync_utils.table)  # preserve checked or hidden rows; not in 1.0 -- refactor TBD: redundant with sync_utils?
		sync_utils.table.destroy()
		sync_utils.table = None

	if is_initial:

		# comparing similar code elsewhere: tabulator_data param vs. tabulator_data
		if False:###DEBUG
			print('\n-' * 3)
			print('file_data.py - create_table - is_initial True')
			print('tabulator_data[0].keys()', tabulator_data[0].keys())
			print('table_utils.built_in_fields', table_utils.built_in_fields)
		sync_utils.columns = [key for key in dict(sync_utils.table_data[0]).keys() if key not in table_utils.built_in_fields]

		# is_customer_data=False for (history, edits, actions, db_tables)
		sync_utils.columns = table_utils.add_internal_column_names(sync_utils.columns, is_customer_data=True, with_details_icon=True)

		for col in sync_utils.columns:
			sync_utils.search_options_dropdown[col] = 'like'

	else:
		# already have header_data ==> build from there
		sync_utils.columns = []
		for item in sync_utils.header_data:
			sync_utils.columns.append(item['name'])
			visibility_dict[item['name']] = item['visible']

	# FYI: column_dicts is essentially new_header_data
	column_dicts = table_utils.make_columns(sync_utils, visibility_dict)
	table_utils.add_column_details(column_dicts, editor_CALLBACK=custom_editor)  # only csv/txt and (when implemented) SQLite data are editable

	# final_data = tabulator_data  # elsewhere: table_utils.get_extra_data(tabulator_data, is_customer_data=True, hidden_rows=hidden_rows)
	final_data = table_utils.get_extra_data(tabulator_data, False, hidden_rows=sync_utils.hidden_rows, checked_rows=sync_utils.checked_rows, include_details_icon=False)

	# for Delete button ==> only csv/txt and (when implemented) SQLite data
	if app_config.is_paid_or_trial:
		selectable_rows = 1  # or True for any number, False for 0
	else:
		selectable_rows = False  # might add 1 in future for cmd-space to open READ ONLY detail view

	sync_utils.table = window.Tabulator.new(
		'#table',
		{
			'height': 'auto',
			# 'rowHeight': 24, # rowHeight prevents text from wrapping onto multiple rows ==> don't use!
			'pagination': True,
			'paginationSize': table_utils.pagination_default,
			'paginationSizeSelector': table_utils.pagination_options,
			'paginationButtonCount': table_utils.pagination_buttons,
			'data': final_data,
			'columns': column_dicts,
			'movableColumns': True,
			'editorParams': {'elementAttributes': {'spellcheck': False}},
			'debugInvalidOptions': False,
			'debugInvalidComponentFuncs': False,
			'debugInitialization': False,
			# placeholder only needed for history & edits
			'selectableRows': selectable_rows,  # for Delete button ==> only csv/txt and (when implemented) SQLite data
		},
	)

	sync_utils.table.on('tableBuilt', lambda: (
		# print('file_data -- create_table: table built'),
		on_listeners()  # SEE BELOW though the related UI files have the contents embedded
		))

	sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table))

	if is_initial:
		sync_utils.table.on('dataLoaded', execute_save_loaded_data)
	else:
		sync_utils.table.on('dataLoaded', update_main_table_when_recreate)

		# restore_row_states is for checked or hidden rows; not in 1.0
		window.setTimeout(lambda: sync_utils.restore_row_states(sync_utils.table, sync_utils.table_data), 50)

	# sync_utils.table.on('renderComplete', lambda: print('FYI: render Complete'))

	window.tabulator = sync_utils.table

	sync_utils.create_table_done = True  # a 'lock' for column definitions; documentation TBD: why here but not elsewhere?


def on_listeners():
	# refactor TBD: the related UI files have this code embedded above; should probably move to table_utils
	table_utils.update_shown_entries(None, sync_utils.table, table_type),
	sync_utils.table.on('columnMoved', lambda column, columns: sync_utils.on_table_column_moved(column,columns, sync_utils.table))
	sync_utils.table.on('columnResized', lambda column: sync_utils.column_width_updated(column))
	sync_utils.table.on('dataSorted', lambda sorters, rows: sync_utils.on_table_data_sorted(sorters, rows, sync_utils.table))
	sync_utils.table.on('headerClick', lambda event, column: sync_utils.on_header_click(event, column, sync_utils.table))
	sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table))

	# open details window
	sync_utils.table.on('cellClick', lambda event, cell: table_utils.on_cell_click(event, cell))  # varies across similar code


#...........................................end...........................................

# specific to file_data.py? ==> should probably be moved elsewhere
# ... though many of these should be added to db_tables.py since csv/txt and sqlite features should match (except for unavoidable differences)

async def update_table(columns_to_select=None):
	# calls tabulator's replaceData ... saving/restoring checked/hidden and other housekeeping
	# columns_to_select is empty or non-built-ins; not sure why

	# TBD: probably implement the equivalent for SQLite then refactor into a common function

	if sync_utils.table is None:
		# TBD: is this an error that should be flagged?
		return

	await get_cached_data(columns_to_select)

	sync_utils.save_row_states(sync_utils.table)  # save checked/hidden -- is this required here? redundant with other code?

	# performance TBD: when called for editing a single cell, does this refresh entire data?
	sync_utils.table.replaceData(sync_utils.table_data)  # put current data into existing tabulator object

	window.setTimeout(lambda: sync_utils.restore_row_states(sync_utils.table, sync_utils.table_data), 50)  # restore checked/hidden

	# not sure if this is necessary since the above code only replaced table_data -- but perhaps it is
	sync_utils.bind_header_filter_events(sync_utils.table)


def insert_new_row(e=None):
	# refactor TBD: align file_data.py & db_data.py

	if e:
		e.stopImmediatePropagation()

	if not app_config.is_paid_or_trial:
		table_utils.notify_user()
		return

	if window.tabulator is None:
		print('WARNING or ERROR: window.tabulator is NONE')
		return

	args = {'path': FILE_PATH, 'tableName': file_name}
	tauri.invoke('open_new_row_window_file', args)  # open detail window for user to enter data  # TBD: table_utils.run?


async def on_shortcut(event):
	# SEE ALSO: on_shortcut in shortcuts.py
	shortcut_action = table_utils.get_shortcut_action(event)

	if shortcut_action == 'open':
		await open_file.open_file(from_menu=True)

	elif shortcut_action == 'new row' and app_config.is_paid_or_trial:
		insert_new_row()

	elif shortcut_action == 'save' and app_config.is_paid_or_trial:
		await save_utils.overwrite_file({}, get_cached_data, current_window.label)


def menu_new_row(tauri_message):
	# documentation TBD: explain e.g. why can't Mac use INSERT_ID?
	current_window = tauri.window.getCurrentWindow()

	if table_utils.is_mac():
		label = tauri_message.payload.label
		if label == current_window.label:
			insert_new_row()
	else:
		insert_id = tauri_message.payload.insert_id
		if insert_id == INSERT_ID:
			insert_new_row()

# ================= (reorganized)

#.......................end..............................

# new row/record
# (only applies to the csv/txt and (when implemented) DB data tables i.e. file_data.py & db_data.py)
menu_new_row_DEBOUNCE = table_utils.debounce(lambda payload: menu_new_row(payload), 2000)
current_window.listen('add_new', lambda payload: menu_new_row_DEBOUNCE(payload))

#..................................... cache db code.............................................................
sync_utils.cache_db = None  # instance of DataSQLite for cached copy of file data

# refactor to CONSIDER: make edits_db (renamed edits_wrapper?) a global to match sync_utils.cache_db, then only load_db if null


async def create_cache_db(rows):
	# globals not modified: file_name

	deeply_utils.pp('>>>>>>>>>>>> file_data.py - create_cache_db - len(rows), file_name', len(rows), file_name)

	sync_utils.cache_db = DataSQLite(file_name, rows)  # awkward: stores the rows but doesn't do anything with them

	await sync_utils.cache_db.load_db()  # refactor TBD: why not do this on init?

	await sync_utils.cache_db.create_cache_table()  # creates an empty table; must have data (via above constructor) for deriving the columns

	await sync_utils.cache_db.insert_data_as_batch()  # uses rows specified in constructor

	deeply_utils.pp('file_data.py - create_cache_db - DONE with insert_data_as_batch')

	# nothing to return: the caller already has rows of data


async def read_cache_db():
	# globals not modified: file_name

	sync_utils.cache_db = DataSQLite(file_name)

	await sync_utils.cache_db.load_db()  # refactor TBD: why not do this on init?

	deeply_utils.pp('read_cache_db - will call sync_utils.cache_db.get_all_db_data()')
	return await sync_utils.cache_db.get_all_db_data()


# async def create_db_table(use_cached_db=False):
# 	# this loads the DB -- creating it if needed ==> refactor TBD: consider renaming or refactoring based on how it's used
# 	# globals not modified here: file_name
#
# 	# performance TBD: if use_cached_db is
# 	# - True then should use sync_utils.table_data ==> probably no need to call get_cached_data
# 	# - False then
# 	sync_utils.cache_db = DataSQLite(file_name, sync_utils.table_data)
#
# 	await sync_utils.cache_db.load_db()  # refactor TBD: why not do this on init?
#
# 	if use_cached_db:
# 		exists = await sync_utils.cache_db.check_table_exists()
# 	else:
# 		exists = False  # or None
#
# 	if use_cached_db and exists:
# 		# await get_cached_data()  # Scott TBD July: does it work after I comment out?
# 		pass
# 	else:
# 		# create
# 		await sync_utils.cache_db.create_cache_table()
# 		await sync_utils.cache_db.insert_data_as_batch()
# 		# performance TBD: likely not efficient to FIRST create THEN get_cached_data
# 		# await get_cached_data()  # Scott TBD July: does it work after I comment out?


async def get_cached_data(columns_to_select=None):
	# performance TBD: does Tabulator let us load 1 page worth of records first to see something right away, then load the remainder incrementally so that search/sort still has access to all data?

	if not sync_utils.cache_db:
		print('ERROR: no sync_utils.cache_db object')

	if columns_to_select: # to avoid a wierd error: index out of bounds: the len is 11* but the index is 11*
		rows = await sync_utils.cache_db.select_db_data(columns_to_select)
	else:
		rows = await sync_utils.cache_db.get_all_db_data()

	if True:###debug
		row1 = rows[0]
		deeply_utils.pp('row1', row1)
	# Scott TBD July: this is repeated in create_table so probably delete here
	# include_details_icon=True currently only for user data (csv/txt or DB); should add READ ONLY for history & edits
	# rows = table_utils.get_extra_data(rows, True, hidden_rows=sync_utils.hidden_rows, checked_rows=sync_utils.checked_rows, include_details_icon=True)

	# performance/memory note: these should be references ==> low overhead ... but still ugly ==> refactor TBD
	sync_utils.table_data = rows

#..........................................end...............................................................
# sync between main table and import dialog
add_new_data_deb = table_utils.debounce(lambda payload: aio.run(add_new_data(payload)), 2000)


async def add_new_data(tauri_message):
	# import data
	# globals not modified here: sync_utils.cache_db
	if not sync_utils.cache_db:
		print('ERROR in add_new_data: no sync_utils.cache_db object')
		return

	timestamp = deeply_utils.now_string()

	# print('file_data.py: add_new_data(tauri_message)', tauri_message)
	payload = json.loads(dict(tauri_message)['payload'])  # from import_dialog.py merge_data(e)
	# print('payload[0]', payload[0])

	imported_from_file_name = payload.pop()  # hack: grab and remove the last item (ugly but not worth refactoring right now)

	# the remaining payload is row_dicts
	for row in payload:
		row[table_utils.mod_date] = timestamp  # will search for this to show the imported data (and useful in general)
		row[table_utils.mod_note] = 'imported from ' + imported_from_file_name

	await sync_utils.cache_db.set_column_globals(payload[0])
	await sync_utils.cache_db.insert_data_as_batch(payload)  # add to SQLite cache

	sync_utils.current_filters = []  # clear filters as prep for adding mod_date

	await update_table()  # put latest data into existing tabulator table

	# show the imported data via date filter
	# print('file_data.py -- add_new_data', 'table_utils.mod_date "%s", timestamp "%s"' % (table_utils.mod_date, timestamp))
	sync_utils.update_main_tabulator_filter(table_utils.mod_date, timestamp, 'like')


async def cell_edited(old_value, new_value, row_id, field):
	# TBD: move to a shared file then extend for SQLite data

	# SEE ALSO: save_to_edits() in row_details.py

	deeply_utils.pp('cell_edited', old_value, new_value, row_id, field)

	# write to edits table first: better to have a record with no edit vs. an edit with no record
	# future: could eventually attempt a transaction and rollback (though DBs are different so a bit of work)
	timestamp = deeply_utils.now_string()

	if sync_utils.edits_db:
		edits_data = [
			str(int(row_id)),
			field,
			old_value,
			new_value,
			timestamp,
			sync_utils.edits_db.path_relative,
		]
		status = await sync_utils.edits_db.insert_data(edits_data)
		deeply_utils.pp('ADDED TO EDITS TABLE:', status)  # TBD: check for errors!
	else:
		# TBD: if is_paid_or_trial then alert?
		print('ERROR: no edits table')

	if sync_utils.cache_db:
		# refactor TBD: this seems like a hack
		columns_to_select = [x for x in sync_utils.columns if x not in table_utils.built_in_fields]

		res = await sync_utils.cache_db.update_data(field, new_value, int(row_id), timestamp)
		status = await update_table(columns_to_select)
		deeply_utils.pp('MAIN TABLE CHANGED:', status)  # TBD: check for errors!
	else:
		# TBD: if is_paid_or_trial then alert?
		print('ERROR: data not cached')

# -----
# was in main_table_helper.py

async def get_rows():
	# was handle_checksum_and_edits -- and previously set a global instead of returning rows
	# most of the work is done here

	# globals not modified here: file_name, FILE_PATH

	# print('handle_checksum_and_edits row_dicts[0]', len(row_dicts[0]), row_dicts[0])

	deeply_utils.pp('get_rows STARTED')

	have_valid_cache = await db_utils.exists_with_data(file_name)
	deeply_utils.pp('file_data.py -- have_valid_cache', have_valid_cache)
	# if False then for now ignore 'if edited_since_last_opened' case since that complicates the UI

	# Scott TBD July: move the following out of get_rows -- though not sure how that should affect main()
	edited_since_last_opened = await check_edit_since_last_opened()
	file_changed = bool(previous_checksum != current_checksum)  # it's already a boolean but added for clarity

	if edited_since_last_opened and have_valid_cache:
		deeply_utils.pp('get_rows edited_since_last_opened')
		last_ignored_changes_date = await sync_utils.actions_db.get_last_ignored_edits_date(FILE_PATH)

		if file_changed:
			status_prompt = 'The disk file changed after your edits (shown below).'
			suffix1 = ' (ignoring changes to disk file)'
			old_or_new = 'New'
		else:
			status_prompt = 'Your edits (shown below) are not in the original file.'
			suffix1 = ''
			old_or_new = 'Old'
		document['edits-preview-text'].textContent = status_prompt + ' Which would you like?'

		# buttons
		document['resume'].textContent = 'Continue With My Edits' + suffix1
		document['start-over'].textContent = 'Read %s File From Disk (ignoring my edits here)' % old_or_new

		# button handlers
		document['resume'].bind('click', lambda e, button='resume': aio.run(button_handler(e, button)))
		document['start-over'].bind('click', lambda e, button='start-over': aio.run(button_handler(e, button)))

		document['edits-preview-container'].removeAttribute('hidden')
		await get_preview_table_data(last_ignored_changes_date)  # load 'edits' data for this file
		await create_preview_table()  # show to user and wait for them to choose one of the above button options

		deeply_utils.pp('get_rows found edited_since_last_opened ==> waiting for user input')
		return []  # don't yet know where to get rows

	elif file_changed or not have_valid_cache:
		deeply_utils.pp('get_rows file_changed?', file_changed, 'and/or not have_valid_cache?', have_valid_cache)
		# must re-load the file
		rows = await process_files.get_content()  # performance TBD: can we LIMIT to 100 for initial display then page additional records for search/sort?
		deeply_utils.pp('=============== DONE getting content; found len(rows)', len(rows))
		await create_cache_db(rows)  # performance TBD: batch add in background
		# Scott TBD July: update history DB table with checksum & timestamp

	else:
		deeply_utils.pp('get_rows no change')
		# use the cache
		rows = await read_cache_db()
		# Scott TBD July: update history DB table with timestamp

	# add icon_link_out
	# performance TBD: likely slow for large dataset; could it be added on-the-fly for rows shown in the UI?
	deeply_utils.pp('add icon_link_out')
	for i in range(len(rows)):
		row_dict = OrderedDict(dict(rows[i]))  # convert from JSObject; move_to_end requires OrderedDict

		row_dict[table_utils.details_icon_field] = app_data.icon_link_out
		row_dict.move_to_end(table_utils.details_icon_field, last=False)  # OrderedDict move_to_end with last=False means move_to_START

		row = __BRYTHON__.pyobj2jsobj(dict(row_dict))  # Convert back to JSObject
		rows[i] = row

	return rows


async def check_edit_since_last_opened():
	last_opened = window.__PROPS__['last_opened']
	await set_edits_actions_dbs()

	if sync_utils.edits_db:
		edited = await sync_utils.edits_db.check_edit_since_last_opened(last_opened)
		return edited
	else:
		# TBD: is this an error? it's only checking the vaiable not if there are edits
		return False


async def button_handler(e, button):
	# with overlay displayed: user chooses to 'resume' with the cached DB or 'start over' (re-load the text file)

	# globals not modified here: preview_tabulator
	e.stopImmediatePropagation()

	# close review window
	preview_tabulator.destroy()
	document['edits-preview-container'].remove()

	if button == 'resume':
		# i.e. 'Continue With Edited Data'
		# therefore no need to load original csv/text etc. file

		return await read_cache_db()

	else:
		# start-over i.e. throw away previous changes to re-load the specified csv/txt file
		# import: 'Read Original File'

		use_cached_db = False  # will move this to backup folder
		changes_ignored_timestamp = deeply_utils.now_string()

		# 1. update the Actions table
		if not sync_utils.actions_db:
			await set_edits_actions_dbs()

		if last_ignored_changes_date:
			# TBD: should probably store every save not just the most recent
			# TBD: this isn't actually 'date_saved'
			res = await sync_utils.actions_db.update_action(['date_saved'], [changes_ignored_timestamp], {'details': FILE_PATH})
			# print('update_action:', res)
		else:
			res = await sync_utils.actions_db.add_action('ignored edits', FILE_PATH, changes_ignored_timestamp, 1)
			# print('add_action:', res)
		last_ignored_changes_date = changes_ignored_timestamp

		# 2. rename/move the edited sqlite cache (that user has chosen to skip) so user can review later if useful
		await save_utils.cache_to_backup(file_name, sync_utils.edits_db)  # edits_db to get date of last edit (with fallback to the SQLite file)

		# 3. read data from file
		rows = await process_files.get_content()
		await create_cache_db(rows)  # performance TBD: batch add in background
		return rows


async def get_edits_db():
	# globals not modified here: app_config
	edits_db = EditsSQLite(window.__PROPS__['path_relative'], app_config=app_config)
	await edits_db.load_db()  # refactor TBD: why not done on init?
	return edits_db


async def get_actions_db():  # TBD merge with get_edits_db
	# globals not modified here: app_config
	actions_db = ActionsSQLite(app_config=app_config)
	await actions_db.load_db()  # refactor TBD: why not done on init?
	return actions_db


async def set_edits_actions_dbs():
	# refactor TBD: there must be a better approach!
	sync_utils.edits_db = await get_edits_db()
	sync_utils.actions_db = await get_actions_db()


#..........................edits preview.......................................
async def create_preview_table():
	global preview_tabulator, preview_data
	# print('creating preview table')

	preview_tabulator = window.Tabulator.new('#edits-preview-table', {
		'height': 'auto',
		'pagination': True,
			'paginationSize': table_utils.pagination_default,
			'paginationSizeSelector': table_utils.pagination_options,
			'paginationButtonCount': table_utils.pagination_buttons,
			'data': preview_data,
			'autoColumns': True,
			'movableColumns': True,
			'editorParams': {'elementAttributes': {'spellcheck': False}},
	})


async def get_preview_table_data(last_ignored_changes_date=None):
	global preview_data
	edits_db = EditsSQLite(window.__PROPS__['path_relative'], app_config=app_config)
	await edits_db.load_db()
	preview_data = await edits_db.get_preview_data(last_ignored_changes_date)


sync_utils.setup_window_event_handlers(current_window, create_table, get_table)  # window object + 2 callbacks

page_runner.register_for_js()  # hardcoded for 'main'
