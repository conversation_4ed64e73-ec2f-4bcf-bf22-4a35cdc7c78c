"""
db_tables.py - UI for SQLite table of tables

Advanced feature: for each Foreign Key, attempts to add the relevant lookup data

TBD: why 'setup_' ?

refactor TBD: update to share code across edits.py, history.py, file_data.py, db_data.py, db_tables.py

... though this one may have less in common since it's not rows/cols of regular tabular data
... extra overlap with history.py since both have the pair of 'open' / 'bring to front' icons
"""

from browser import aio, document, window
from collections import OrderedDict
import json

# local to load first
import page_runner
import tauri

# local imports
from file_sqlite import Database
import db_utils  # for foreign key info
import deeply_utils
# import open_file  # for import-btn ==> not in edits, history or db_tables
import sync_utils
import table_utils

app_config = None  # must be loaded async
current_window = tauri.window.getCurrentWindow()  # TBD: or None then fetch later

# -----

db_path = ''
NAME = ''

try:
	PROPS = dict(window['__PROPS__'])
	db_path = PROPS['path']
	NAME = PROPS['name']

except Exception as e:
	print('ERROR in db_tables.py: could not get __PROPS__', e)

# -----
table_type = 'db_tables'  # metadata about each table, not actual row/column data
table = ''  # TBD: vs. sync_utils.table


platform = {  # Scott TBD: obsolete? or refactor
	'using_windows': '',
	'using_other': '',
}

# -----
window_label = None  # edits uses sync_utils; history, file_data & db_tables use globals
window_title = None

# -----
def get_table():
	return sync_utils.table  # TBD: table vs. sync_utils.table


async def reload_rows(tabulator, db):
	rows = await get_rows(db)
	tabulator.replaceData(rows)


async def on_file_tables_update(tabulator, db):
	# FYI: other code calls reload directly without a pause; probably better to be consistent

	# pause to allow table window to fully close before updating window restore icons
	await aio.sleep(0.4)  # 0.4 seconds
	await reload_rows(tabulator, db)


# no link_out detail window for this db_tables

# edits & history & db_tables: no cell editor


# -----
async def main():
	global current_window

	global app_config
	app_config = await window.get_app_config()
	sync_utils.app_config = app_config  # must be async so cannot be set on import
	table_utils.app_config = app_config  # must be async so cannot be set on import

	deeply_utils.pp('db_tables.py - app_config', app_config)

	await table_utils.add_flags_to_body()

	aio.run(sync_utils.run_check())  # check_column_width_queue

	# edits & history & db_tables ==> no #import-btn

	# documentation TBD: which windows need this and why?
	await table_utils.set_title()
	tauri.event.listen(f'title_update', lambda _: aio.run(table_utils.set_title()))

	document.querySelector('.spinner').classList.add('active')  # spinner ON

	# ---------------------------------------------
	# refactor TBD: move this to a local get_tables() like get_history()
	# deeply_utils.pp('db_tables.py - db_path', db_path)
	# db = await Database.load(db_path)
	db = db_utils.SimpleDB(db_path)
	rows = await get_rows(db)
	# no ForeignKey info for edits table
	# ^^^^^^^^^^ end future get_tables() ^^^^^^^^^^

	sync_utils.table_data = rows

	create_table(sync_utils.table_data)  # lots of action here

	document.querySelector('.spinner').classList.remove('active')  # spinner OFF

	window.tabulator = sync_utils.table

	# current_window is global

	# documentation TBD: compare this 'listen' across similar code
	# - edits: table_{table_name}_update
	# - history: table_history_update -- perhaps should be table_{table_name}_update
	# - db_tables: file_tables_update -- calls a thin wrapper to WAIT before reload
	tauri.event.listen(f'file_tables_update', lambda _: aio.run(on_file_tables_update(sync_utils.table, db)))  # SEE ALSO: table_history_update

	current_window.listen('add_new', lambda payload: deb(payload))  # documentation TBD

	# refactor TBD: review/compare/document different close-requested behavior
	current_window.listen('tauri://close-requested', lambda _: aio.run(on_window_close()))

	# edits & history & db_tables: no import_data

	# refactor TBD: window_title vs. sync_utils.window_title
	window_title = await current_window.title()
	window_label = current_window.label

	document['show-hidden-checkbox'].bind('change', lambda e: sync_utils.update_row_visibility(e, sync_utils.table, table_type))
	document['edit-layout'].bind('click', lambda e: aio.run(sync_utils.open_layout_editor(e)))
	document['edit-sort'].bind('click', lambda e: aio.run(sync_utils.open_sort_editor(e)))
	document['edit-search'].bind('click', lambda e: aio.run(sync_utils.open_search_editor(e)))
	document['export-btn'].bind('click', lambda e: aio.run(sync_utils.open_export_editor(e)))
	document['export-btn'].removeAttribute('disabled')

	document['select-all-btn'].bind('click', lambda event: sync_utils.select_all_checkboxes())
	document['select-none-btn'].bind('click', lambda event: sync_utils.deselect_all_checkboxes())
	document['hide-btn'].bind('click', lambda event: sync_utils.main_tabulator_row_toggle_visibility(event, sync_utils.table))

	# edits, history & db_tables:
	# - no 'Define' column editor
	# - no 'insert_new_row'

	# history.py adds table_history_update listener here

# ^^^^^ END: main()


async def on_window_close():
	current_window = tauri.window.getCurrentWindow()
	await tauri.event.emit('table_history_update')  # only file.py (file_data.py) & db_tables.py?
	current_window.destroy()


# -----
# edits, history & db_tables:
# - no 'Define' column editor
# - no 'insert_new_row'

#........................end.............................

# Scott TBD July: probably get rid of these too
def update_main_table_when_recreate(evt):
	sync_utils.on_table_data_loaded(evt, sync_utils.table)


def save_loaded_data(evt):
	sync_utils.on_table_data_loaded_init(evt, sync_utils.table)


def execute_save_loaded_data(evt):
	window.setTimeout(lambda: save_loaded_data(evt), 50)


# create table
def create_table(tabulator_data, is_initial=True):
	# assembles params then calls window.Tabulator.new()

	if not tabulator_data:
		print('ERROR reading SQLite file')
		return

	visibility_dict = {}  # true/false lookup table by column name; default TRUE

	if sync_utils.table:
		# this should correspond to is_initial FALSE but perhaps there are exceptions
		# some changes require rebuilding the table; this preserves then later restores checked/hidden
		sync_utils.save_row_states(sync_utils.table)  # preserve checked or hidden rows; not in 1.0 -- refactor TBD: redundant with sync_utils?
		sync_utils.table.destroy()
		sync_utils.table = None

	if is_initial:
		# no open_details '(details icon)' for list of DB tables

		# comparing similar code elsewhere: tabulator_data param vs. tabulator_data
		sync_utils.columns = [key for key in dict(tabulator_data[0]).keys() if key not in table_utils.built_in_fields]

		# no details icon for tables
		sync_utils.columns = table_utils.add_internal_column_names(sync_utils.columns, with_window_icons=True)

		for col in sync_utils.columns:
			sync_utils.search_options_dropdown[col] = 'like'

	else:
		sync_utils.columns = []
		for item in sync_utils.header_data:
			sync_utils.columns.append(item['name'])
			visibility_dict[item['name']] = item['visible']

	column_dicts = table_utils.make_columns(sync_utils, visibility_dict)

	table_utils.add_column_details(column_dicts)  # edits, history & db_tables: no editor_callback

	# include_details_icon=True currently only for user data (csv/txt or DB); should add READ ONLY for history & edits
	final_data = table_utils.get_extra_data(tabulator_data, False, hidden_rows=sync_utils.hidden_rows, checked_rows=sync_utils.checked_rows, include_details_icon=False)

	sync_utils.table = window.Tabulator.new(
		'#table',
		{
			'height': 'auto',
			# 'rowHeight': 24, # rowHeight prevents text from wrapping onto multiple rows ==> don't use!
			'pagination': True,
			'paginationSize': table_utils.pagination_default,
			'paginationSizeSelector': table_utils.pagination_options,
			'paginationButtonCount': table_utils.pagination_buttons,
			'data': final_data,
			'columns': column_dicts,
			'movableColumns': True,
			'editorParams': {'elementAttributes': {'spellcheck': False}},
			'debugInvalidOptions': False,
			'debugInvalidComponentFuncs': False,
			'debugInitialization': False,
			# placeholder only needed for history & edits
		},
	)

	sync_utils.table.on('tableBuilt', lambda: (
		# print('db_tables -- create_table: table built'),
		table_utils.update_shown_entries(None, sync_utils.table, table_type),
		sync_utils.table.on('columnMoved', lambda column, columns: sync_utils.on_table_column_moved(column,columns, sync_utils.table)),
		sync_utils.table.on('columnResized', lambda column: sync_utils.column_width_updated(column)),
		sync_utils.table.on('dataSorted', lambda sorters, rows: sync_utils.on_table_data_sorted(sorters, rows, sync_utils.table)),
		sync_utils.table.on('headerClick', lambda event, column: sync_utils.on_header_click(event, column, sync_utils.table)),
		sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table)),

		sync_utils.table.on('cellClick', lambda e, cell: aio.run(on_cell_click(e, cell))),  # varies across similar code
		))

	# documentation TBD: why here AND after tableBuilt
	sync_utils.table.on('dataFiltered', lambda filters, rows: sync_utils.on_table_data_filtered(filters, rows, sync_utils.table))

	if is_initial:
		sync_utils.table.on('dataLoaded', execute_save_loaded_data)
	else:
		sync_utils.table.on('dataLoaded', update_main_table_when_recreate)

		# restore_row_states is for checked or hidden rows; not in 1.0
		window.setTimeout(lambda: sync_utils.restore_row_states(sync_utils.table, sync_utils.table_data), 50)

	window.tabulator = sync_utils.table


#...........................................end...........................................

# window icons: history & db_tables

async def on_cell_click(event, cell):
	field = cell.getField()
	if field not in table_utils.window_icon_fields:
		# history & db_tables only support clicks in these 2 cells of any row
		return

	# for now: treats click on the entire cell as if a click on the icon; may want to change that

	row_dict = dict(cell.getRow().getData())
	row_webview_label = row_dict.get('webview_label')

	if field == table_utils.restore_window_icon_field:
		windows = window.__TAURI__.webviewWindow.getAll()
		# TBD: find better way to get the earliest
		for webviewWindow in windows:
			if dict(webviewWindow)['label'] == row_webview_label:
				webviewWindow.unminimize()
				webviewWindow.setFocus()
				break

	elif field == table_utils.new_window_icon_field:
		# New table
		label = tauri.window.getCurrentWindow().label
		args = {
			'table': row_dict['table'],
			'path': db_path,
			'parentLabel': label,
			}
		await tauri.invoke('open_table', args)

# -----
# specific to db_tables ==> should probably be moved elsewhere

async def get_rows(db):
	query = "SELECT name FROM sqlite_master WHERE type='table'"
	rows = await db.get_rows(query)

	# history & db_tables need active_items for 'restore' icon (bring to front)
	try:
		active_items = await tauri.invoke('get_active_tables')
		active_items = dict(active_items)  # convert from JSObject
	except Exception as e:
		print('db_tables.py - get_rows: no tables???', e)
		active_items = {}

	deeply_utils.pp('active_items', active_items)

	for i in range(len(rows)):
		row_dict = OrderedDict(dict(rows[i]))  # convert from JSObject; other code requires OrderedDict

		# -----
		# only in this db_tables.py
		row_dict['table'] = row_dict.pop('name') # rename since column titles come from here
		table_name = row_dict['table']

		row_count_data = await db.get_rows(f"SELECT count(*) as count from '{table_name}'", [])
		row_dict['rows'] = row_count_data[0]['count']

		# for columns & primary_keys
		fields_info = await db.get_rows(f"PRAGMA table_info('{table_name}')", [])

		row_dict['columns'] = len(fields_info)
		row_dict['column names'] = ', '.join(field['name'] for field in fields_info)  # are these guaranteed to be sorted?

		primary_keys = [i for i in fields_info if i.pk != 0]
		primary_keys = sorted(primary_keys, key=lambda i: i['name'])
		row_dict['primary keys'] = ', '.join(i['name'] for i in primary_keys)

		foreign_keys_info = await db.get_rows(f"PRAGMA foreign_key_list('{table_name}')", [])
		# print('foreign keys info => ', foreign_keys_info)
		foreign_keys = []
		for j in list(foreign_keys_info):
			friendly_columns = await db_utils.get_friendly_fk_columns(db, j['table'])
			foreign_keys.append(f"{j['from']} - {j['table']}:{j['to']}</br>")
		row_dict['foreign keys'] = ''.join(sorted(foreign_keys))
		# ^^^^^ END: only in this db_tables.py

		# history.py & db_tables.py
		table_utils.add_window_restore_icons(row_dict, active_items.get(table_name))

		row = __BRYTHON__.pyobj2jsobj(dict(row_dict))  # Convert back to JSObject
		rows[i] = row

	return rows  # i.e. row_objects

sync_utils.setup_window_event_handlers(current_window, create_table, get_table)  # window object + 2 callbacks

page_runner.register_for_js()  # hardcoded for 'main'
