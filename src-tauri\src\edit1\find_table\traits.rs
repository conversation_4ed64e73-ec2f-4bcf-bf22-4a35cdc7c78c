use regex::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::collections::{HashMap, HashSet};
use std::ffi::OsStr;
use std::fs::{self, File};
use std::io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Seek, <PERSON>k<PERSON><PERSON>, Write};
use std::path::{Path, PathBuf};
use walkdir::WalkDir;

#[cfg(target_os = "macos")]
use xattr;

use crate::edit1::get_file_folder;

use super::dto::{
    Data, Filters, Finder, MatchIgnoreData, Position, ReplaceInstance, ReplaceOptions, Row, M,
};
use super::utils::{
    is_hidden, is_whitelisted, load_gitignore, matches_any_rule, should_exclude_path,
};

pub fn find_occurrences_in_files(file_paths: &[String], finder: &Finder) -> Vec<Data> {
    file_paths
        .iter()
        .filter_map(
            |file_path| match find_occurrences_in_file(file_path, finder) {
                Ok(rows) if !rows.is_empty() => {
                    let path = Path::new(file_path);
                    let file_name = path
                        .file_name()
                        .unwrap_or_default()
                        .to_string_lossy()
                        .into_owned();

                    Some(Data {
                        title: file_name.clone(),
                        file_name,
                        file_folder: path.to_string_lossy().into_owned(),
                        parent_folder: get_file_folder(path.to_string_lossy().into_owned()),
                        window: String::new(),
                        opendocs: false,
                        finder: finder.clone(),
                        selection: M { from: 0, to: 0 },
                        rows,
                    })
                }
                Ok(_) => None,
                Err(e) => {
                    println!("Error processing file {}: {}", file_path, e);
                    None
                }
            },
        )
        .collect()
}

pub fn find_occurrences_in_file(
    file_path: &str,
    finder: &Finder,
) -> Result<Vec<Row>, std::io::Error> {
    let file = File::open(file_path)?;
    let mut reader = BufReader::new(file);
    let mut contents = String::new();
    reader.read_to_string(&mut contents)?;
    //println!("##########################");
    //println!("Path => {:?}", file_path);
    let mut rows = Vec::new();
    //println!("Finder {:?}", finder);
    let regex = if finder.grep {
        RegexBuilder::new(&finder.find)
            .case_insensitive(!finder.case_sensitive)
            .build()
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidInput, e))?
    } else {
        let mut pattern = if finder.entire_word {
            format!(r"\b{}\b", regex::escape(&finder.find))
        } else {
            regex::escape(&finder.find)
        };
        if !finder.case_sensitive {
            pattern = format!("(?i){}", pattern);
        }
        Regex::new(&pattern)
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidInput, e))?
    };

    //println!("Regex {:?}", regex);

    for captures in regex.captures_iter(&contents) {
        if let Some(m) = captures.get(0) {
            let start = m.start();
            let end = m.end();
            let matched_text = m.as_str();

            let line_start = contents[..start].rfind('\n').map_or(0, |pos| pos + 1);
            let line_end = contents[end..]
                .find('\n')
                .map_or(contents.len(), |pos| end + pos);
            let mut content = contents[line_start..line_end].to_string();
            content = content.replace('\n', "¶");
            let line_number = contents[..start].matches('\n').count() + 1;

            rows.push(Row {
                m: M {
                    from: start,
                    to: end,
                },
                number: line_number,
                match_from: start - line_start,
                match_to: end - line_start,
                content,
                found: Some(matched_text.to_string()),
            });
        }
    }

    Ok(rows)
}

pub fn search_in_folder(folder_path: &Path, finder: &Finder) -> Vec<Data> {
    WalkDir::new(folder_path)
        .into_iter()
        .filter_map(|entry| entry.ok())
        .filter(|entry| entry.file_type().is_file())
        .filter_map(|entry| {
            let file_path = entry.path();
            let path_str = file_path.to_str()?;
            match find_occurrences_in_file(path_str, finder) {
                Ok(rows) if !rows.is_empty() => {
                    let file_name = file_path
                        .file_name()
                        .unwrap_or_default()
                        .to_string_lossy()
                        .into_owned();

                    Some(Data {
                        title: file_name.clone(),
                        file_name,
                        file_folder: file_path.to_string_lossy().into_owned(),
                        parent_folder: get_file_folder(file_path.to_string_lossy().into_owned()),
                        window: String::new(),
                        opendocs: false,
                        finder: finder.clone(),
                        selection: M { from: 0, to: 0 },
                        rows,
                    })
                }
                Ok(_) => None,
                Err(e) => {
                    println!("Error processing file {:?}: {}", file_path, e);
                    None
                }
            }
        })
        .collect()
}

pub fn replace_in_file(
    instance: ReplaceInstance,
    file_path: String,
    options: ReplaceOptions,
) -> Result<bool, std::io::Error> {
    // Open the file in read-write mode
    let mut file = fs::OpenOptions::new()
        .read(true)
        .write(true)
        .open(&file_path)?;

    // Get the file size
    let _file_size = file.metadata()?.len();

    // Read the content of the specified range
    let mut buffer = vec![0; (instance.pos.to - instance.pos.from) as usize];
    file.seek(SeekFrom::Start(instance.pos.from))?;
    file.read_exact(&mut buffer)?;

    // Convert the buffer to a string
    let content = String::from_utf8_lossy(&buffer);

    // Check if the content matches the 'find' string based on the options
    let is_match = if options.regex {
        let re_flags = if options.case_sensitive { "" } else { "(?i)" };
        let re_pattern = format!(
            "{}{}",
            re_flags,
            if options.entire_word {
                format!(r"\b{}\b", instance.find)
            } else {
                instance.find
            }
        );
        Regex::new(&re_pattern).unwrap().is_match(&content)
    } else if options.entire_word {
        let word_boundary = r"\b";
        let pattern = format!(
            "{}{}{}",
            word_boundary,
            regex::escape(&instance.find),
            word_boundary
        );
        let re_flags = if options.case_sensitive { "" } else { "(?i)" };
        let re_pattern = format!("{}{}", re_flags, pattern);
        Regex::new(&re_pattern).unwrap().is_match(&content)
    } else if !options.case_sensitive {
        content.to_lowercase() == instance.find.to_lowercase()
    } else {
        content == instance.find
    };

    if is_match {
        // Read the rest of the file after the replacement point
        let mut rest_of_file = Vec::new();
        file.seek(SeekFrom::Start(instance.pos.to))?;
        file.read_to_end(&mut rest_of_file)?;

        // Write the replacement
        file.seek(SeekFrom::Start(instance.pos.from))?;
        file.write_all(instance.replace.as_bytes())?;

        // Write the rest of the file
        file.write_all(&rest_of_file)?;

        // Truncate the file to remove any excess content
        let new_file_size = file.stream_position()?;
        file.set_len(new_file_size)?;

        Ok(true)
    } else {
        Ok(false)
    }
}

pub fn replace_set_in_file(
    instances: Vec<ReplaceInstance>,
    file_path: String,
) -> Result<Vec<Position>, std::io::Error> {
    let mut file = fs::OpenOptions::new()
        .read(true)
        .write(true)
        .open(&file_path)?;

    let mut successful_replacements = Vec::new();
    let mut file_content = String::new();
    file.read_to_string(&mut file_content)?;

    //println!("replace_set_in_file: read {:?}", file_path);

    let mut sorted_instances = instances.to_vec();
    sorted_instances.sort_by(|a, b| b.pos.from.cmp(&a.pos.from));

    for instance in sorted_instances {
        let start = instance.pos.from as usize;
        let end = instance.pos.to as usize;

        if start < file_content.len()
            && end <= file_content.len()
            && file_content[start..end].to_lowercase() == instance.find.to_lowercase()
        {
            file_content.replace_range(start..end, &instance.replace);
            successful_replacements.push(Position {
                from: instance.pos.from,
                to: instance.pos.to,
            });
            //println!("Replaced {}:{}", instance.pos.from, instance.pos.to);
        } else {
            // println!(
            // 	"No match or out of bounds: start={}, end={}, file_content_len={}, expected={}, actual={}",
            // 	start,
            // 	end,
            // 	file_content.len(),
            // 	instance.find.to_lowercase(),
            // 	&file_content[start..end].to_lowercase()
            // );
        }
    }

    // Write the modified content back to the file
    file.seek(SeekFrom::Start(0))?;
    file.write_all(file_content.as_bytes())?;
    file.set_len(file_content.len() as u64)?;

    Ok(successful_replacements)
}

fn is_alias_or_symlink(path: &Path) -> bool {
    // Check symlink on all platforms
    if let Ok(metadata) = std::fs::symlink_metadata(path) {
        if metadata.file_type().is_symlink() {
            return true;
        }
    }

    #[cfg(target_os = "macos")]
    {
        // Alias has FinderInfo attribute
        if xattr::get(path, "com.apple.FinderInfo")
            .map(|v| v.is_some())
            .unwrap_or(false)
        {
            return true;
        }
    }

    #[cfg(windows)]
    {
        // Check if the file is a Windows shortcut (.lnk file)
        if let Some(extension) = path.extension() {
            if extension.eq_ignore_ascii_case("lnk") {
                return true;
            }
        }
    }

    // For other platforms or if no conditions matched, return false
    false
}

pub fn get_filtered_files(folder_path: &str, filters: &Filters) -> Vec<String> {
    let mut result = Vec::new();
    let root = Path::new(folder_path);
    // let mut gitignore_rules = Vec::new();
    let mut excluded_dirs = HashSet::new();

    let mut rules: Vec<String> = Vec::new();
    //println!("Filters {:?}", filters);
    if filters.use_gitignore {
        rules = load_gitignore(root);
    }

    if !filters.ignore_rules.is_empty() {
        rules.append(&mut filters.ignore_rules.clone());
    }
    for entry in WalkDir::new(folder_path).into_iter().filter_map(|e| e.ok()) {
        let path = entry.path();
        let relative_path = path.strip_prefix(root).unwrap_or(path);
        let path_str = relative_path.to_str().unwrap_or("");

        if path.file_name().map_or(false, |name| name == ".DS_Store") {
            continue;
        } else if path
            .extension()
            .and_then(|ext| ext.to_str())
            .map_or(false, |ext| ["zip", "rar", "gzip", "gz"].contains(&ext))
        {
            continue;
        } else if is_alias_or_symlink(path) {
            continue;
        }

        let is_ignored = should_exclude_path(path, path_str, filters, &rules, &excluded_dirs);
        let isnt_matched =
            !filters.match_rules.is_empty() && !matches_any_rule(path_str, &filters.match_rules);
        //println!("## {:?} ==> |{:?}| |{:?}| => {:?}", path_str, is_ignored, isnt_matched, is_ignored || isnt_matched);
        if is_ignored || isnt_matched {
            if path.is_dir() && path.to_string_lossy().into_owned() != folder_path {
                excluded_dirs.insert(path.to_path_buf());
            }

            continue;
        }

        if path.is_file() {
            if let Some(path_string) = path.to_str() {
                result.push(path_string.to_string());
            }
        }
    }
    //println!("############");
    //println!("{:?}", result);
    //println!("############");
    result
}

pub fn get_matchignore_folder(folder_path: &str, filters: &Filters) -> Vec<MatchIgnoreData> {
    let mut result: Vec<MatchIgnoreData> = Vec::new();
    let root = Path::new(folder_path);
    // let mut gitignore_rules = Vec::new();
    let mut excluded_dirs = HashSet::new();

    let mut rules: Vec<String> = Vec::new();

    if filters.use_gitignore {
        rules = load_gitignore(root);
    }

    // if !filters.ignore_rules.is_empty() {
    // 	rules.append(&mut filters.ignore_rules.clone());
    // }

    //println!("Filters {:?}", filters);

    for entry in WalkDir::new(folder_path).into_iter().filter_map(|e| e.ok()) {
        let path = entry.path();
        let relative_path = path.strip_prefix(root).unwrap_or(path);
        let path_str = relative_path.to_str().unwrap_or("");

        if path.file_name().map_or(false, |name| name == ".DS_Store") {
            continue;
        } else if path
            .extension()
            .and_then(|ext| ext.to_str())
            .map_or(false, |ext| ["zip", "rar", "gzip", "gz"].contains(&ext))
        {
            continue;
        } else if is_alias_or_symlink(path) {
            continue;
        }
        //println!("################");
        ////println!("Path {:?}", path);
        let mut review: MatchIgnoreData = MatchIgnoreData {
            search: "Yes".into(),
            matched: "-".into(),
            ignored: "-".into(),
            extension: None,
            name: "".into(),
            parent_folder: "".into(),
            path: path.to_string_lossy().into_owned(),
            flags: Vec::new(),
        };
        if should_exclude_path(path, path_str, filters, &rules, &excluded_dirs) {
            if excluded_dirs.iter().any(|dir| path.starts_with(dir)) {
                //println!("PATH REMOVED");
                continue;
            }
            // //println!("PATH IGNORED");
            // review.ignored = "i".into();
            excluded_dirs.insert(path.to_path_buf());
        }
        // if !is_whitelisted(path_str, &rules) && excluded_dirs.iter().any(|dir: &PathBuf| path.starts_with(dir)) {
        // 	review.search = "-".into();
        // 	review.flags.push("parent folder ignored".into());
        // 	excluded_dirs.insert(path.to_path_buf());
        // } else
        if is_whitelisted(path_str, &rules) {
            review.search = "Yes".into();
            review.flags.push("whitelisted".into());
            if excluded_dirs
                .iter()
                .any(|dir: &PathBuf| path.starts_with(dir))
            {
                review.flags.push("parent folder ignored".into());
            }
        }

        if filters.omit_invisible_files && path.is_file() && is_hidden(path) {
            review.search = "-".into();
            review.flags.push("hidden file".into());
        }

        if filters.omit_invisible_folders && path.is_dir() && is_hidden(path) {
            review.search = "-".into();
            review.flags.push("hidden folder".into());
        }

        if !rules.is_empty()
            && matches_any_rule(path_str, &rules)
            && !is_whitelisted(path_str, &rules)
        {
            review.search = "-".into();
            review.flags.push("gitignore".into());
        }

        if !filters.ignore_rules.is_empty() && matches_any_rule(path_str, &filters.ignore_rules) {
            review.search = "-".into();
            review.flags.push("ignore filter rules".into());
        }
        if !filters.match_rules.is_empty() && !matches_any_rule(path_str, &filters.match_rules) {
            //println!("PATH MATCHED");
            review.matched = "m".into();
            review.search = "-".into();
            review.flags.push("not matched".into());
        } else if !filters.match_rules.is_empty() {
            review.flags.push("matched".into());
        }

        if path.is_file() {
            let extension = path
                .extension()
                .unwrap_or(OsStr::new("unknown"))
                .to_string_lossy()
                .into_owned();
            review.extension = Some(extension);
            review.name = path.file_name().unwrap().to_string_lossy().into_owned();
            // review.folder = path.parent().unwrap().to_string_lossy().into_owned();
            review.parent_folder = get_file_folder(path.to_string_lossy().into_owned());
        } else if path.is_dir() {
            review.name = format!(
                "/{}/",
                path.file_name().unwrap().to_string_lossy().into_owned()
            );
            // review.folder = path.parent().unwrap().to_string_lossy().into_owned();
            review.parent_folder = get_file_folder(path.to_string_lossy().into_owned());
        }
        result.push(review);
        //println!("---------------------");
    }
    result
}

pub fn get_matchignore_file(file_path: &str, filters: &Filters) -> Option<MatchIgnoreData> {
    let path = Path::new(file_path);
    // let mut gitignore_rules = Vec::new();
    let mut excluded_dirs = HashSet::new();

    let rules: Vec<String> = Vec::new();

    // if !filters.ignore_rules.is_empty() {
    // 	rules.append(&mut filters.ignore_rules.clone());
    // }

    //println!("Filters {:?}", filters);

    // let path = root.path();
    // let relative_path = path.strip_prefix(root).unwrap_or(path);
    // let path_str = relative_path.to_str().unwrap_or("");

    if path.file_name().map_or(false, |name| name == ".DS_Store") {
        return None;
    } else if path
        .extension()
        .and_then(|ext| ext.to_str())
        .map_or(false, |ext| ["zip", "rar", "gzip", "gz"].contains(&ext))
    {
        return None;
    } else if is_alias_or_symlink(path) {
        return None;
    }
    //println!("################");
    ////println!("Path {:?}", path);
    let mut review: MatchIgnoreData = MatchIgnoreData {
        search: "Yes".into(),
        matched: "-".into(),
        ignored: "-".into(),
        extension: None,
        name: "".into(),
        parent_folder: "".into(),
        path: path.to_string_lossy().into_owned(),
        flags: Vec::new(),
    };
    // if should_exclude_path(path, path_str, filters, &rules, &excluded_dirs) {
    // 	if excluded_dirs.iter().any(|dir| path.starts_with(dir)) {
    // 		//println!("PATH REMOVED");
    // 		continue;
    // 	}
    // 	//println!("PATH IGNORED");
    // 	review.ignored = "i".into();
    // 	excluded_dirs.insert(path.to_path_buf());
    // }
    if !is_whitelisted(file_path, &rules)
        && excluded_dirs
            .iter()
            .any(|dir: &PathBuf| path.starts_with(dir))
    {
        review.search = "-".into();
        review.flags.push("Parent folder ignored".into());
        excluded_dirs.insert(path.to_path_buf());
    } else if is_whitelisted(file_path, &rules) {
        review.search = "Yes".into();
        review.flags.push("Whitelisted".into());
    }

    if filters.omit_invisible_files && path.is_file() && is_hidden(path) {
        review.search = "-".into();
        review.flags.push("hidden file".into());
    }

    if filters.omit_invisible_folders && path.is_dir() && is_hidden(path) {
        review.search = "-".into();
        review.flags.push("hidden folder".into());
    }

    if !rules.is_empty()
        && matches_any_rule(file_path, &rules)
        && !is_whitelisted(file_path, &rules)
    {
        review.search = "-".into();
        review.flags.push("gitignore".into());
    }

    if !filters.ignore_rules.is_empty() && matches_any_rule(file_path, &filters.ignore_rules) {
        review.search = "-".into();
        review.flags.push("ignore filter rules".into());
    }
    if !filters.match_rules.is_empty() && !matches_any_rule(file_path, &filters.match_rules) {
        //println!("PATH MATCHED");
        review.matched = "m".into();
        review.search = "-".into();
        review.flags.push("not matched".into());
    } else if !filters.match_rules.is_empty() {
        review.flags.push("matched".into());
    }

    if path.is_file() {
        let extension = path
            .extension()
            .or(Some(OsStr::new("unknown")))
            .unwrap()
            .to_string_lossy()
            .into_owned();
        review.extension = Some(extension);
        review.name = path.file_name().unwrap().to_string_lossy().into_owned();
        // review.folder = path.parent().unwrap().to_string_lossy().into_owned();
        review.parent_folder = get_file_folder(path.to_string_lossy().into_owned());
    } else if path.is_dir() {
        review.name = format!(
            "/{}/",
            path.file_name().unwrap().to_string_lossy().into_owned()
        );
        // review.folder = path.parent().unwrap().to_string_lossy().into_owned();
        review.parent_folder = get_file_folder(path.to_string_lossy().into_owned());
    }
    //println!("---------------------");

    Some(review)
}

pub fn filter_files_by_gitignore(
    folders: Vec<String>,
    files: Vec<String>,
    filters: Filters,
) -> Vec<String> {
    let mut folder_files: HashMap<String, HashSet<String>> = HashMap::new();
    let mut ignored_files: Vec<String> = Vec::new();
    let mut excluded_dirs = HashSet::new();
    let mut sorted_folders: Vec<_> = folders.iter().cloned().collect();
    sorted_folders.sort_by(|a, b| a.len().cmp(&b.len()));

    for file in files {
        for folder in &sorted_folders {
            if file.starts_with(folder) {
                folder_files
                    .entry(folder.to_string())
                    .or_insert_with(HashSet::new)
                    .insert(file.clone());
                let path = Path::new(&file);
                for ancestor in path.ancestors() {
                    let x = ancestor.to_string_lossy().into_owned();
                    if x != *folder {
                        folder_files
                            .entry(folder.to_string())
                            .or_insert_with(HashSet::new)
                            .insert(x);
                    } else {
                        break;
                    }
                }
            }
        }
    }

    for (key, _) in folder_files.clone() {
        for folder in sorted_folders.clone() {
            if key != folder && folder.starts_with(&key) {
                folder_files
                    .entry(key.clone())
                    .or_insert_with(HashSet::new)
                    .insert(folder.clone());
            }
        }
    }

    let mut entries: Vec<(String, HashSet<String>)> = folder_files.into_iter().collect();
    entries.sort_by_key(|(key, _v)| key.len());

    for (key, paths_str) in entries {
        let root = Path::new(&key);
        let mut rules: Vec<String> = Vec::new();
        if filters.use_gitignore {
            rules = load_gitignore(root);
        }
        if !filters.ignore_rules.is_empty() {
            rules.append(&mut filters.ignore_rules.clone());
        }

        let mut vec_path_str: Vec<&String> = paths_str.iter().collect();
        vec_path_str.sort();

        for path_str in vec_path_str {
            let file_path = Path::new(&path_str);
            let relative_path = file_path.strip_prefix(root).unwrap_or(file_path);
            let rel_path_str = relative_path.to_str().unwrap_or("");

            let is_ignored =
                should_exclude_path(file_path, &rel_path_str, &filters, &rules, &excluded_dirs);
            let is_matched = !filters.match_rules.is_empty()
                && !matches_any_rule(&rel_path_str, &filters.match_rules);

            if is_ignored || is_matched {
                if file_path.is_dir() && file_path.to_string_lossy().into_owned() != key {
                    excluded_dirs.insert(file_path.to_path_buf());
                }
                ignored_files.push(path_str.clone());
            }
        }
    }
    ignored_files
}
