use std::fs;

use handlebars::Handlebars;
use serde_json::json;
use tauri::{Manager, WebviewWindowBuilder};

/// Read HTML & CSS files from the app config directory
/// Add them to the newly created window
pub fn user_customize_assets<'a, R, M>(
	app: &'a tauri::AppHandle,
	mut window: WebviewWindowBuilder<'a, R, M>,
) -> WebviewWindowBuilder<'a, R, M>
where
	R: tauri::Runtime,
	M: tauri::Manager<R>,
{
	// Determine and ensure the app config directory exists
	let config_dir = app.path().app_config_dir().unwrap();
	println!("config_dir => {:?}", config_dir);
	log::debug!("App config directory: {}", config_dir.display());

	if let Err(e) = fs::create_dir_all(&config_dir) {
		log::error!("Failed to create config dir {}: {}", config_dir.display(), e);
	}

	let mut css = String::new();
	let mut js = String::new();

	// this does a file scan each time a window is opened
	// perhaps not ideal but should only have a few files & folders
	// - cache folder
	// - window-state.json
	// - any user-added css or js

	// Safely attempt to read directory entries
	match fs::read_dir(&config_dir) {
		Ok(entries) => {
			for entry in entries.flatten() {
				log::debug!("entry: {}", entry.path().display());
				let filename = entry.file_name().to_string_lossy().to_string();

				if filename.ends_with(".css") {
					if let Ok(content) = fs::read_to_string(entry.path()) {
						css.push_str(&format!("{}\n", content));
					}
				} else if filename.ends_with(".js") {
					if let Ok(content) = fs::read_to_string(entry.path()) {
						// Use anonymous function so it won't conflict with globals
						js.push_str(&format!(r#"(async () => {{{} }})();"#, content));
					}
				}
			}
		}
		Err(err) => {
			log::warn!(
				"Reading config dir failed ({}): {}; skipping custom assets",
				config_dir.display(),
				err
			);
		}
	}

	// Inject CSS via Handlebars template if any CSS was found
	if !css.is_empty() {
		let reg = Handlebars::new();
		let template = include_str!("templates/custom_css.js");
		match reg.render_template(template, &json!({ "content": css })) {
			Ok(script) => window = window.initialization_script(&script),
			Err(err) => log::error!("Failed to render CSS injection script: {}", err),
		}
	}

	// Inject any JS snippets collected
	if !js.is_empty() {
		window = window.initialization_script(&js);
	}

	window
}
