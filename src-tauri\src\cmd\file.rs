//! file.rs - contains a mix of file utils and general code
//!
//! handles checksum, opening files & SQLite DBs, open window with tabulator table, open row details window, history (including column order?)
//! and sync windows to main table
//!
//! unknown: is all this code still in use or has some moved to Python?

use super::common::escape_window_label;
use super::window::{ create_window, NewWindowOptions };
// use crate::cmd::window::OffsetFromWindow;
use crate::global_config::AppConfig;
use crate::utils::{ eval_with_event, home_dir };
use crate::window::numbering::NumberedWindows;
use crate::{ config, date };
use eyre::{ eyre, Context, ContextCompat, Result };
use indexmap::IndexMap;
use rand::distributions::Alphanumeric;
use rand::Rng;
use serde_json::{ json, Value };
use serde::{ Deserialize, Serialize };
use sqlx::{ Column, Pool, Row, Sqlite, SqlitePool };
// use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::path::PathBuf;
use std::time::Duration;
use std::time::SystemTime;
use std::io::{ BufReader, Read };
use tauri::{ command, AppHandle, Emitter, EventTarget, Manager, State, Webview };
use tauri_plugin_sql::DbInstances;

use sha2::{ Sha512, Digest };

fn compute_sha512<P: AsRef<Path>>(path: P) -> Result<String, std::io::Error> {
	let file = fs::File::open(path)?;
	let mut reader = BufReader::new(file);
	let mut hasher = Sha512::new();
	let mut buffer = [0u8; 8192];

	loop {
		let bytes_read = reader.read(&mut buffer)?;
		if bytes_read == 0 {
			break;
		}
		hasher.update(&buffer[..bytes_read]);
	}

	let result = hasher.finalize();
	Ok(hex::encode(result))
}

async fn get_file_checksum(path: PathBuf, app_handle: AppHandle) -> Result<String> {
	// refactor TBD: get_file_checksum and get_last_opened should call a common function
	let path_id = refind::get_id(path.clone()).map_err(|e| eyre!(e))?;
	let pool: State<'_, Pool<Sqlite>> = app_handle.state();
	let mut conn = pool.acquire().await.unwrap();
	let query = "SELECT checksum FROM history WHERE path_id=? ORDER BY date_opened DESC LIMIT 1";
	let query = sqlx::query(query).bind(path_id.clone());
	let row = query.fetch_one(&mut *conn).await?;

	let checksum: String = row.try_get("checksum")?;
	// log::debug!("{:?}", checksum);
	Ok(checksum)
}

async fn get_last_opened(path: PathBuf, app_handle: AppHandle) -> Result<String> {
	// refactor TBD: get_file_checksum and get_last_opened should call a common function
	let path_id = refind::get_id(path.clone()).map_err(|e| eyre!(e))?;
	let pool: State<'_, Pool<Sqlite>> = app_handle.state();
	let mut conn = pool.acquire().await.unwrap();
	let query = "SELECT MAX(datetime(date_opened, 'localtime')) as last_opened FROM history WHERE path_id=? ORDER BY last_opened DESC LIMIT 1";
	let query = sqlx::query(query).bind(path_id.clone());
	let row = query.fetch_one(&mut *conn).await?;

	let last_opened: String = row.try_get("last_opened")?;
	// log::debug!("{:?}", last_opened);
	Ok(last_opened)
}

pub async fn insert_id() -> String {
    let s: String = rand::thread_rng()
        .sample_iter(&Alphanumeric)
        .take(25)
        .map(char::from)
        .collect();
	"insert_".to_owned() + &s
}

// pub fn with_standard_offset(parent_label: impl Into<String>) -> OffsetFromWindow {
//	 OffsetFromWindow {
//		 label: parent_label.into(),
//		 x: 30,
//		 y: 30,
//	 }
// }

#[command]
pub async fn open_file(
	app_handle: AppHandle,
	mut path: String,
	src: String,
	path_id: Option<String>,
	header_columns: Option<String>,
	parent_label: Option<String>,
	target_name: Option<String>
) -> Result<()> {
	let mut is_data_table = false;
	let insert_id_str = insert_id().await;
	if let Some(path_id) = path_id {
		if let Ok(path_buf) = refind::find_path(&path_id) {
			path = path_buf.to_str().unwrap().to_string();
		}
	}
	let path_buf = PathBuf::from(path.clone());
	let path_buf_c = path_buf.clone();
	let ext = path_buf.extension().unwrap_or_default().to_str().unwrap();  // Rust: no leading dot (unlike Python)
	let name = path_buf.file_name().unwrap().to_str().unwrap().to_string();
	let prev_checksum = get_file_checksum(path_buf_c.clone(), app_handle.clone()).await;
	let checksum = compute_sha512(path.clone()).unwrap_or_default();
	let path_relative = path.clone().replace(home_dir().to_str().unwrap(), "~");
	let last_opened = get_last_opened(path_buf.clone(), app_handle.clone()).await;
	upsert_file_in_history(&app_handle, path_buf_c, Some(date::get_date_utc()), Some(checksum.clone())).await.unwrap();

	let numbered_windows: State<'_, tokio::sync::Mutex<NumberedWindows>> = app_handle.state();
	let mut numbered_windows = numbered_windows.lock().await;
	let label = escape_window_label(&format!("file_{}_{}", &name, numbered_windows.count(name.clone()) + 1));
	let header_cols = header_columns.clone();
	let mut props = json!({"path": path, "name": name, "ext": ext});
	log::debug!("props: {:?}", props);

	let (window_url, window_props, init_script) = if config::SQLITE_EXTENSIONS.contains(&ext) {
		("/windows/db/db_tables.html".into(), Some(json!({"path": path, "name": name})), None)
	} else {
		is_data_table = true;
		let text_content = read_text_file(path_buf.clone()).await;
		let content = text_content.unwrap();

		if let Some(_header_cols) = header_cols {
			props = json!({
				"path": path,
				"name": name,
				"ext": ext,  // Rust: no leading dot (unlike Python)
				"insert_id": insert_id_str.clone(),

				"header_columns": header_columns,
				"parent_label": parent_label,
				"target_name": target_name,

				"path_relative": path_relative,
				"last_opened": last_opened.unwrap_or_default(),
				"current_checksum": checksum,
				"prev_checksum": prev_checksum.unwrap_or_default(),
			});
		} else {
			props = json!({
				"path": path,
				"name": name,
				"ext": ext,  // Rust: no leading dot (unlike Python)
				"insert_id": insert_id_str.clone(),
				"path_relative": path_relative,
				"last_opened": last_opened.unwrap_or_default(),
				"current_checksum": checksum,
				"prev_checksum": prev_checksum.unwrap_or_default(),
			});
		}

		log::debug!("props: {:?}", props);

		if ["html", "htm", "svg"].contains(&ext) && !content.contains("</table>") {
			// call table_history_update
			(src, Some(props.clone()), Some(include_str!("../scripts/on_html_close.js").to_string()))
		} else if let Some(_header_columns) = header_columns {
			("/windows/import_dialog.html".into(), Some(props.clone()), None)
		} else {
			("/windows/file_data.html".into(), Some(props.clone()), None)
		}
	};

	if let Some(parent_label) = parent_label {
		create_window(
			app_handle.clone(),
			name.clone(),
			label.clone(),
			window_url,
			NewWindowOptions {
				props: window_props,
				init_script,
				parent_label: Some(parent_label),
				..Default::default()
			},
			is_data_table, // is data window
			Some(insert_id_str),
			Some("true".to_string())
		).await;
	} else {
		create_window(
			app_handle.clone(),
			name.clone(),
			label.clone(),
			window_url,
			NewWindowOptions {
				props: window_props,
				init_script,
				..Default::default()
			},
			is_data_table, // is data window
			Some(insert_id_str),
			None
		).await;
	}

	// did NOT help with cmd-i so may not be useful
	// 	if let Some(window) = app_handle.get_window(&label) {
	// 		window.set_focus().unwrap();
	// 	}

	numbered_windows.add_window(label, name.to_string(), &app_handle)?;

	Ok(())
}

#[command]
pub async fn open_table(
	app_handle: AppHandle,
	path: String,
	table: String,
	parent_label: String
) -> Result<()> {
	// refactor TBD: extract common code from open_table & open_row_details & open_new_row_window & open_new_row_window_file & pivot_row_to_edit
	let path_buf = PathBuf::from(path.clone());
	let path_buf_c = path_buf.clone();
	let name = path_buf.file_name().unwrap().to_str().unwrap().to_string();
	let name = format!("{} - {}", name, table);
	let id = upsert_file_in_history(&app_handle, path_buf_c, Some(date::get_date_utc()), None).await;
	let insert_id_str = insert_id().await;

	let numbered_windows: State<'_, tokio::sync::Mutex<NumberedWindows>> = app_handle.state();
	let mut numbered_windows = numbered_windows.lock().await;
    let label = escape_window_label(&format!(
        "file_{}_table_{}_{}",
        &name,
        table,
        numbered_windows.count(name.clone()) + 1
    ));

	let props = json!({
		"path": path,
		"name": name,
		"id": id,
		"insert_id": insert_id_str,
	});

	log::debug!("props: {:?}", props);

	let window_props = Some(json!({
		"path": path,
		"table": table,
		"insert_id": insert_id_str
	}));

	create_window(
		app_handle.clone(),
		name.clone(), // updated later with numbered window
		label.clone(),
		"/windows/db/db_data.html".into(),
		NewWindowOptions {
			props: window_props,
			parent_label: Some(parent_label),
			..Default::default()
		},
		true, // is data window
		Some(insert_id_str),
		None
	).await;
	app_handle.emit("file_tables_update", json!({})).unwrap();
	numbered_windows.add_window(label, name.to_string(), &app_handle)?;

	Ok(())
}

/// open details window for existing row
#[command]
pub async fn open_row_details(
	app_handle: AppHandle,
	path: String,
	table: String,
	row_id: String,
	parent_label: String,
	primary_key: String
) -> Result<()> {
	// refactor TBD: extract common code from open_table & open_row_details & open_new_row_window & open_new_row_window_file & pivot_row_to_edit
	let path_buf = PathBuf::from(path.clone());
	let name = path_buf.file_name().unwrap().to_str().unwrap().to_string();
	let name = format!("{} - {} - {}", name, table, row_id);

	let numbered_windows: State<'_, tokio::sync::Mutex<NumberedWindows>> = app_handle.state();
	let mut numbered_windows = numbered_windows.lock().await;
    let label = escape_window_label(&format!(
        "file_{}_table_{}_{}_{}",
        &name,
        table,
        row_id,
        numbered_windows.count(name.clone()) + 1
    ));

	let props = json!({
		"path": path,
		"name": name,
		"table": table,
		"row_id": row_id,
		"primary_key": primary_key,
	});
	log::debug!("props: {:?}", props);

	create_window(
		app_handle.clone(),
		name.clone(), // updated later with numbered window
		label.clone(),
		"/windows/db/record_details.html".into(), // record_details.html vs row_details.html
		NewWindowOptions {
			props: Some(props),
			parent_label: Some(parent_label),
			..Default::default()
		},
		false, // not a data window
		None,
		None
	).await;
	app_handle.emit("file_tables_update", json!({})).unwrap();
	numbered_windows.add_window(label, name.to_string(), &app_handle)?;

	Ok(())
}

/// open details window for NEW row from a DB table
#[command]
pub async fn open_new_row_window(
	app_handle: AppHandle,
	path: String,
	table: String,
	parent_label: String,
	primary_key: String
) -> Result<()> {
	// refactor TBD: extract common code from open_table & open_row_details & open_new_row_window & open_new_row_window_file & pivot_row_to_edit

	let path_buf = PathBuf::from(path.clone());
	let name = path_buf.file_name().unwrap().to_str().unwrap().to_string();
	let name = format!("{} - {} - New Record", name, table);

	let numbered_windows: State<'_, tokio::sync::Mutex<NumberedWindows>> = app_handle.state();
	let mut numbered_windows = numbered_windows.lock().await;
    let label = escape_window_label(&format!(
        "file_{}_table_{}_new_{}",
        &name,
        table,
        numbered_windows.count(name.clone()) + 1
    ));

	let props = json!({
		"path": path,
		"name": name,
		"table": table,
		"primary_key": primary_key,
		"is_new_row": true
		});
	log::debug!("props: {:?}", props);

	create_window(
		app_handle.clone(),
		name.clone(), // updated later with numbered window
		label.clone(),
		"/windows/db/record_details.html".into(), // record_details.html vs row_details.html
		NewWindowOptions {
			props: Some(props),
			parent_label: Some(parent_label),
			..Default::default()
		},
		false, // not a data window
		None,
		None
	).await;

	app_handle.emit("file_tables_update", json!({})).unwrap();

	numbered_windows.add_window(label, name.to_string(), &app_handle)?;

	Ok(())
}

/// open details window for NEW row from a file (other than SQLite)
#[command]
pub async fn open_new_row_window_file(
	app_handle: AppHandle,
	path: String,
	table_name: String
) -> Result<()> {
	// refactor TBD: extract common code from open_table & open_row_details & open_new_row_window & open_new_row_window_file & pivot_row_to_edit

	let path_buf = PathBuf::from(path.clone());
	let name = path_buf.file_name().unwrap().to_str().unwrap().to_string();
	let name = format!("{} - New Row", name);

	let path_relative = path.replace(home_dir().to_str().unwrap(), "~");

	let numbered_windows: State<'_, tokio::sync::Mutex<NumberedWindows>> = app_handle.state();
	let mut numbered_windows = numbered_windows.lock().await;
	let label = escape_window_label(&format!("file_{}_new_{}", &name, numbered_windows.count(name.clone()) + 1));

	let props = json!({
		"path": path,
		"name": name,
		"table_name": table_name,
		"is_new_row": true,
		"path_relative": path_relative
		});
	log::debug!("props: {:?}", props);

	create_window(
		app_handle.clone(),
		name.clone(), // updated later with numbered window
		label.clone(),
		"/windows/row_details.html".into(), // record_details.html vs row_details.html
		NewWindowOptions {
			props: Some(props),
			..Default::default()
		},
		false, // not a data window
		None,
		None
	).await;

	numbered_windows.add_window(label, name.to_string(), &app_handle)?;
	Ok(())
}

#[command]
pub async fn pivot_row_to_edit(
	app_handle: AppHandle,
	row_index: String,
	table_name: String,
	path: String
) -> Result<()> {
	// refactor TBD: extract common code from open_table & open_row_details & open_new_row_window & open_new_row_window_file & pivot_row_to_edit
	let path_buf = PathBuf::from(path.clone());
	let name = path_buf.file_name().unwrap().to_str().unwrap().to_string();
	let name = format!("{} - {}", name, row_index);

	let path_relative = path.replace(home_dir().to_str().unwrap(), "~");

	let props = json!({
		"row_index": row_index,
		"table_name": table_name,
		"path": path,
		"path_relative": path_relative,
	});
	log::debug!("pivot_row_to_edit -- props: {:?}", props);

	let numbered_windows: State<'_, tokio::sync::Mutex<NumberedWindows>> = app_handle.state();
	let mut numbered_windows = numbered_windows.lock().await;

    let label = escape_window_label(&format!(
        "file_{}_{}",
        &name,
        numbered_windows.count(name.clone()) + 1
    ));

	create_window(
		app_handle.clone(),
		name.clone(),
		label.clone(),
		"/windows/row_details.html".into(), // record_details.html vs row_details.html
		NewWindowOptions {
			props: Some(props),
			..Default::default()
		},
		false, // not a data window
		None,
		None
	).await;

	app_handle.emit("file_tables_update", json!({})).unwrap();
	numbered_windows.add_window(label, name.to_string(), &app_handle)?;

	Ok(())
}

#[command]
pub async fn update_table(app_handle: AppHandle) -> Result<()> {
	app_handle.emit("update_table", json!({})).unwrap();
	Ok(())
}

async fn get_record(
	app_handle: &AppHandle,
	path: PathBuf,
	mut ext: &str
) -> i32 {
	// ext:   // Rust: no leading dot (unlike Python)
	if config::SQLITE_EXTENSIONS.contains(&ext) {
		// Create connection to db and add to db instance in sql plugin
		let instances = app_handle.state::<DbInstances>();
		let mut instances = instances.inner().0.write().await;
		let pool = SqlitePool::connect(path.to_str().unwrap()).await.unwrap();
        instances.insert(
            format!("sqlite:{}", path.to_str().unwrap()),
            tauri_plugin_sql::DbPool::Sqlite(pool.clone()),
        );

		let mut conn = pool.acquire().await.unwrap();
		let query = sqlx::query("SELECT count(*) as tables_count FROM sqlite_master WHERE type='table'");
		let row = query.fetch_one(&mut *conn).await.unwrap();
		let tables_count: i32 = row.get("tables_count");
		return tables_count;
	}
	// log::debug!("{:?}", path);

	let content = std::fs::read_to_string(path).unwrap();
	let content = content.trim_start().trim_end();
	let lines: Vec<&str> = content.lines().collect();

	// if txt file contains tab in first line, treat as tabbed file
	if ext == "txt" && lines.first().unwrap_or(&"").contains('\t') {
		ext = "tabbed";
	}

	// Lines count
	if ["csv", "tsv", "tabbed"].contains(&ext) {
		// N rows - header
		let row_count = (lines.len() as i32) - 1;
		return row_count;
	}

	// Array length or 1 if it's object
	if ["json"].contains(&ext) {
		// Parse JSON
		if let Ok(json_data) = serde_json::from_str::<Value>(content) {
			if let Some(array) = json_data.as_array() {
				return array.len().try_into().unwrap();
			} else {
				return 1;
			}
		}
	}
	content.lines().count() as i32
}

#[derive(Serialize, Deserialize)]
pub struct QueryResult {
	pub data: Vec<serde_json::Map<String, Value>>,
}

// from Claude Sonnet 4
#[command]
pub async fn sqlite_exists_with_data(
	db: String,
	app_handle: AppHandle,
) -> Result<bool, String> {
	log::debug!("=== Exists With Data Debug ===");
	log::debug!("Input db: {}", db);

	// Remove the "sqlite:" prefix if present to get the raw path
	let raw_path = db.strip_prefix("sqlite:").unwrap_or(&db);

	// Extract table name from the database path
	// cache/foo.txt.sqlite -> foo.txt
	let table_name = if let Some(file_name) = std::path::Path::new(raw_path).file_name() {
		let file_name_str = file_name.to_string_lossy();
		// Remove .sqlite extension to get foo.txt
		file_name_str.strip_suffix(".sqlite").unwrap_or(&file_name_str).to_string()
	} else {
		return Err("Could not extract table name from database path".to_string());
	};

	log::debug!("Extracted table name: '{}'", table_name);

	// Convert to absolute path
	let absolute_path = if std::path::Path::new(raw_path).is_absolute() {
		PathBuf::from(raw_path)
	} else {
		let app_data_dir = app_handle.path().app_data_dir()
			.map_err(|e| format!("Failed to get app data directory: {}", e))?;
		app_data_dir.join(raw_path)
	};

	log::debug!("Checking path: {:?}", absolute_path);

	// Check if file exists
	if !absolute_path.exists() {
		log::debug!("Database file does not exist");
		return Ok(false);
	}

	// Check file size
	let file_size = std::fs::metadata(&absolute_path)
		.map(|m| m.len())
		.unwrap_or(0);
	log::debug!("Database file size: {} bytes", file_size);

	if file_size == 0 {
		log::debug!("Database file is empty");
		return Ok(false);
	}

	// Connect to database
	let database_url = format!("sqlite:{}", absolute_path.to_string_lossy());
	let pool = match SqlitePool::connect(&database_url).await {
		Ok(pool) => pool,
		Err(e) => {
			log::debug!("Failed to connect to database: {}", e);
			return Ok(false);
		}
	};

	let mut conn = match pool.acquire().await {
		Ok(conn) => conn,
		Err(e) => {
			log::debug!("Failed to acquire connection: {}", e);
			return Ok(false);
		}
	};

	// Check if table exists
	let table_exists_query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?";
	let table_check = sqlx::query(table_exists_query)
		.bind(&table_name)
		.fetch_optional(&mut *conn)
		.await;

	match table_check {
		Ok(Some(_)) => {
			log::debug!("Table '{}' exists", table_name);
		},
		Ok(None) => {
			log::debug!("Table '{}' does not exist", table_name);
			return Ok(false);
		},
		Err(e) => {
			log::debug!("Error checking if table exists: {}", e);
			return Ok(false);
		}
	}

	// Check if table has at least 1 record
	let count_query = format!("SELECT COUNT(*) as count FROM \"{}\"", table_name);
	let count_result = sqlx::query(&count_query)
		.fetch_one(&mut *conn)
		.await;

	match count_result {
		Ok(row) => {
			let count: i64 = row.try_get("count").unwrap_or(0);
			log::debug!("Table '{}' has {} records", table_name, count);
			Ok(count > 0)
		},
		Err(e) => {
			log::debug!("Error counting records in table '{}': {}", table_name, e);
			Ok(false)
		}
	}
}

// from Claude Sonnet 4
#[command]
pub async fn sqlite_select(
	db: String,
	query: String,
	values: Vec<Value>,
	app_handle: AppHandle,
) -> Result<Vec<serde_json::Map<String, Value>>, String> {
	log::debug!("=== ====================================== ===");
	log::debug!("=== SQLite Select Debug with abs path code ===");
	log::debug!("Input db: {}", db);

	// Remove the "sqlite:" prefix if present to get the raw path
	let raw_path = db.strip_prefix("sqlite:").unwrap_or(&db);
	log::debug!("Raw path: {}", raw_path);

	// Convert to absolute path
	let absolute_path = if std::path::Path::new(raw_path).is_absolute() {
		// Already absolute, use as-is
		log::debug!("Path is already absolute");
		PathBuf::from(raw_path)
	} else {
		// Relative path - resolve against app data directory
		log::debug!("Converting relative path to absolute using app_data_dir");
		let app_data_dir = app_handle
			.path()
			.app_data_dir()
			.map_err(|e| format!("Failed to get app data directory: {}", e))?;

		log::debug!("App data dir: {:?}", app_data_dir);
		app_data_dir.join(raw_path)
	};

	log::debug!("Final absolute path: {:?}", absolute_path);

	// Create the database URL with sqlite: prefix
	let database_url = format!("sqlite:{}", absolute_path.to_string_lossy());
	log::debug!("Database URL: {}", database_url);

	// Ensure the parent directory exists (only for relative paths that we're creating)
	if !std::path::Path::new(raw_path).is_absolute() {
		if let Some(parent) = absolute_path.parent() {
			if !parent.exists() {
				log::debug!("Creating directory: {:?}", parent);
				std::fs::create_dir_all(parent)
					.map_err(|e| format!("Failed to create directory {:?}: {}", parent, e))?;
			}
		}
	}

	// Check if file exists
	if !absolute_path.exists() {
		log::warn!("Database file does not exist at: {:?}", absolute_path);
		return Err(format!("Database file does not exist: {:?}", absolute_path));
	}

	let file_size = std::fs::metadata(&absolute_path)
		.map(|m| m.len())
		.unwrap_or(0);
	log::debug!("Database file size: {} bytes", file_size);

	if file_size == 0 {
		log::warn!("Database file is empty (0 bytes)");
		return Err("Database file is empty".to_string());
	}

	// Connect to database using the absolute path
	let pool = match SqlitePool::connect(&database_url).await {
		Ok(pool) => {
			log::debug!("✓ Successfully connected to database");
			pool
		},
		Err(e) => {
			log::error!("✗ Failed to connect to database: {}", e);
			return Err(format!("Failed to connect to database: {}", e));
		}
	};

	let mut conn = match pool.acquire().await {
		Ok(conn) => {
			log::debug!("✓ Successfully acquired connection");
			conn
		},
		Err(e) => {
			log::debug!("✗ Failed to acquire connection: {}", e);
			return Err(format!("Failed to acquire connection: {}", e));
		}
	};

	// First, let's check what tables exist in the database
	log::debug!("Checking database tables...");
	let table_check = sqlx::query("SELECT name FROM sqlite_master WHERE type='table'")
		.fetch_all(&mut *conn)
		.await;

	match table_check {
		Ok(table_rows) => {
			log::debug!("Found {} tables:", table_rows.len());
			for table_row in table_rows {
				if let Ok(table_name) = table_row.try_get::<String, _>("name") {
					log::debug!("  - Table: {}", table_name);
				}
			}
		},
		Err(e) => {
			log::debug!("Failed to check tables: {}", e);
		}
	}

	// Prepare query
	let mut sqlx_query = sqlx::query(&query);
	log::debug!("Prepared base query: {}", query);

	// Bind values
	for (i, value) in values.iter().enumerate() {
		log::debug!("Binding parameter {}: {:?}", i, value);
		sqlx_query = match value {
			Value::String(s) => sqlx_query.bind(s),
			Value::Number(n) => {
				if let Some(i) = n.as_i64() {
					sqlx_query.bind(i)
				} else if let Some(f) = n.as_f64() {
					sqlx_query.bind(f)
				} else {
					sqlx_query.bind(n.to_string())
				}
			},
			Value::Bool(b) => sqlx_query.bind(b),
			Value::Null => sqlx_query.bind(None::<String>),
			_ => sqlx_query.bind(value.to_string()),
		};
	}

	// Execute query
	log::debug!("Executing query...");
	let rows = match sqlx_query.fetch_all(&mut *conn).await {
		Ok(rows) => {
			log::debug!("✓ Query executed successfully");
			log::debug!("Number of rows returned: {}", rows.len());
			rows
		},
		Err(e) => {
			log::debug!("✗ Query execution failed: {}", e);
			return Err(format!("Query execution failed: {}", e));
		}
	};

	// If no rows, return empty result but don't error
	if rows.is_empty() {
		log::debug!("Query returned 0 rows (this is normal for empty tables or no matches)");
		return Ok(Vec::new());
	}

	// Convert rows to JSON-serializable format
	let mut results = Vec::new();
	log::debug!("Processing {} rows...", rows.len());

	for (row_idx, row) in rows.iter().enumerate() {
		let mut row_map = serde_json::Map::new();
		log::debug!("Processing row {}, columns: {}", row_idx, row.columns().len());

		// Get column names and values
		for (i, column) in row.columns().iter().enumerate() {
			let column_name = column.name().to_string();
			log::debug!("  Column {}: {}", i, column_name);

			// Try to get value as different types
			let value = if let Ok(val) = row.try_get::<String, _>(i) {
				log::debug!("	Value (string): {}", val);
				Value::String(val)
			} else if let Ok(val) = row.try_get::<i64, _>(i) {
				log::debug!("	Value (i64): {}", val);
				Value::Number(serde_json::Number::from(val))
			} else if let Ok(val) = row.try_get::<f64, _>(i) {
				log::debug!("	Value (f64): {}", val);
				Value::Number(serde_json::Number::from_f64(val).unwrap_or(serde_json::Number::from(0)))
			} else if let Ok(val) = row.try_get::<bool, _>(i) {
				log::debug!("	Value (bool): {}", val);
				Value::Bool(val)
			} else if row.try_get::<Option<String>, _>(i).is_ok() {
				log::debug!("	Value: NULL");
				Value::Null
			} else {
				log::debug!("	Value: Could not determine type, using NULL");
				Value::Null
			};

			row_map.insert(column_name, value);
		}

		results.push(row_map);
	}

	log::debug!("Final results: {} rows processed", results.len());
	log::debug!("=== End SQLite Select Debug ===");

	Ok(results)
}

#[command]
pub async fn read_text_file(path: PathBuf) -> Result<String> {
	fs::read_to_string(path).context("read")
}

/// Get history including filename, extension and home path replaced with ~
#[command]
pub async fn get_history(app_handle: AppHandle) -> Result<Vec<Value>> {
	// Create DB connection
	let pool: State<'_, Pool<Sqlite>> = app_handle.state();
	let mut conn = pool.acquire().await.unwrap();
	let app_config: State<'_, AppConfig> = app_handle.state();

	let kind_field = if app_config.flags.can_open_sqlite { "kind, " } else { "" };

	// TBD: create variable naming convention to distinguish
	// - static string: query = "SELECT ..."
	// - 'owned' String object: result of format!("...") -- pass &query below
	let query = format!(
		"SELECT
			{kind_field}
			path,
			record,
			checksum,
			MIN(datetime(date_opened, 'localtime')) as first_opened,
			MAX(datetime(date_opened, 'localtime')) as last_opened,
			path_id
		FROM history
		GROUP BY path_id
		ORDER BY last_opened DESC;
	",
		kind_field = kind_field
	);

	let query = sqlx::query(&query);  // need & for result of format!()

	let rows = query.fetch_all(&mut *conn).await?;
	// Convert rows to Vec<Value>
	let mut result = Vec::new();

	for row in rows {
		let mut kind = "";
		if app_config.flags.can_open_sqlite {
			kind = row.try_get("kind")?;
		}
		let realpath: String = row.try_get("path")?;
		let path = realpath.replace(home_dir().to_str().unwrap(), "~");
		let path_buf = PathBuf::from(path.clone());
		let folder = path_buf.parent().unwrap_or(Path::new(""));
		let name = path_buf.file_name().map(|n| n.to_str().context("tostr").unwrap_or("-"));
		let ext = path_buf.extension().map(|n| n.to_str().context("tostr").unwrap_or("-"));  // Rust: no leading dot (unlike Python)
		let record: i32 = row.try_get("record")?;
		let first_opened: String = row.try_get("first_opened")?;
		let last_opened: String = row.try_get("last_opened")?;
		let path_id: String = row.try_get("path_id")?;

		let mut json_val =
			serde_json::json!({
				"name": name,
				"ext": ext,  // Rust: no leading dot (unlike Python)
				"count": record,
				"folder": folder.display().to_string() + std::path::MAIN_SEPARATOR_STR,
				"realpath": realpath,
				"first_opened": first_opened,
				"last_opened": last_opened,
				"path_id": path_id,
			});

		if app_config.flags.can_open_sqlite {
			json_val.as_object_mut().unwrap().insert("kind".to_string(), kind.into());
		}

		result.push(
			// this also defines the default column order
			json_val
		);
	}
	Ok(result)
}

#[command]
pub async fn get_active_files_path(app_handle: AppHandle) -> Result<IndexMap<String, String>> {
	// refactor TBD: get_active_files_path & get_active_tables should call a common function with "REALPATH" or "TABLE"
	let webviews = app_handle.webviews();
	let mut webviews: Vec<&Webview> = webviews.values().collect::<Vec<&Webview>>();
	// sort webviews by title (lowest instance number come first)
	webviews.sort_by(|a, b| { b.window().title().unwrap_or_default().cmp(&a.window().title().unwrap_or_default()) });

	let mut paths = IndexMap::new();
	for webview in webviews {
		let label = webview.label();
		if label.starts_with("file_") && !label.contains("_table_") {
			let script = r#"
			(() => {
				window.__TAURI__.event.emit("REALPATH", {path: window.__PROPS__.path})
			})()
			"#;

			let payload = eval_with_event(webview, "REALPATH", script, Duration::from_secs(1)).await?;
			let realpath = payload["path"].as_str().context("as_str")?;
			paths.insert(realpath.to_string(), label.to_string());
		}
	}
	Ok(paths)
}

#[command]
pub async fn get_active_tables(app_handle: AppHandle) -> Result<IndexMap<String, String>> {
	// refactor TBD: get_active_files_path & get_active_tables should call a common function with "REALPATH" or "TABLE"
	let webviews = app_handle.webviews();
	let mut webviews: Vec<&Webview> = webviews.values().collect::<Vec<&Webview>>();
	// sort webviews by title (lowest instance number come first)
	webviews.sort_by(|a, b| { b.window().title().unwrap_or_default().cmp(&a.window().title().unwrap_or_default()) });

	let mut tables = IndexMap::new();
	for webview in webviews {
		let label = webview.label();
		// refactor TBD: probably rename TABLE to table_name BUT not sure what other code that affects
		if label.starts_with("file_") && label.contains("_table_") {
			let script = r#"
			(() => {
				window.__TAURI__.event.emit("TABLE", {table: window.__PROPS__.table})
			})()
			"#;

			let payload = eval_with_event(webview, "TABLE", script, Duration::from_secs(1)).await?;
			let table = payload["table"].as_str().context("as_str")?;
			tables.insert(table.to_string(), label.to_string());
		}
	}
	Ok(tables)
}

/// Check if path exists based on its id (it may have been moved or deleted)
#[command]
pub async fn exists(mut path: PathBuf, path_id: Option<String>) -> bool {
	if let Some(path_id) = path_id {
		if let Ok(path_from_id) = refind::find_path(&path_id) {
			path = path_from_id;
		}
	}
	path.exists()
}

#[command]
pub async fn save_file(file_path: String, data: String) -> bool {
	use std::fs::File;
	use std::io::prelude::*;
	use std::path::Path;

	let path = Path::new(&file_path);
	let display = path.display();

	// Open a file in write-only mode, returns `io::Result<File>`
	let mut file = match File::create(path) {
		Err(why) => panic!("couldn't create {}: {}", display, why),
		Ok(file) => file,
	};

	// Write the string to `file`, returns `io::Result<()>`
	match file.write_all(data.as_bytes()) {
		Err(why) => panic!("couldn't write to {}: {}", display, why),
		Ok(_) => log::debug!("successfully wrote to {}", display),
	}

	true
}

#[command]
pub async fn rename_file(old_path: String, new_path: String) -> bool {
	// rename / move
	let old_path = PathBuf::from(old_path);
	let new_path = PathBuf::from(new_path);
	let renamed = fs::rename(old_path, new_path);
	if let Err(e) = renamed {
		log::error!("Error renaming file: {}", e);
		print!("Error renaming file: {}", e);
		return false;
	}
	true
}

#[command]
pub async fn create_folder(folder_path: String) -> bool {
	// i.e. ensure the folder exists, creating all intermediate paths as needed
	// ... no harm if already exists
	// called at launch for 'cache' & 'backup'

	use std::fs;

	match fs::create_dir_all(&folder_path) {
		Ok(()) => true,
		Err(e) => {
			log::error!("Failed to create folder {}: {}", folder_path, e);
			false
		}
	}
}

#[tauri::command]
pub fn get_file_mod_date(path: String) -> Result<u64, String> {
	let metadata = fs::metadata(&path)
		.map_err(|e| format!("Error: {}", e))?;

	let mod_time = metadata.modified()
		.map_err(|e| format!("Error: {}", e))?;

	let timestamp = mod_time.duration_since(SystemTime::UNIX_EPOCH)
		.map_err(|e| format!("Error: {}", e))?;

	Ok(timestamp.as_secs())
}

async fn upsert_file_in_history(
	app_handle: &AppHandle,
	path: PathBuf,
	date_opened: Option<String>,
	checksum: Option<String>
) -> Result<i64> {
	// Create DB connection
	let pool: State<'_, Pool<Sqlite>> = app_handle.state();
	let mut conn = pool.acquire().await.unwrap();

	let path_id = refind::get_id(path.clone()).map_err(|e| eyre!(e))?;

	// Try to find most recent path in history
	let query = "SELECT * FROM history WHERE path_id = ? ORDER BY date_opened DESC LIMIT 1";
	let query = sqlx::query(query).bind(path_id.clone());
	let row = query.fetch_optional(&mut *conn).await.unwrap();
	if let Some(row) = row {
		// already exists
		let most_recent_path: String = row.get("path");

		// Check if path changed
		let path = path.to_str().unwrap();
		if path != most_recent_path {
			// Create new edit
			let query = "INSERT INTO edits (the_table, the_id, the_field, old_value, new_value) VALUES (?, ?, ?, ?, ?)";
            let query = sqlx::query(query)
                .bind("history")
                .bind(&path_id)
                .bind("path")
                .bind(most_recent_path.clone())
                .bind(path);
			query.execute(&mut *conn).await.unwrap();
            app_handle
                .emit(&format!("table_{}_update", "edits"), ())
                .unwrap();
			log::debug!("old path: {} new path: {}", most_recent_path, path);
		}
	}

	let ext = path.extension().unwrap_or_default().to_str().unwrap();  // Rust: no leading dot (unlike Python)
	let kind = if config::SQLITE_EXTENSIONS.contains(&ext) { "db" } else { "data" }; // Kind: data (csv, tabbed, json, html) or DB (sqlite, future Postgres connection) or text for non-data
	let record = get_record(app_handle, path.clone(), ext).await;
	let date_opened = date_opened.unwrap_or(date::get_date_utc()); // First opened

	// Insert to DB
	let table_name = "history";

	let checksm = checksum.unwrap_or_default();

	let query = format!("INSERT INTO {} (kind, path, path_id, date_opened, record, checksum) VALUES (?, ?, ?, ?, ?, ?);", table_name);
	let query = sqlx::query(&query).bind(kind).bind(path.to_str().unwrap()).bind(path_id).bind(date_opened).bind(record).bind(checksm);
	let result = query.execute(&mut *conn).await.unwrap();

	// update history table
	app_handle.emit(&format!("table_{}_update", table_name), ()).unwrap();
	Ok(result.last_insert_rowid())
}

// ===========================
// refactor TBD: for (nearly?) every function below,
// probably better for caller to directly invoke emit with appropriate params
// i.e. I don't think this indirection adds value

// layout window to main table
#[command]
pub async fn column_visibility(app_handle: AppHandle, column: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "update_column_visibility", json!(column)).unwrap();
}

#[command]
pub async fn queue_column_width(app_handle: AppHandle, column: String, label: String) {
	// println!("\n - Emitting column '{}' to window '{}' - \n", column, label);
	app_handle.emit_to(EventTarget::labeled(label), "queue_column_width", json!(column)).unwrap();
}

#[command]
pub async fn column_order(app_handle: AppHandle, order: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "update_column_order", json!(order)).unwrap();
}

#[command]
pub async fn update_sort_column_order(app_handle: AppHandle, order: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "change_sort_col_order", json!(order)).unwrap();
}

// main table to layout window
#[command]
pub async fn main_table_column_moved(app_handle: AppHandle, order: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "column_order_changed", json!(order)).unwrap();
}

#[command]
pub async fn column_width_updated(app_handle: AppHandle, column: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "update_layout_row_width", json!(column)).unwrap();
}

// sorting window to main table
#[command]
pub async fn sort_main_table(app_handle: AppHandle, sorters: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "sort_from_editor", json!(sorters)).unwrap();
}

// sorting from main table to sorting editor
#[command]
pub async fn sort_from_main_table(app_handle: AppHandle, field: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "sort_from_main_table", json!(field)).unwrap()
}

// search from search window to main table
#[command]
pub async fn update_main_table_search(app_handle: AppHandle, filters: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "update_main_table_search_filters", json!(filters)).unwrap();
}

#[command]
pub async fn update_main_table_search_checkboxes(app_handle: AppHandle, filters: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "update_main_table_checkbox_search_filters", json!(filters)).unwrap();
}

#[command]
pub async fn update_main_table_show_hidden(app_handle: AppHandle, show_hide: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "update_show_hidden", json!(show_hide)).unwrap();
}

// search from main table to search editor

#[command]
pub async fn update_search_editor_data(app_handle: AppHandle, filters: String, label: String) {
	println!("\n - fn update_SEARCH_editor_data emits update_editor_field");
	app_handle.emit_to(EventTarget::labeled(label), "update_editor_field", json!(filters)).unwrap();
}

#[command]
pub async fn update_search_column_order(app_handle: AppHandle, order: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "change_column_order", json!(order)).unwrap();
}

#[command]
pub async fn update_search_editor_checkbox(app_handle: AppHandle, filter: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "update_checked_checkbox", json!(filter)).unwrap();
}

#[command]
pub async fn update_search_hidden(app_handle: AppHandle, show_hide: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "update_hidden_checkbox", json!(show_hide)).unwrap();
}

// sync between column editor and sort editor, search editor, and layout editor
#[command]
pub async fn columns_changed(app_handle: AppHandle, sorters: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "columns_edited", json!(sorters)).unwrap();
}

#[command]
pub async fn update_search_columns(app_handle: AppHandle, columns: String, label: String) {
	println!("\n - fn update_SEARCH_columns emits CHANGE_search_columns");
	app_handle.emit_to(EventTarget::labeled(label), "change_search_columns", json!(columns)).unwrap();
}

#[command]
pub async fn update_layout_columns(app_handle: AppHandle, columns: String, label: String) {
	println!("\n - fn update_LAYOUT_columns emits CHANGE_layout_columns");
	app_handle.emit_to(EventTarget::labeled(label), "change_layout_columns", json!(columns)).unwrap();
}

// 'Define' dialog: column editor and main table update
#[command]
pub async fn update_table_columns(app_handle: AppHandle, columns: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "change_table_columns", json!(columns)).unwrap();
}

//import dialog to main table
#[command]
pub async fn merge_data(app_handle: AppHandle, data: String, label: String) {
	app_handle.emit_to(EventTarget::labeled(label), "import_data", json!(data)).unwrap();
}

// =============================

#[tauri::command]
pub async fn get_open_windows(app_handle: AppHandle) -> Result<Vec<String>, String> {
	let windows = app_handle.webview_windows();
	let window_labels: Vec<String> = windows.keys().cloned().collect();

	// println!("Open windows: {:?}", window_labels);
	Ok(window_labels)
}

// I couldn't get this to work; not sure why

// #[tauri::command]
// pub async fn emit_to_window(
//     app_handle: AppHandle,
//     target_window_label: String,
//     event_name: String,
//     payload: String
// ) -> Result<(), String> {
//     println!("\n\n - Emitting '{}' to window '{}' with payload \n\n", event_name, target_window_label);
//
//     // Clone to avoid move error
//     let label_for_error = target_window_label.clone();
//
//     app_handle
//         .emit_to(EventTarget::labeled(target_window_label), &event_name, json!(payload))
//         .map_err(|e| format!("Failed to emit to window '{}': {}", label_for_error, e))
// }

// #[tauri::command]
// pub async fn emit_to_window(app_handle: AppHandle, json_args: String) -> Result<(), String> {
// 	// Parse the JSON string
// 	let args: serde_json::Value = serde_json::from_str(&json_args)
// 		.map_err(|e| format!("Invalid JSON: {}", e))?;
//
// 	// Extract fields
// 	let target_window_label = args.get("target_window_label")
// 		.and_then(|v| v.as_str())
// 		.ok_or("Missing target_window_label")?;
//
// 	let event_name = args.get("event_name")
// 		.and_then(|v| v.as_str())
// 		.ok_or("Missing event_name")?;
//
// 	let payload = args.get("payload")
// 		.ok_or("Missing payload")?;
//
// 	println!("\n - Emitting '{}' to window '{}'\n", event_name, target_window_label);
//
// 	app_handle
// 		.emit_to(EventTarget::labeled(target_window_label), event_name, payload.clone())
// 		.map_err(|e| format!("Failed to emit: {}", e))
// }
