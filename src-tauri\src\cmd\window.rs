use crate::{ common, config, customize };
// use crate::global_config::AppConfig;  // might attempt again at some point
use crate::window::menu::{ self, create_menu, create_menu_table, MenuType };

use base64::{ engine::general_purpose, Engine as _ };
use serde::{ Deserialize, Serialize };
use tauri::{ Listener, Manager };  // TBD: restore PhysicalPosition and apply hardcoded offset
use tauri::async_runtime::spawn;
use urlencoding::encode;

fn handle_existing_window(window: tauri::Window) {
	if window.is_visible().unwrap() {
		// just bring focus back and do nothing
		window.set_focus().unwrap();
		return; // no need to do anything
	}
	// The way to focus window is hide and show currenly in Tauri on Window
	window.hide().unwrap();
	window.unminimize().unwrap(); // even if minimized
	window.set_focus().unwrap();
	window.show().unwrap();
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NewWindowOptions {
    pub props: Option<serde_json::Value>,
    pub allow_duplicate: Option<bool>,
    pub width: Option<f64>,
    pub height: Option<f64>,
    pub init_script: Option<String>,
    pub parent_label: Option<String>,
}

impl Default for NewWindowOptions {
    fn default() -> Self {
        Self {
            props: None,
            allow_duplicate: None,
            width: None,
            height: None,
            init_script: None,
            parent_label: None,
        }
    }
}

#[tauri::command]
pub async fn create_window(
	app_handle: tauri::AppHandle,
	title: String,
	label: String,
	url: String,
	options: NewWindowOptions,
	is_data_window: bool,
	insert_id: Option<String>,
	disable_file_drop: Option<String>
) {
	let window = app_handle.get_window(&label.clone());
	if options.allow_duplicate.is_none() {
		if let Some(window) = window {
			handle_existing_window(window);
			return;
		}
	}
	// Set default size
	let width = options.width.unwrap_or(config::WINDOW_SIZE_MAP.get_width(&label.clone()));
	let height = options.height.unwrap_or(config::WINDOW_SIZE_MAP.get_height(&label));

	// Create window
	log::trace!("Creating window {label}");
	let url = if url.starts_with("http") {
		tauri::WebviewUrl::External(url.parse().unwrap())
	} else {
		tauri::WebviewUrl::App(url.parse().unwrap())
	};

	let mut window = {
		let mut builder = tauri::WebviewWindowBuilder
			::new(&app_handle, label, url.clone())
			.title(title)
			.center()
			.accept_first_mouse(true)  // macOS: 1st click performs action rather than merely activating the window
			.visible(true)
			.inner_size(width, height);

		// Apply file drop disabling if the option is provided
		if disable_file_drop.is_some() {
			builder = builder.disable_drag_drop_handler();
		}

		if is_data_window {
			// TBD if NOT MacOS: enable/fix the following
			builder = builder.menu(create_menu_table(app_handle.clone(), insert_id.unwrap()).unwrap());
		} else {
			builder = builder.menu(create_menu(app_handle.clone(), MenuType::TextEditor, false).unwrap());
		}
		builder
	};

	// 2025-07-11 SSL: this wasn't reliable and broke with other refactoring; should fix
// 	if let Some(offset_from_window) = options.offset_from_window {
// 		if let Some(from_window) = app_handle.get_webview_window(&offset_from_window.label) {
// 			let from_window_position = from_window.outer_position().unwrap();
// 			let new_position = PhysicalPosition {
// 				x: from_window_position.x + offset_from_window.x,
// 				y: from_window_position.y + offset_from_window.y,
// 			};
// 			window = window.position(new_position.x.into(), new_position.y.into());
// 		}
// 	}

	// load any user-added css or js files
	window = customize::user_customize_assets(&app_handle, window);

	// Add global props; 2025-07-11 SSL: these may not get loaded correctly / in time
	if let Some(props) = options.props {
		let encoded = encode(&serde_json::to_string(&props).unwrap()).to_string();
		let encoded = general_purpose::STANDARD.encode(encoded); // encode to escape invalid json characters
		window = window.initialization_script(&format!(
			"window.__PROPS__ = JSON.parse(decodeURIComponent(atob(`{}`)))",
			encoded
		));
	}

	// 2025-07-07 SSL: better in theory but I couldn't get working
	// Get app config and add it to window props
// 	let app_config = common::get_app_config(app_handle.state::<AppConfig>());
// 	let app_config_json = serde_json::to_string(&app_config).unwrap();
// 	let encoded_config = general_purpose::STANDARD.encode(&app_config_json);
// 	window = window.initialization_script(&format!(
// 		"window.__APP_CONFIG__ = JSON.parse(atob('{}'));",
// 		encoded_config
// 	));

	if let Some(script) = options.init_script {
		window = window.initialization_script(&script);
	}

	#[allow(unused_variables)]
	let window = window.build().unwrap();

	// add & remove window names to 'Windows' menu
	let window_c = window.clone();
	let window_c1 = window.clone();
	let app_handle_c = app_handle.clone();
	let app_handle_c1 = app_handle.clone();
	window.listen("tauri://window-created", move |event| {
		menu::update_titles(&app_handle_c);
		window_c.unlisten(event.id());
	});
	window.listen("tauri://destroyed", move |event| {
		menu::update_titles(&app_handle_c1);
		window_c1.unlisten(event.id());
	});
}

// wrapper function that calls create_window with is_data_window false
#[tauri::command]
pub async fn create_document_window(app_handle: tauri::AppHandle, title: String, label: String, url: String, options: NewWindowOptions) {
	create_window(app_handle, title, label, url, options, false, None, None).await;
}

// called by menu.rs
// &str (slightly faster) instead of string literal
pub fn create_known_window_rust(app_handle: tauri::AppHandle, which: &str) {
	let title = {
		let mut chars = which.chars();
		match chars.next() {
			None => String::new(),
			Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
		}
	};
	let label = which.to_string();

	let url = match which {
		"edits" | "actions" => format!("/windows/db/{}.html", which),
		"help" => format!("/windows/help_{}.html", common::get_product_id(app_handle.clone())),
		"history" => "/windows/history.html".to_string(),
		_ => format!("/windows/{}.html", which),
	};

	spawn(
		create_document_window(
			app_handle,
			title,
			label,
			url,
			NewWindowOptions {
				..Default::default()
			},
		)
	);
}

// called from Python: string literal not &str
#[tauri::command]
pub fn create_known_window(app_handle: tauri::AppHandle, which: String) {
	create_known_window_rust(app_handle, &which);
}
