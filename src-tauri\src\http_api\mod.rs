use axum::{
    body::Bytes,
    extract::Path,
    http::{header, HeaderMap, StatusCode},
    response::IntoResponse,
    routing::{get, post},
    Router,
};
use mime_guess::from_path;
use tokio::{self, fs};
mod cmd;

pub async fn run(app_handle: tauri::AppHandle, port: i32) {
    // build our application with a route
    let app = Router::new()
        .route("/create_window", post(cmd::create_window))
        .route("/get_outer_html", post(cmd::get_outer_html))
        .route("/wait_for_document", post(cmd::wait_for_document))
        .route("/set_visible", post(cmd::set_visible))
        .route("/is_focused", post(cmd::is_focused))
        .route("/set_focus", post(cmd::set_focus))
        .route("/get_location", post(cmd::get_location))
        .route("/test", get(cmd::get_location))
        .route("/close_window", post(cmd::close_window))
        .route("/static/*path", get(serve_static_file))
        .with_state(app_handle);

    let bind_addr = format!("127.0.0.1:{}", port);
	let listener = tokio::net::TcpListener::bind(bind_addr.clone()).await.unwrap();
    log::info!("Spawn HTTP API server at http://{}...", bind_addr);
    println!("Spawn HTTP API server at http://{}...", bind_addr);
    axum::serve(listener, app).await.unwrap();
}

async fn serve_static_file(Path(path): Path<String>) -> impl IntoResponse {
    // Construct the file path
    let file_path = format!("./static/{}", path);

    // Attempt to read the file
    match fs::read(&file_path).await {
        Ok(content) => {
            // Guess the MIME type based on the file extension
            let mime_type = from_path(&file_path).first_or_octet_stream();

            // Create headers with the correct MIME type
            let mut headers = HeaderMap::new();
            headers.insert(header::CONTENT_TYPE, mime_type.to_string().parse().unwrap());

            // Return the file content with headers
            (StatusCode::OK, headers, Bytes::from(content))
        }
        Err(_) => {
            // Return a 404 if the file doesn't exist
            (
                StatusCode::NOT_FOUND,
                HeaderMap::new(),
                Bytes::from("File not found"),
            )
        }
    }
}
