use std::path::Path;

use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Listener, Window};

use crate::{edit1::get_label, window::menu};

use super::{
    dto::{Data, Filters, Finder, MatchIgnoreData, Position, ReplaceInstance, ReplaceOptions},
    traits::{
        filter_files_by_gitignore, find_occurrences_in_files, get_filtered_files,
        get_matchignore_file, get_matchignore_folder, replace_in_file, replace_set_in_file,
        search_in_folder,
    },
};

#[tauri::command]
pub async fn open_tabulater(handle: AppHandle, _window: Window, label: String, data: Data) {
    println!("OPEN TABULATER DATA => {:?}", data);
    let monitor_height: f64 = 600_f64;
    let tab_label = format!("{label}tabulated{}", get_label());
    let tabulater = tauri::WebviewWindowBuilder::new(
        &handle,
        &tab_label,
        tauri::WebviewUrl::App("src/edit1/ui/find-table/find-table.html#scroll-to".into()),
    )
    .title(&data.title)
    .inner_size(850.0, monitor_height)
    .disable_drag_drop_handler()
    .maximizable(true)
    .minimizable(false)
    .focused(true)
    .center()
    .build()
    .unwrap();
    let t = tabulater.clone();
    tabulater.listen("tabulater:created", move |_| {
        t.emit_to(t.label(), "tabulater:set-data", &data).unwrap();
    });

	let window_c = tabulater.clone();
	let window_c1 = tabulater.clone();
	let app_handle_c = handle.clone();
	let app_handle_c1 = handle.clone();

	tabulater.listen("tauri://window-created", move |event| {
		menu::update_titles(&app_handle_c);
		window_c.unlisten(event.id());
	});
	tabulater.listen("tauri://destroyed", move |event| {
		menu::update_titles(&app_handle_c1);
		window_c1.unlisten(event.id());
	});
}

#[tauri::command]
pub fn search_in_files(paths: Vec<String>, finder: Finder) -> Result<Vec<Data>, String> {
    let data = find_occurrences_in_files(&paths, &finder);

    if data.is_empty() {
        Err("No occurrences found in any file.".to_string())
    } else {
        Ok(data)
    }
}

#[tauri::command]
pub fn search_in_folders(paths: Vec<String>, finder: Finder) -> Result<Vec<Data>, String> {
    let mut all_data = Vec::new();

    for folder_path in paths {
        let path = Path::new(&folder_path);
        if path.is_dir() {
            all_data.extend(search_in_folder(path, &finder));
        } else {
            return Err(format!("Path is not a directory: {}", folder_path));
        }
    }

    if all_data.is_empty() {
        Err("No occurrences found in any folder.".to_string())
    } else {
        Ok(all_data)
    }
}

#[tauri::command]
pub fn get_files_in_folders(paths: Vec<String>, filters: Filters) -> Result<Vec<String>, String> {
    let mut all_file_paths = Vec::new();
    for folder_path in paths {
        let path = Path::new(&folder_path);
        if !path.is_dir() {
            return Err(format!("Path is not a directory: {}", folder_path));
        }
        let mut paths = get_filtered_files(&folder_path, &filters.clone());
        all_file_paths.append(&mut paths);

        // for entry in WalkDir::new(path)
        // 	.follow_links(true)
        // 	.into_iter()
        // 	.filter_map(Result::ok)
        // 	.filter(|e| e.file_type().is_file()) {
        // 	all_file_paths.push(entry.path().to_string_lossy().into_owned());
        // }
    }

    if all_file_paths.is_empty() {
        Err("No files found in the specified folders.".to_string())
    } else {
        Ok(all_file_paths)
    }
}

#[tauri::command]
pub async fn replace_value(
    instance: ReplaceInstance,
    file_path: String,
    options: ReplaceOptions,
) -> Result<bool, String> {
    let result = replace_in_file(instance, file_path, options);
    match result {
        Ok(rs) => {
            if rs {
                Ok(true)
            } else {
                Err("Replace failed: file may have changed.".into())
            }
        }
        Err(_e) => Err("Unknown error in replace one: check debug log.".into()),
    }
}

#[tauri::command]
pub async fn replace_values(
    instances: Vec<ReplaceInstance>,
    file_path: String,
) -> Result<Vec<Position>, String> {
    let result = replace_set_in_file(instances, file_path);
    match result {
        Ok(rs) => Ok(rs),
        Err(_e) => Err("Unknown error in replace many: check debug log.".into()),
    }
}

#[tauri::command]
pub fn get_review_data(
    paths: Vec<String>,
    filters: Filters,
) -> Result<Vec<MatchIgnoreData>, String> {
    let mut all_file_paths: Vec<MatchIgnoreData> = Vec::new();
    for folder_path in paths {
        let path = Path::new(&folder_path);
        if path.is_dir() {
            let mut paths = get_matchignore_folder(&folder_path, &filters.clone());
            all_file_paths.append(&mut paths);
        } else {
            let result = get_matchignore_file(&folder_path, &filters.clone());
            match result {
                Some(review) => {
                    println!("result {:?}", review);
                    all_file_paths.append(&mut vec![review]);
                }
                None => {
                    println!("review => None");
                }
            }
        }
    }

    if all_file_paths.is_empty() {
        Err("No files found in the specified folders.".to_string())
    } else {
        Ok(all_file_paths)
    }
}

#[tauri::command]
pub fn check_files_status(
    folders: Vec<String>,
    files: Vec<String>,
    filters: Filters,
) -> Result<Vec<String>, String> {
    let result = filter_files_by_gitignore(folders, files, filters);
    Ok(result)
}

#[tauri::command]
pub fn filter_valid_paths(paths: Vec<String>, filters: Filters) -> Result<Vec<String>, String> {
    let result = get_review_data(paths, filters);
    let mut data: Vec<String> = Vec::new();
    match result {
        Ok(values) => {
            for v in values {
                if v.search == "Yes" {
                    data.push(v.path);
                }
            }
            Ok(data)
        }
        Err(_) => Err("Failed to filter paths.".into()),
    }
}
