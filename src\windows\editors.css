/* editors.css */

#header-btn-container div {
/* 	margin-right: 1rem; */
}

#delete-btn-container {
	/* margin: auto 1rem; */
}

/*
#main-container {
	display: flex;
	gap: 50px;
}
 */

#table {
	overflow: hidden;
}

.dialog-btn-group,
#error-text-area {
	margin-top: 20px;
}

#editor-column {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.editor-page {
	border-bottom: 8px solid var(--pale_blue);
	border-top: 8px solid var(--pale_blue);
	margin-top: 4px;
}

/* TBD: change tapx1 to match (vs 80% which depends on padding etc.) */
.tabulator-row .tabulator-cell.tabulator-row-handle .tabulator-row-handle-box {
	width: 18px;
}

.editor-page .tabulator-cell input[type='text'] {
	background: var(--pale_blue);
}

/* like editable-color but that's too light for this context */
#edits-page {
	border-bottom: 8px solid var(--pale_green);
	border-top: 8px solid var(--pale_green);
	padding-top: 6px;
}

#actions-page,
#history-page {
	border-bottom: 8px solid var(--pale_tan);
	border-top: 8px solid var(--pale_tan);
	padding-top: 6px;
}

#db-index-page {
	border: 3px solid var(--pale_gray);

	padding-left: 6px;
	padding-right: 6px;

	padding-top: 4px;
	padding-bottom: 4px;
}

#not-sorted-info {
	margin-top: 15px;
	margin-bottom: 15px;
	margin-left: 1rem;
}

#not-sorted-info span {
	color: #aaa;
	background-color: white;
	border-bottom: 1px solid #aaa;
	padding: 5px;
	padding-left: 1rem;
	padding-right: 1rem;
}

#static-layout-table {
	margin: 0 20px 20px 20px;
}

#export-editor-table {
	display: flex;
	justify-content: space-between;
}

#editor-container {
	margin-top: 6px;
}

#search-dialog #editor-container {
	margin-bottom: 4px;
}

.export-section {
	margin-top: 0.5rem;
}

/* mimic .tabulator .tabulator-header */
.export-section .tabulator-header {
	color: #363636;
	font-weight: 700;
}

#export-btn-group button:hover,
#header-btn-container button:hover,
#home-page button:hover {
	/* background: #ccc; */ /* TBD: works in tapx1; here it converts button from roundrect to square */
}

#sort-control-panel {
/*
	display: flex;
	justify-content: center;
	align-items: center;
 */
	padding: 10px;
}

#sort-control-panel button.active {
	font-weight: bold;
}

#column-select {
	margin-right: 10px;
	min-width: 150px;
}

#sort-toggle {
	margin-right: 10px;
}

#ok-cancel-btn-group {
	display: flex;
	justify-content: space-between;
	margin-top: 0.5rem;
	margin-bottom: 0.5rem;
	margin-left: 1rem;
	width: 250px;
}

/*
#export-btn-group button,
#ok-cancel-btn-group button {
	padding-left: 10px;
	padding-right: 10px;

	padding-top: 3px;
	padding-bottom: 3px;
}
 */

#static-layout-table .tabulator-row .tabulator-cell:not(:first-child) {
	padding-top: 3px;
}

#static-layout-table .tabulator-row .tabulator-cell:first-child {
	padding-top: 5px;
}

/*column editor*/
#column-table .tabulator-editable {
	background-color: transparent;
}

.delete-btn-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
}

.delete-btn-container .removed {
	/* background: var(--pale_gray); */ /* TBD: fix on macOS; converts from roundrect to square */
	color: red;
}

.remove-btn {
	margin: 0 10px 0 10px;
}

.dialog {
	display: none;
	position: fixed; /*absolute;*/
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	align-items: center;
	justify-content: center;
	z-index: 10;
}

.dialog-content {
	background: white;
	padding: 20px;
	border-radius: 5px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dialog-btn-group {
	display: flex;
	justify-content: space-between;
}

.dialog-btn-group button {
	padding: 10px 20px;
	margin: 0 10px;
	cursor: pointer;
}

#add-column-section {
	margin: 20px 5px;
}

#add-column-name,
#add-column-label,
#add-column-type {
	margin-right: 5px;
}

#add-column-add-btn {
	padding: 1px 10px;
}

/*save dialog*/
#save-as-dialog[data-hidden="true"] {
	display: none;
}

#save-as-dialog[data-hidden="false"] {
	display: flex;
	justify-content: center;
	z-index: 20;
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background-color: rgba(255, 255, 255, 0.7);
	align-items: center;
}

#error-text-area {
	display: none;
	height: auto;
	width: 100%;
	overflow-y: auto;
}

#header-btn-container {
	display: flex;
	gap: 1.5rem;
	flex-wrap: nowrap; /* Prevents wrapping to the next row */
}

#header-info-row {
	margin-left: 0.25rem;
	margin-top: 0.25rem;
}

#editor-container,
#header-info-row {
	font-size: 14px;
}

#edit-rows {
	display: flex;
	align-items: self-start;
	gap: 5px;
}

#search-table .tabulator-header {
	display: none;

}

#table .tabulator-header-filter input {
	padding: 4px;
	width: 100%;
	box-sizing: border-box;
	border-radius: 0;
}

.hide-info {
	display: none;
}

#format-container {
	display: grid;
	grid-template-columns: auto auto auto;
	gap: 0.2rem;
}

.tabulator-cell input[type='text'],
.tabulator-cell select {
	border: 1px solid #dbdbdb;
	border-radius: 0;
}

#column-dialog div[tabulator-field='label'] {
	padding-top: 0;
	padding-bottom: 0;
}

.tabulator-headers div[tabulator-field='(details icon)'] .tabulator-header-filter input,
.tabulator-headers div[tabulator-field='(hidden)'] .tabulator-header-filter input {
	display: none;
}

/*
#control-panel-container {
	position: absolute;
	left: var(--sort-table-width);
}
 */

.disable-nav {
	pointer-events: none;
	background-color: var(--mid_gray);
}

#column-table >* {
	user-select: auto !important;
}

.tabulator-edit-list {
	background-color: var(--pale_blue);
}

.subtle {
	background: white;
	color: var(--mid_gray);
}

.pale {
	background: var(--pale_tan);
	color: black;
}

#static-layout-table {
	display: block;
}

.tabulator-row.tabulator-selected,
.tabulator-row.tabulator-selected:hover {
	background-color: inherit !important;
}

.tabulator-row.tabulator-selected .tabulator-cell[tabulator-field='(checked)'],
.tabulator-row.tabulator-selected .tabulator-cell[tabulator-field='(details icon)'],
.tabulator-row.tabulator-selected .tabulator-cell[tabulator-field='(row_selector)'],
.tabulator-row.tabulator-selected .tabulator-cell:not([tabulator-field]) {
	background-color: var(--pale_tan) !important;
}


.tabulator-headers .tabulator-col:not([tabulator-field]) .tabulator-col-sorter {
  display: none !important;
}
