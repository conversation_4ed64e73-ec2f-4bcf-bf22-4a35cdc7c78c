import { listen } from '@tauri-apps/api/event';
import { getOpenDocsPath, getTabulatedPaths } from '../find-table';
import {
	ignoreTextArea,
	matchTextArea,
	omitInvisibleFilesCheck,
	omitInvisibleFoldersCheck,
	selFilesCheck,
	selFoldersCheck,
	useGitIgnoreCheck,
} from './elements';
import filesFoldersStorage from './files-folders-js/files-folders-storage';
import { invoke } from '@tauri-apps/api/core';
export function getFilters() {
	const match = matchTextArea.value.split('\n');
	const ignore = ignoreTextArea.value.split('\n');
	//console.log('Match #=>', match);
	let matchRules = match.filter((m) => m != '');
	//console.log('Match Rules', matchRules);
	return {
		match_rules: matchRules,
		ignore_rules: ignore.filter((rule) => rule != ''),
		use_gitignore: useGitIgnoreCheck.checked,
		omit_invisible_files: omitInvisibleFilesCheck.checked,
		omit_invisible_folders: omitInvisibleFoldersCheck.checked,
	};
}
export async function getReviewData() {
	const openDocs = [];
	await getOpenDocsPath();
	listen('tabulater:set-file-path', (d) => {
		console.log('review payload', d.payload);
		if (d.payload) openDocs.push(d.payload);
	});
	await new Promise((resolve) => {
		setTimeout(() => {
			resolve(true);
		}, 2000);
	});
	console.log('Open Docs Review', openDocs);
	const dir_paths = selFoldersCheck.checked ? await filesFoldersStorage.getDirsPaths() : [];
	const file_paths = selFilesCheck.checked ? await filesFoldersStorage.getFilesPaths() : [];
	const paths = [...dir_paths, ...file_paths, ...openDocs];
	const filters = getFilters();
	filters.use_gitignore = filters.use_gitignore && selFoldersCheck.checked;
	//console.log('Filters #=>', filters);
	return await invoke('get_review_data', { paths, filters });
}

export async function checkSelectedFilesStatus() {
	const filters = getFilters();
	const folders = await filesFoldersStorage.getDirsPaths();
	const files = await filesFoldersStorage.getFilesPaths();
	const tabluatedPaths = getTabulatedPaths();
	let paths = files;
	if (tabluatedPaths.length > 0) {
		let uniquePaths = new Set([...files, ...tabluatedPaths]);
		paths = [...uniquePaths];
	}
	return await invoke('check_files_status', { folders, files: paths, filters });
}

export async function filterValidPaths(paths) {
	if (!Array.isArray(paths)) {
		return [];
	}

	const validPaths = paths.filter((p) => p != null);
	if (validPaths.length === 0) {
		return [];
	}

	try {
		const filters = getFilters();
		return await invoke('filter_valid_paths', { paths: validPaths, filters });
	} catch (error) {
		console.error('filterValidPaths ERROR:', error);
		return [];
	}
}
