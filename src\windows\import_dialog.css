table {
	margin-bottom: 1rem;
	margin-top: 0.5rem;
	border-bottom: 2px solid var(--deep_green);
}

#after-table {
	margin-left: 0.5rem;
}

.import-table th {
	color: white;
	background-color: var(--deep_green);
	text-align: center;
}

.record1-label {
	outline: 1px solid var(--deep_green);
	outline-offset: -1px;
}

#importing-n-m {
	margin-top: 0.5rem;
}

.non-matched td {
	border-top: 1px dotted red;
}

.extra {
	display: flex;
	flex-direction: row;
	gap: 10px;
	margin: 10px;
}

.import-extras-divider {
	border-style: dashed;
}

#importing-from-to,
#importing-n-m {
	font-size: 14px;
}

#importing-n-m {
	font-style: italic;
}

#header-row-container,
.first-row-wrapper {
	display: flex;
	gap: 0.5rem;
}

#header-row-container {
	margin-bottom: 20px;
}

.drag-handle {
	width: 100%;
}

.bar {
	width: 100%;
	height: 3px;
	margin-top: 2px;
	background-color: #666;
}

td:has(.drag-handle) {
	display: grid;
	grid-template-columns: auto 1fr;
	column-gap: 10px;
}

td:has(.drag-handle) > *:nth-child(2) {
	justify-self: start;
}

.handle-container {
	display: inline-flex;
	flex-direction: column;
	width: 16px;
	/* height: 27px; */
	/* justify-content: center; */
	align-items: center;
}

