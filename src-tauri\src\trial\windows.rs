use super::TrialInfo;
use crate::{
    config,
    trial::{decrypt, encrypt, get_app_hwid},
};
use anyhow::{bail, Context, Result};
use chrono::{DateTime, Utc};
use std::path::PathBuf;
use winreg::{self, enums::HKEY_CURRENT_USER, RegKey};

pub fn hide_file(path: PathBuf) -> Result<()> {
    // Docs.rs
    // https://microsoft.github.io/windows-docs-rs/doc/windows/Win32/Storage/FileSystem/fn.SetFileAttributesW.html

    // GetFileAttributesW
    // https://learn.microsoft.com/en-us/windows/win32/api/fileapi/nf-fileapi-getfileattributesw

    // SetFileAttributesW
    // https://learn.microsoft.com/en-us/windows/win32/api/fileapi/nf-fileapi-setfileattributesw

    use windows::core::{HSTRING, <PERSON><PERSON>TR};
    use windows::Win32::Storage::FileSystem::{
        GetFileAttributesW, SetFileAttributesW, FILE_ATTRIBUTE_HIDDEN, FILE_FLAGS_AND_ATTRIBUTES,
        INVALID_FILE_ATTRIBUTES,
    };

    let path = HSTRING::from(path.to_str().context("cant convert path to str")?);
    unsafe {
        let attributes: u32 = GetFileAttributesW(PCWSTR(path.as_ptr()));
        if attributes == INVALID_FILE_ATTRIBUTES {
            bail!("Invalid file attributes");
        }
        let new_attributes = attributes | FILE_ATTRIBUTE_HIDDEN.0;
        SetFileAttributesW(
            PCWSTR(path.as_ptr()),
            FILE_FLAGS_AND_ATTRIBUTES(new_attributes),
        )
        .context("failed to set attributes")?;
    }
    Ok(())
}

fn get_paths(app: &tauri::AppHandle) -> Result<(PathBuf, PathBuf, PathBuf, String)> {
    let hwid = get_app_hwid(app)?;
    let filename_a = &hwid[0..9];
    let filename_b = &hwid[9..18];
    let reg_key = &hwid[18..27];
    let appdata = std::env::var("APPDATA")?;
    let localappdata = std::env::var("LOCALAPPDATA")?;

    let path_a = PathBuf::from(format!("{}\\{}", appdata, filename_a));
    let path_b = PathBuf::from(format!("{}\\{}", localappdata, filename_b));
    let reg_path = PathBuf::from("Software").join(app.package_info().name.clone());
    Ok((path_a, path_b, reg_path, reg_key.to_owned()))
}

pub fn get_info(app: &tauri::AppHandle) -> Result<Option<TrialInfo>> {
    let (path_a, path_b, reg_path, _reg_key) = get_paths(app)?;
    let hkcu = RegKey::predef(HKEY_CURRENT_USER);

    let (_key, disp) = hkcu.create_subkey(reg_path.clone())?;
    let mut reg_key_exists = true;
    if disp == winreg::enums::RegDisposition::REG_CREATED_NEW_KEY {
        reg_key_exists = false;
    }

    let paths = vec![path_a.clone(), path_b.clone()];
    if cfg!(debug_assertions) {
        println!(
            "\n - to clear invisible: rm -f \"{}\" \"{}\" \n",
            path_a.display(),
            path_b.display()
        );
    }

    let mut count_exists = 0;
    for path in paths.clone() {
        if path.exists() {
            count_exists += 1;
        }
    }

    if count_exists == 0 && !reg_key_exists {
        // time to create new one!
        return Ok(None);
    }
    if count_exists == paths.len() && reg_key_exists {
        // all exists, let's read the info

        match *config::TRIAL_INFO_ENCRYPTION {
            true => {
                let enc_info = std::fs::read_to_string(path_a)?;
                let dec_info = decrypt(&enc_info)?;
                let info: TrialInfo = serde_json::from_str(&dec_info)?;
                return Ok(Some(info));
            }
            false => {
                let info_str = std::fs::read_to_string(path_a)?;
                let info: TrialInfo = serde_json::from_str(&info_str)?;
                return Ok(Some(info));
            }
        }
    }

    // something bad with trial files.
    bail!("one of the trial date files missing");
}

pub fn activate(app: &tauri::AppHandle) -> Result<TrialInfo> {
    let (path_a, path_b, reg_path, _reg_key) = get_paths(app)?;

    let utc: DateTime<Utc> = Utc::now();
    let utc_date_string = utc.format("%Y-%m-%d %H:%MZ").to_string();
    let mut info = TrialInfo {
        active_date: utc_date_string,
        last_popup_date: None,
    };

    let hkcu = RegKey::predef(HKEY_CURRENT_USER);
    let (_key, disp) = hkcu.create_subkey(reg_path.clone())?;
    match disp {
        winreg::enums::RegDisposition::REG_CREATED_NEW_KEY => {
            log::debug!("A new key has been created")
        }
        winreg::enums::RegDisposition::REG_OPENED_EXISTING_KEY => {
            // bail!("trial already activated in registry!")
        }
    }

    let paths = vec![path_a, path_b];
    log::debug!("trial paths: {paths:?}");
    let mut count_exists = 0;
    for path in paths.clone() {
        if path.exists() {
            count_exists += 1;
        }
    }
    // Check if one of the files exists already
    if count_exists > 0 {
        bail!("Trial already activated!");
    }

    info.write(app)?;
    Ok(info)
}

pub fn update_popup_date(app: &tauri::AppHandle) -> Result<()> {
    let info = get_info(app);
    if let Ok(Some(mut info)) = info {
        let utc: DateTime<Utc> = Utc::now();
        let utc_date_string = utc.format("%Y-%m-%d %H:%MZ").to_string();
        info.last_popup_date = Some(utc_date_string);
        info.write(app)?;
    }
    Ok(())
}

impl TrialInfo {
    fn write(&mut self, app: &tauri::AppHandle) -> Result<()> {
        let (path_a, path_b, reg_path, reg_key) = get_paths(app)?;
        for path in [path_a, path_b] {
            match *config::TRIAL_INFO_ENCRYPTION {
                true => {
                    let content = serde_json::to_string_pretty(&self)?;
                    let enc = encrypt(&content);
                    std::fs::write(path.clone(), enc)?;
                }
                false => {
                    std::fs::write(path.clone(), serde_json::to_string_pretty(&self)?)?;
                }
            }
            hide_file(path)?;
        }

        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let (key, _) = hkcu.create_subkey(reg_path.clone())?;
        match *config::TRIAL_INFO_ENCRYPTION {
            true => {
                let enc = encrypt(&serde_json::to_string_pretty(&self)?);
                key.set_value(reg_key, &enc)?;
            }
            false => {
                key.set_value(reg_key, &serde_json::to_string_pretty(&self)?)?;
            }
        }

        Ok(())
    }
}
