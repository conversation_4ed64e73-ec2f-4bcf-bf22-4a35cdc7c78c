@import 'style/theme.css';
@import 'style/titlebar.css';
@import 'style/menu.css';
@import 'style/split-screen.css';

html,
body {
	padding: 0;
	margin: 0;
	width: 100vw;
	height: 100vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	font-family: Avenir, 'Lucida Grande', Lucida, Verdana, sans-serif;
}

* {
	box-sizing: border-box;
}
#container {
	width: 100vw;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	flex-grow: 1;
}

#editor {
	width: 100%;
	height: 100%;
	flex-grow: 1;
	/* overflow: hidden; */
}

#editor.split {
	width: 100%;
	height: 50%;
}

#editor .cm-editor {
	height: 100%;
}

:is(#editor, #editor2) .cm-activeLine {
	background-color: transparent !important;
}

.cm-content {
	cursor: text !important;
}

.cm-selectionBackground {
	background-color: var(--pale_tan) !important;
}

.cm-gutters.blured {
	background-color: var(--gutters-blured-bg) !important;
}

.cm-content.blured {
	background-color: var(--editor-blured-bg) !important;
}

.cm-gutters {
	background-color: var(--gutters-bg) !important;
}
