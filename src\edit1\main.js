// import { javascript } from '@codemirror/lang-javascript';
import { EditorState } from '@codemirror/state';
import { EditorView } from '@codemirror/view';
import { invoke } from '@tauri-apps/api/core';
import { listen, TauriEvent } from '@tauri-apps/api/event';
import { getCurrentWindow, Window } from '@tauri-apps/api/window';
import { espresso } from 'thememirror';
import { mySetup } from './editor-setup';
import { AutoSave } from './js/autosave.js';
import { Editor } from './js/editor';
import { EditorManager } from './js/editor-manager';
import { getFileNameFromPath } from './js/file';
import { FindDialog } from './js/findDialog';
import { FindTable } from './js/findTable';
import { initMenuFileOptions } from './js/menu-file-options';
import { SplitScreen } from './js/split-screen/split-screen';
import "./js/split-screen/split-thumb-dragger.js";
import { MyStorage } from './js/storage';
import './js/window-header.js';
// import './js/theme';

initMenuFileOptions();
FindDialog.initFinderListeners();
FindTable.initListeners();
const appWindow = getCurrentWindow();

// set focus to the editor when window is focus
appWindow.listen(TauriEvent.WINDOW_FOCUS, async (e) => {
	console.log('Setting Focus to CM on window focus');
	if (e.windowLabel == appWindow.label) {
		view.focus();
		await invoke('set_active_window');
	}
});

appWindow.onCloseRequested(()=>{
	// alert(`window ${appWindow.label} closed !`)
	AutoSave.updateIClose(true);
})

//in my machine it work even without this code
document.addEventListener('focus', () => {
	console.log('Setting Focus to CM on document focus');
	view.focus();
});

listen('all:get-file-path', async (e) => {
	const path = await MyStorage.getFileFolder();
	console.log('Window Path', path);
	new Window(e.windowLabel).emit('tabulater:set-file-path', path);
});

/**
 * parameters : the file object from backend(rust)
 * @description set  the editor content and window title
 */
appWindow.listen('file:on-open', async (e) => {
	MyStorage.setData(e.payload);
	let data = MyStorage.getData();
	Editor.setValue(data.content);

	let folder = await invoke('get_file_folder', { path: data.path });
	appWindow.setTitle(`${getFileNameFromPath(MyStorage.getFileName())} - ${folder}`);
	// console.log('FOLDER', folder);
	Editor.setLanguageByExt(data.ext);

	console.log('listen file:on-open DONE');
	await AutoSave.checkAutosave();
});

/**
 * parameters : the new object of the saved file from Rust
 * @description store and set the new data
 */
listen('file:on-saved-as', async (e) => {
	MyStorage.setFile(e.payload);
	Editor.setValue(MyStorage.getData().content);
	let folder = await invoke('get_file_folder', { path: MyStorage.getData().path });
	appWindow.setTitle(`${getFileNameFromPath(MyStorage.getFileName())} - ${folder}`);
	Editor.setLanguageByExt(MyStorage.getData().ext);
});

// listen when window loaded and confirm that to the backend
document.addEventListener('DOMContentLoaded', async () => {
	console.log('window:on-loaded');
	await invoke('set_active_window');
	await appWindow.emit('window:on-loaded');
});

let timer = 0;
const focusTrackerExtension = EditorView.updateListener.of(async (update) => {
	if (update.focusChanged) {
		const status = update.view.hasFocus;
		console.log(`Main editor is now focused : ${status}`);
		if(status || SplitScreen.isSplitViewFocused()){
			Editor.setFocusColorEffect(status);
			SplitScreen.setFocusColorEffect(!status);
		}
	}

	if (update.docChanged) {
		clearTimeout(timer)
    	timer = setTimeout(async ()=>{
			const newContent = update.state.doc.toString();
			await AutoSave.autosave(newContent)
		},2000)
  	}
});

export const startState = EditorState.create({
	// temporary text for testing:
	doc: `hello 1 --123456789
Hello 2 9
hello 3 9
this ---- 1456789
that ----
many word
more word 123456
hello 8 9
hellox ab
hello end

wrap 1: phrase: a quick brown fox jumps over the lazy dog
wrap 2: Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut. Every good kid deserves fudge. When in the course of human events.

middle

a
b
c
d
e

bell
Dell
fell
jello
mellow
sell
tell
well

the end
`,
	extensions: [
		mySetup,
		Editor.lineWrap.of([EditorView.lineWrapping]),
		Editor.theme.of(espresso),
		// Editor.language.of(javascript()),
		Editor.selectionListener.of([]),
		SplitScreen.splitListener.of([]),
		focusTrackerExtension,
	],
	selection: { anchor: 0 },
});

export let view = new EditorView({
	state: startState,
	parent: document.getElementById('editor'),
});

Editor.attachView(view)

view.focus();

document.querySelector('#editor .cm-gutters')?.addEventListener('click', () => {
	view.focus();
});

AutoSave.initListeners(view)





await appWindow.emit('window:on-loaded');


await getCurrentWindow().onFocusChanged(async ({ payload }) => {
	console.log('#########################################');
	console.log('Focus changed, window is focused? ', payload);
	await invoke('set_active_window');
	if (EditorManager.splitIsFocused() && SplitScreen.getSplitView()) {
		SplitScreen.getSplitView().focus();
	} else view.focus();
});

listen("window:focused",async (e)=>{
	console.log("FOCUS ==>",e.payload.label);
	if(appWindow.label === e.payload.label){
		await invoke('set_active_window');
		if (EditorManager.splitIsFocused() && SplitScreen.getSplitView()) {
			SplitScreen.getSplitView().focus();
		} else view.focus();
		appWindow.emit("finder:change-parent",{label:appWindow.label})
	}
})


