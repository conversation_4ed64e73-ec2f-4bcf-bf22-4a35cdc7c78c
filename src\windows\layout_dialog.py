"""
layout_dialog.py
"""

from browser import aio, document, window
import json
import math
import tauri

# local imports
import deeply_utils
import shortcuts  # runs the bind commands on import
import table_utils

layout_editor_table = None
layout_table_data = window['__PROPS__'].table_data
current_window = tauri.window.getCurrentWindow()
label = current_window.label[:current_window.label.rindex('_layout_editor')]


def create_layout_editor():
	global layout_editor_table

	layout_editor_header_data = [
		{
			'field': 'visible',
			'title': 'Show',
			'formatter': checkbox_formatter,
			'headerSort': False,
			'width': 70,
		},
		{
			'field': 'label',
			'title': 'Label',
			'headerSort': False,
		},
# 		{
# 			'field': 'name',
# 			'title': 'Name',
# 			'headerSort': False,
# 		},
		{
			'field': 'width',
			'title': 'Width',
			'cellEdited': on_width_edited,
			'editor': custom_editor,  # callback function
			# 'number',
			'headerSort': False,
		}
	]

	layout_table_config = {
		'height': 'auto',
		'rowHeight': table_utils.layout_define_row_height,
		'layout': 'fitDataTable',
		'movableRows': True,
		'movableColumns': True,
		'editorParams': {'elementAttributes': {'spellcheck': False}},
		'editorEmptyValue': 0,
		'rowHeader': {
			'rowHandle': True,
			'formatter': 'handle',
			'headerSort': False,
			'width': table_utils.handle_width,
		},
		'columns': layout_editor_header_data,
		'data': layout_table_data,
	}

	layout_editor_table = window.Tabulator.new(
		'#layout-table',
		layout_table_config,
	)
	layout_editor_table.on('rowMoved', on_layout_row_moved)


# table event listener handlers
def on_layout_row_moved(row):
	global layout_table_data
	deeply_utils.pp('on_layout_row_moved')

	new_order = [row_.getData()['name'] for row_ in layout_editor_table.getRows()]
	args = {
		'order': json.dumps({'order': new_order}),
		'label': label,
		}

	tauri.invoke('column_order', args)  # emit update_column_order to call sync_utils.update_column_order

	# -----
	# I could not get table_utils.emit or emit_args to call Tauri; failed with 'undefined undefined'

	# deeply_utils.pp('on_layout_row_moved -> calling table_utils.emit_args(update_column_order)')
	# table_utils.emit_args('update_column_order', args)

	# deeply_utils.pp('on_layout_row_moved -> calling table_utils.emit(label, update_column_order, {dict})')
	# table_utils.emit(label, 'update_column_order', {'order': new_order})  # call sync_utils.update_column_order(tauri_message, table, create_tabulator_table_CALLBACK, get_table_CALLBACK)


async def on_width_edited(cell):
	global layout_table_data
# 	deeply_utils.pp('on_width_edited')

	key = cell.getRow().getData()['name']
	new_width = cell.getValue()
	if new_width is math.isnan:
		new_width = 0

	for row in layout_table_data:
		if row['name'] == key:
			row['width'] = new_width
			break

	args = {
		'column': json.dumps({'name': key, 'width': new_width}),
		'label': label,
		}

# 	deeply_utils.pp('on_width_edited -- args', args)
	# table_utils.invoke_tauri('column_width', args, 'on_width_edited', debug=True)
	tauri.invoke('queue_column_width', args)  # emit to call sync_utils.queue_column_width
# 	deeply_utils.pp('on_width_edited -- DONE')  # Scott TBD July: enable this to help debug


def checkbox_formatter(cell, *args):
	global layout_table_data
	checkbox = document.createElement('input')

	checkbox.type = 'checkbox'
	checkbox.checked = bool(cell.getValue())

	# -----
	def on_change(e):
		key = cell.getRow().getData()['name']
		is_visible = True

		for row in layout_table_data:
			if row['name'] == key:
				row['visible'] = not row['visible']
				is_visible = row['visible']
				break
		cell.setValue(is_visible)

		args = {
			'column': json.dumps({'name': key, 'is_visible': is_visible}),
			'label': label,
			}
		tauri.invoke('column_visibility', args)  # emit update_column_visibility
	# -----

	checkbox.bind('change', on_change)
	return checkbox


def change_row_order(tauri_message):
	global layout_table_data
	new_row_order = json.loads(dict(tauri_message)['payload'])['order']

	if layout_editor_table:
		layout_editor_table.destroy()
	# else: why would this not be true?

	layout_table_data = new_row_order
	create_layout_editor()


def change_row_width(tauri_message):
	global layout_table_data
	meta_data = json.loads(dict(tauri_message)['payload'],parse_int=deeply_utils.is_nan)
	layout_table_data = meta_data['table_data']  # update the global

	if layout_editor_table:
		rows = layout_editor_table.getRows()
		for row in rows:
			if row.getData()['name'] == meta_data['key']:
				row.update({'width': meta_data['new_width']})
				break
	# else: why would this not be true?


def update_layout_columns(tauri_message):
	global layout_table_data, layout_editor_table
	layout_table_data = json.loads(dict(tauri_message)['payload'])['header_data']  # update the global

	if layout_editor_table:
		layout_editor_table.destroy()
		create_layout_editor()
	# else: why would this not be true?


def custom_editor(cell, onRendered, success, cancel, EditorParams):
	# callback function for tabulator
	# onRendered, success & cancel are callbacks that it provides
	input = document.createElement('input')
	input.setAttribute('min', 0)
	input.setAttribute('step', 1)
	input.type='number'
	input.value = cell.getValue();

	onRendered(lambda: input.focus())

	# -----
	def onChange(e=None):
		if input.value != cell.getValue():
			success(input.value)
		else:
			cancel()

	def key_down(e):
		# refactor TBD: ideally share code with regular table
		e.stopImmediatePropagation()

		if e.code in ('NumpadEnter', 'Escape'):
			# TBD: ESC to cancel (if it's not too hard to store the 'previous' value)
			# no difference with or without enter key
			input.blur()

		elif e.shiftKey and e.key in ('Enter', 'Tab'):  # macOS return
			layout_editor_table.navigateUp()

		elif e.key in ('Enter', 'Tab'):  # macOS return
			layout_editor_table.navigateDown()
	# -----

	input.bind('blur', onChange)
	input.bind('keydown', key_down)

	return input


# event listeners from main table
current_window.listen('column_order_changed', lambda payload: sync_deb_row_order(payload))
current_window.listen('update_layout_row_width', lambda payload: sync_deb_row_width(payload))
current_window.listen('change_layout_columns', lambda payload: sync_deb_layout_columns(payload))

current_window.listen('tauri://close-requested', lambda *_args: aio.run(tauri.window.getCurrentWindow().destroy()))

sync_deb_row_order = table_utils.debounce(lambda payload: change_row_order(payload), 1000)
sync_deb_row_width = table_utils.debounce(lambda payload: change_row_width(payload), 1000)
sync_deb_layout_columns = table_utils.debounce(lambda payload: update_layout_columns(payload), 1000)

create_layout_editor()
