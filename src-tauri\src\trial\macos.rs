use super::TrialInfo;
use crate::{
	config,
	trial::{decrypt, encrypt, get_app_hwid},
};
use anyhow::{bail, Result};
use chrono::{DateTime, Utc};
use std::path::PathBuf;

fn get_paths(app: &tauri::AppHandle) -> Result<(PathBuf, PathBuf)> {
	let hwid = get_app_hwid(app)?;
	let filename_a = format!(".{}", &hwid[0..9]);
	let filename_b = format!(".{}", &hwid[9..18]);

	let username = whoami::username();
    let path_a = PathBuf::from(format!(
        "/Users/<USER>/Library/Application Support/{}",
        username, filename_a
    ));
    let path_b = PathBuf::from(format!(
        "/Users/<USER>/Library/Preferences/{}",
        username, filename_b
    ));
	Ok((path_a, path_b))
}

pub fn get_info(app: &tauri::AppHandle) -> Result<Option<TrialInfo>> {
	let (path_a, path_b) = get_paths(app)?;

	let paths = vec![path_a.clone(), path_b.clone()];
	if cfg!(debug_assertions) {
		println!(
			"\n - to clear invisible: rm -f \"{}\" \"{}\" \n",
			path_a.display(),
			path_b.display()
		);
	}

	let mut count_exists = 0;
	for path in paths.clone() {
		if path.exists() {
			count_exists += 1;
		}
	}

	if count_exists == 0 {
		// time to create new one!
		return Ok(None);
	}

	if count_exists == paths.len() {
		// all exists, let's read the info

		match *config::TRIAL_INFO_ENCRYPTION {
			true => {
				let data = std::fs::read_to_string(path_a)?;
				let data = decrypt(&data)?;
				let info: TrialInfo = serde_json::from_str(&data)?;
				return Ok(Some(info));
			}
			false => {
				let data = std::fs::read_to_string(path_a)?;
				let info: TrialInfo = serde_json::from_str(&data)?;
				return Ok(Some(info));
			}
		}
	}

	// something bad with trial files.
	bail!("one of the trial date files missing");
}

pub fn activate(app: &tauri::AppHandle) -> Result<TrialInfo> {
	let (path_a, path_b) = get_paths(app)?;

	let utc: DateTime<Utc> = Utc::now();
	let utc_date_string = utc.format("%Y-%m-%d %H:%MZ").to_string();
	let mut info = TrialInfo {
		active_date: utc_date_string,
		last_popup_date: None,
	};
	let paths = vec![path_a, path_b];
	log::debug!("trial paths: {paths:?}");
	let mut count_exists = 0;
	for path in paths.clone() {
		if path.exists() {
			count_exists += 1;
		}
	}
	// Check if one of the files exists already
	if count_exists > 0 {
		bail!("Trial already activated!");
	}
	info.write(app)?;
	Ok(info)
}

pub fn update_popup_date(app: &tauri::AppHandle) -> Result<()> {
	let info = get_info(app);
	if let Ok(Some(mut info)) = info {
		let utc: DateTime<Utc> = Utc::now();
		let utc_date_string = utc.format("%Y-%m-%d %H:%MZ").to_string();
		info.last_popup_date = Some(utc_date_string);
		info.write(app)?;
	}
	Ok(())
}

impl TrialInfo {
	fn write(&mut self, app: &tauri::AppHandle) -> Result<()> {
		let (path_a, path_b) = get_paths(app)?;
		for path in [path_a, path_b] {
			match *config::TRIAL_INFO_ENCRYPTION {
				true => {
					let content = serde_json::to_string_pretty(&self)?;
					let enc = encrypt(&content);
					std::fs::write(path, enc)?;
				}
				false => {
					std::fs::write(path, serde_json::to_string_pretty(&self)?)?;
				}
			}
		}

		Ok(())
	}
}
