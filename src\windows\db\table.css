.spinner-container {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.spinner {
	display: none;
}
.spinner.active {
	display: block;
	width: 30px;
	height: 30px;
	border-radius: 50%;
	border: 4px solid;
	border-color: #dbdcef;
	border-right-color: var(--deep_blue);
	animation: spinner-d3wgkg 1s infinite linear;
}

@keyframes spinner-d3wgkg {
	to {
		transform: rotate(1turn);
	}
}
