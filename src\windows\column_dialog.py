﻿"""
column_dialog.py - 'Define' fields / columns: customize the friendly label, add fields, delete fields

TBD: restore 'type' and apply to Tabulator to fix numeric search e.g. >999
"""

from browser import aio, document, window
import javascript
import json
import math
import tauri

# local imports
import deeply_utils
import shortcuts  # runs the bind commands on import
import table_utils

# -----
column_editor_table = None
newly_added_columns = {}

current_window = tauri.window.getCurrentWindow()

# parse the parent window label out of this dialog's label
main_window_label = current_window.label[:current_window.label.rindex('_column_editor')]

# documentation TBD: summarize or show an example
header_data = json.loads(window.__PROPS__['header_data'], parse_int=deeply_utils.is_nan)['header_data']


#........................update num of changes......................
def update_num_of_changes_in_column_editor(is_save=False, column_editor_rows = []):
	if True:#try:
		global header_data, newly_added_columns
		rows  = column_editor_table.getRows()
		no_of_changes = 0

		deleting_fields = []
		for row in rows:
			name = row.getData()['name']
			# data_type = row.getData()['type']
			label = row.getElement().querySelector('input').value
			is_deleted = False
			if row.getElement().querySelector('button'):
				is_deleted = row.getElement().querySelector('button').text == 'Undo'

			prev_data = next((item for item in header_data if item['name'] == name), None)
			if prev_data:
				if prev_data['label'] != label or is_deleted:  ### or prev_data['type'] != data_type:
					no_of_changes += 1

					if is_save:
						prev_data['label'] = label
						# prev_data['type'] = data_type

						if is_deleted:
							deleting_fields.append(name)
			# New column added
			else:
				if not (name in newly_added_columns.keys() and is_deleted):
					no_of_changes += 1

					if is_save:
						header_data.append({
							'visible': True,
							'width': '150',
							'name': name,
							'label': label,
							# 'type': data_type,
						})

		header_data = [item for item in header_data if item['name'] not in deleting_fields]

		# Store existing sorters
		if is_save:
			newly_added_columns = {}
			args = {
				'columns': javascript.JSON.stringify({'columns': header_data}),
				'label': main_window_label,
				}
			tauri.invoke('update_table_columns', args)  # emit change_table_columns

		if no_of_changes == 0:
			document['column-editor-confirm-btn'].setAttribute('disabled', '')
			document['column-editor-confirm-btn'].textContent = 'Save'

		else:
			document['column-editor-confirm-btn'].removeAttribute('disabled')
			document['column-editor-confirm-btn'].textContent = 'Save ' + deeply_utils.plural_phrase(no_of_changes, 'change')

# 	except Exception as e:
# 		print('column_dialog.py -- update_num_of_changes_in_column_editor:', e)

	return no_of_changes


#........................formatters................................
def dropdown_formatter(cell, formatterParams, onRendered):
	value = cell.getValue()
	cell.getElement().classList.add('type-dropdown');
	return str(value) + ' ▾'  # pseudo drop-down menu


def column_editor_table_delete_btn_formatter(cell, *args):
	delete_btn_container = document.createElement('div')
	delete_btn_container.classList.add('delete-btn-container')

	remove_btn = document.createElement('button')

	if dict(cell.getData()).get('name') in table_utils.built_in_fields:
		# cannot delete built-in fields
		return delete_btn_container

	# -----
	def on_delete(event):
		row = cell.getRow()

		if remove_btn.textContent == 'x':
			remove_btn.textContent = 'Undo'
			remove_btn.classList.add('removed')

		else:
			remove_btn.textContent = 'x'
			remove_btn.classList.remove('removed')

		update_num_of_changes_in_column_editor()
	# -----

	remove_btn.textContent = 'x'
	remove_btn.classList.add('remove-btn')
	remove_btn.onclick = lambda event: on_delete(event)

	delete_btn_container.appendChild(remove_btn)

	return delete_btn_container


def column_editor_table_label_formatter(cell, *args):
	input_field = document.createElement('input')
	input_field.type = 'text'
	input_field.classList.add('label-editor')
	input_field.value = cell.getValue()

	# -----
	def on_blur(event):
		event.stopImmediatePropagation()
		cell.getRow().getData()['label'] = input_field.value
		update_num_of_changes_in_column_editor()
	# -----

	input_field.bind('blur', on_blur)

	return input_field
#...........................end of formatters........................

#...........................create column editor table..........................
def column_editor_on_dropdown_change(cell):
	if cell:
		update_num_of_changes_in_column_editor()


def column_table_config():
	column_editor_table_columns = [
		{
			'title': 'Name',
			'field': 'name',
			'width': 120,
			'headerSort': True, 'headerSortTristate': True,
			'headerFilter': 'input',
		},
		{
			'title': 'Label',
			'field': 'label',
			'width': 200,
			'headerSort': True, 'headerSortTristate': True,
			'headerFilter': 'input',
			'formatter': column_editor_table_label_formatter
		},
		# also commented out the 'select' drop-downs in column_dialog.html
# 		{
# 			'title': 'Type',
# 			'field': 'type',
# 			'editor': 'list',
# 			'editorParams': {
# 				'values': ['(unknown)', 'text', 'integer', 'real', 'date', 'datetime', 'json', 'binary'],
# 			},
# 			'formatter': dropdown_formatter,
# 			'width': 120,
# 			'headerFilter': 'input',
# 			'headerSort': True, 'headerSortTristate': True,
# 		},
		{
			# 'title': '',
			'field': 'delete_btn',
			'formatter': column_editor_table_delete_btn_formatter,
			'width': 90,
			'headerSort': False
		},
	]

	column_editor_data = []
	for col in header_data:
		col_dict = dict(col)

		if col_dict.get('name', '') in table_utils.built_in_fields:
			# for now we always show the name
			# ==> editing the label in Define dialog has no effect
			# ==> no need to include!
			# FUTURE to consider: details icon & checkbox don't have a label so even if we allow a new label for the others then still no point in including these
			continue

		column_editor_data.append({
			'name': col_dict.get('name', ''),
			'label': col_dict.get('label', ''),
		})

	column_table_config = {
			'height': 'auto',
			'rowHeight': table_utils.layout_define_row_height,
			'data': column_editor_data,
			'layout': 'fitColumns',
			'columns': column_editor_table_columns,
			'editorParams': {'elementAttributes': {'spellcheck': False}},
		}

	return column_table_config


def create_column_editor():
	global column_editor_table

	column_editor_table = window.Tabulator.new(
		'#column-table',
		column_table_config(),
		)

	column_editor_table.on('cellEdited', column_editor_on_dropdown_change)

#..................................end of create column editor table

#....................................event handlers and listeners....................................
def column_editor_add_column_name_blur(event):
	event.stopImmediatePropagation()
	name_input = document['add-column-name'].value
	document['add-column-label'].value = table_utils.create_label(name_input)

	if name_input:
		document['add-column-add-btn'].removeAttribute('disabled')

	else:
		document['add-column-add-btn'].setAttribute('disabled', '')


def column_editor_add_column(event):
	global newly_added_columns
	name = document['add-column-name'].value
	label = document['add-column-label'].value

	if False:
		data_type = document['add-column-type'].value  # v1: not in use
	else:
		data_type = ''

	newly_added_columns[name] = {
		'label': label,
		# 'type': data_type,  # v1: not in use
		}

	column_editor_table.addRow({
		'name': name,
		'label': label,
		# 'type': data_type,  # v1: not in use
		})

	document['add-column-name'].value = ''
	document['add-column-label'].value = ''

	if False:
		document['add-column-type'].value = '(unknown)'  # v1: not in use

	document['add-column-add-btn'].setAttribute('disabled', '')

	update_num_of_changes_in_column_editor()
	column_editor_on_dropdown_change(None)


def open_column_editor_close_window(event):
	no_of_changes = update_num_of_changes_in_column_editor()

	if no_of_changes == 0:
		close_column_editor(None)
	else:
		document["column-editor-dialog-save-btn"].textContent = 'Save ' + deeply_utils.plural_phrase(no_of_changes, 'change')
		document['column-editor-dialog'].style.display = 'flex'


def save_column_editor(_):
	update_num_of_changes_in_column_editor(is_save=True)
	close_column_editor(None)


def close_column_editor(evt=None):
	global newly_added_columns
	document['column-editor-dialog-save-btn'].textContent = 'Save'
	document['column-editor-confirm-btn'].textContent = 'Save'
	newly_added_columns = {}

	aio.run(table_utils.close_modal_window(main_window_label))


document['add-column-name'].bind('blur', column_editor_add_column_name_blur)
document['add-column-add-btn'].bind('click', column_editor_add_column)

document['column-editor-cancel-btn'].bind('mousedown', open_column_editor_close_window)

document['column-editor-confirm-btn'].bind('mousedown', lambda e: (e.stopImmediatePropagation(), save_column_editor(e)))
document['column-editor-dialog-save-btn'].bind('mousedown',lambda e: (e.stopImmediatePropagation(), save_column_editor(e)))
document['column-editor-dialog-discard-btn'].bind('mousedown', close_column_editor)

current_window.listen('tauri://close-requested', lambda _: open_column_editor_close_window(None))
#.................................. end of event handlers and listeners..................................

aio.run(table_utils.disable_main_windows(main_window_label))  # mimic a modal dialog

create_column_editor()  # no app_config so this should suffice
